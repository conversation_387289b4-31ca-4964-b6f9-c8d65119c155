﻿using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.InitializePayment
{
    public record InitializePaymentCommand(
        decimal? Amount,
        string? CurrencyCode,
        string? CurrencyDesc,
        int? DecimalPrecision,
        string? PolicyId,
        string? InvoiceNumber,
        string? PayorId,
        PaymentProvider? PaymentProvider,
        JsonElement? DynamicFields,
        string? InitializationToken
    ) : ICommand<ProcessInitialPaymentResultDto>;
}
