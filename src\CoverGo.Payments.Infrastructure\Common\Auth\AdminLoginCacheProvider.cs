﻿using CoverGo.Multitenancy;
using CoverGo.Proxies.Auth;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Common.Auth;

//TODO: CH-21596 Remove this class when the admin login is no longer used in the codebase.
internal sealed class AdminLoginCacheProvider(
    IAuthService authService,
    IMemoryCache memoryCache,
    ILogger<AdminLoginCacheProvider> logger)
{
    private const string AdminUserName = "<EMAIL>";
    private const string CacheKeyPrefix = "JwtMessageForwarder_AdminLogin_";
    private const int CacheLifetimeInMinutes = 1440;

    /// <summary>
    /// Retrieves the Admin Login (ID, etc.) for the specified tenant from cache or fetches from the AuthService.
    /// </summary>
    /// <param name="tenantId">Tenant ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>The Admin Login if found; otherwise, null.</returns>
    public async Task<Login?> GetAdminLoginAsync(TenantId tenantId, CancellationToken cancellationToken = default)
    {
        try
        {
            string cacheKey = $"{CacheKeyPrefix}{tenantId.Value}";
            if (memoryCache.TryGetValue(cacheKey, out Login? cachedLogin))
            {
                logger.LogInformation(
                    "Cache hit for tenant {TenantId}. Using cached UserId: {UserId}",
                    tenantId, cachedLogin?.Id);
                return cachedLogin;
            }

            logger.LogInformation("Cache miss for tenant {TenantId}. Fetching login from authService.", tenantId);
            Login? login = await authService.GetLoginByNameAsync(tenantId.Value, AdminUserName, cancellationToken);
            if (login != null)
            {
                memoryCache.Set(cacheKey, login, TimeSpan.FromMinutes(CacheLifetimeInMinutes));
                logger.LogInformation(
                    "Login retrieved and cached for tenant {TenantId} with UserId: {UserId}",
                    tenantId, login.Id);
            }
            else
            {
                logger.LogWarning(
                    "No login found for tenant {TenantId} with username {UserName}",
                    tenantId, AdminUserName);
            }
            return login;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Failed to retrieve Admin Login for tenant {TenantId}", tenantId);
            return null;
        }
    }
}