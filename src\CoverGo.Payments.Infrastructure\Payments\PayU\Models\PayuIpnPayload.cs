﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class PayuIpnPayload
{
    [JsonProperty(PayUContainer.PropsName.OrderData, NullValueHandling = NullValueHandling.Ignore)]
    public OrderData OrderData { get; set; }

    [JsonProperty(PayUContainer.PropsName.PaymentResult, NullValueHandling = NullValueHandling.Ignore)]
    public PaymentResult PaymentResult { get; set; }

    [JsonProperty(PayUContainer.PropsName.Client, NullValueHandling = NullValueHandling.Ignore)]
    public Client Client { get; set; }

    [JsonProperty(PayUContainer.PropsName.Products, NullValueHandling = NullValueHandling.Ignore)]
    public List<Product> Products { get; set; }

    [JsonProperty(PayUContainer.PropsName.DateTime, NullValueHandling = NullValueHandling.Ignore)]
    public string DateTime { get; set; }

    [JsonProperty(PayUContainer.PropsName.Authorization, NullValueHandling = NullValueHandling.Ignore)]
    public AuthorizationResource Authorization { get; set; }

    [JsonProperty(PayUContainer.PropsName.Refund, NullValueHandling = NullValueHandling.Ignore)]
    public RefundResponse Refund { get; set; }

    [JsonProperty(PayUContainer.PropsName.Capture, NullValueHandling = NullValueHandling.Ignore)]
    public CaptureResponse Capture { get; set; }

    [JsonProperty(PayUContainer.PropsName.AdditionalDetails, NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, string> AdditionalDetails { get; set; }
}

public class OrderData
{
    [JsonProperty(PayUContainer.PropsName.OrderDate, NullValueHandling = NullValueHandling.Ignore)]
    public string OrderDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.PayuPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public string PayuPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
    public string Status { get; set; }

    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount, NullValueHandling = NullValueHandling.Ignore)]
    public string Amount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Commission, NullValueHandling = NullValueHandling.Ignore)]
    public string Commission { get; set; }

    [JsonProperty(PayUContainer.PropsName.RefundRequestId, NullValueHandling = NullValueHandling.Ignore)]
    public string RefundRequestId { get; set; }
}