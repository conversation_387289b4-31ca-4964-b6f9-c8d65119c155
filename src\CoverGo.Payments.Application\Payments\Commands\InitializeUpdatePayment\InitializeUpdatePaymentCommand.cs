﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment
{
    public record InitializeUpdatePaymentCommand(
        string CurrencyDesc,
        string PolicyId,
        string PayorId,
        PaymentProvider PaymentProvider
    ) : ICommand<ProcessInitialPaymentResultDto>;
}
