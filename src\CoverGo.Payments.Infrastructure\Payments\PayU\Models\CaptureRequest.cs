﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models
{
    public class CaptureRequest
    {
        [JsonProperty(PayUContainer.PropsName.PayuPaymentReference)]
        public string PayuPaymentReference { get; set; }

        [JsonProperty(PayUContainer.PropsName.OriginalAmount)]
        public decimal OriginalAmount { get; set; }

        [JsonProperty(PayUContainer.PropsName.Amount)]
        public decimal Amount { get; set; }

        [JsonProperty(PayUContainer.PropsName.Currency)]
        public string Currency { get; set; }

        [JsonProperty(PayUContainer.PropsName.InstallmentsAmount, NullValueHandling = NullValueHandling.Ignore)]
        public decimal? InstallmentsAmount { get; set; }

        [JsonProperty(PayUContainer.PropsName.Products, NullValueHandling = NullValueHandling.Ignore)]
        public List<Product> Products { get; set; }

        [JsonProperty(PayUContainer.PropsName.Marketplace, NullValueHandling = NullValueHandling.Ignore)]
        public Marketplace Marketplace { get; set; }
    }
}