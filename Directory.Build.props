<Project>
    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <LangVersion>latest</LangVersion>
        <!--TODO: Commented temporarily for development phase, uncomment at the end when handling exceptions etc. -->
        <!--    <WarningsAsErrors>$(WarningsAsErrors);CA2016;Nullable</WarningsAsErrors>-->
    </PropertyGroup>

    <PropertyGroup>
        <PublishRepositoryUrl>true</PublishRepositoryUrl>
        <RepositoryUrl>https://github.com/CoverGo/Payments</RepositoryUrl>
        <PackageProjectUrl>https://github.com/CoverGo/Payments</PackageProjectUrl>
        <Company>CoverGo</Company>
        <Copyright>CoverGo</Copyright>
    </PropertyGroup>

    <PropertyGroup Condition="'$(CI_BUILD)' == 'true'">
        <ContinuousIntegrationBuild>true</ContinuousIntegrationBuild>
    </PropertyGroup>

    <ItemGroup Condition="'$(IsTestProject)' == 'true'">
        <PackageReference Include="Microsoft.NET.Test.Sdk"/>
        <PackageReference Include="FluentAssertions"/>
        <PackageReference Include="Moq"/>
        <PackageReference Include="JunitXml.TestLogger"/>
        <PackageReference Include="xunit"/>
        <PackageReference Include="xunit.runner.visualstudio">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="coverlet.collector">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
    </ItemGroup>
</Project>
