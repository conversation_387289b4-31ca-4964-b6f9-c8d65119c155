﻿using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Infrastructure.DataAccess;
using FluentAssertions;
using MongoDB.Bson;
using MongoDB.Bson.IO;
using MongoDB.Bson.Serialization;
using Moq;
using System.Text.Json;

namespace CoverGo.Payments.UnitTests.Encryption
{
    public class EncryptedFieldSerializerTests
    {
        private readonly Mock<IEncryptionService> _encryptionServiceMock;
        private readonly EncryptedFieldSerializer<TestObject> _serializer;

        public EncryptedFieldSerializerTests()
        {
            _encryptionServiceMock = new Mock<IEncryptionService>();
            _serializer = new EncryptedFieldSerializer<TestObject>(_encryptionServiceMock.Object);
        }

        [Fact]
        public void Given_NullEncryptionService_When_Constructing_Then_ThrowsArgumentNullException()
        {
            Action act = () => new EncryptedFieldSerializer<TestObject>(null!);
            act.Should().Throw<ArgumentNullException>();
        }

        [Fact]
        public void Given_NullValue_When_Serializing_Then_WritesNull()
        {
            // Arrange
            var document = new BsonDocument();
            using var writer = new BsonDocumentWriter(document);
            var context = BsonSerializationContext.CreateRoot(writer);

            writer.WriteStartDocument();
            writer.WriteName("Field");

            // Act
            _serializer.Serialize(context, default, null!);

            writer.WriteEndDocument();

            // Assert
            document["Field"].Should().Be(BsonNull.Value);
        }

        [Fact]
        public void Given_StringValue_When_Serializing_Then_EncryptsAndWritesString()
        {
            // Arrange
            var document = new BsonDocument();
            using var writer = new BsonDocumentWriter(document);
            var context = BsonSerializationContext.CreateRoot(writer);

            writer.WriteStartDocument();
            writer.WriteName("Field");

            var testObject = new TestObject { TestProperty = "test_value" };
            var serializedJson = JsonSerializer.Serialize(testObject);
            var encryptedString = "encrypted";

            _encryptionServiceMock.Setup(s => s.Encrypt(serializedJson)).Returns(encryptedString);

            // Act
            _serializer.Serialize(context, default, testObject);

            writer.WriteEndDocument();

            // Assert
            _encryptionServiceMock.Verify(s => s.Encrypt(serializedJson), Times.Once);
            document["Field"].AsString.Should().Be(encryptedString);
        }

        [Fact]
        public void Given_EncryptedString_When_Deserializing_Then_DecryptsAndReturnsObject()
        {
            // Arrange
            var encryptedString = "encrypted";
            var decryptedJson = "{\"TestProperty\":\"test_value\"}";
            _encryptionServiceMock.Setup(s => s.Decrypt(encryptedString)).Returns(decryptedJson);

            var document = new BsonDocument { { "Field", encryptedString } };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            reader.ReadStartDocument();
            reader.ReadName("Field");

            // Act
            var result = _serializer.Deserialize(context, default);

            // Assert
            result.Should().NotBeNull();
            result.TestProperty.Should().Be("test_value");
            _encryptionServiceMock.Verify(s => s.Decrypt(encryptedString), Times.Once);
        }

        [Fact]
        public void Given_UnsupportedBsonType_When_Deserializing_Then_ThrowsNotSupportedException()
        {
            // Arrange
            var document = new BsonDocument { { "Field", 123 } };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            reader.ReadStartDocument();
            reader.ReadName("Field");

            // Act
            Action act = () => _serializer.Deserialize(context, default);

            // Assert
            act.Should().Throw<NotSupportedException>()
               .WithMessage("BSON type Int32 is not supported for decryption.");
        }

        [Fact]
        public void Given_NullBsonType_When_Deserializing_Then_ReturnsDefault()
        {
            // Arrange
            var document = new BsonDocument
            {
                { "Field", BsonNull.Value }
            };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            // Act
            reader.ReadStartDocument();
            reader.ReadName("Field");
            var result = _serializer.Deserialize(context, default);

            // Assert
            result.Should().BeNull();
        }

        private class TestObject
        {
            public string? TestProperty { get; set; }
        }
    }
}
