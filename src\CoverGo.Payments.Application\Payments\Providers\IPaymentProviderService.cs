﻿using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using PaymentAggregate = CoverGo.Payments.Domain.Payment.PaymentAggregate;

namespace CoverGo.Payments.Application.Payments.Providers;

public interface IPaymentProviderService
{
    PaymentProvider Type { get; }

    Task<PreauthPaymentAggregate> PreparePaymentAsync(PreauthPaymentAggregate preauthPayment,
        CancellationToken cancellationToken = default);

    Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default);

    Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment, CancellationToken cancellationToken = default);

    Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment, string providerPaymentId,
        CancellationToken cancellationToken = default);

    Task<RecurringPaymentAggregate> RecurringPaymentAsync(RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default);

    Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default);

    Task FinalizePaymentAsync(PreauthPaymentAggregate payment, PreauthPaymentAggregate? prevPayment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default);

    Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default);

    Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference, PaymentStatus paymentStatus, decimal?
        amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(
        string webhookBody,
        PspSettingsAggregate pspSettings, CancellationToken cancellationToken = default);

    string ExtractRequestOrigin(string dynamicFieldsJson);
}