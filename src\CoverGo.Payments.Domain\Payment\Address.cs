﻿using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;

namespace CoverGo.Payments.Domain.Payment;

public class Address : ValueObject
{
    public Address() { }

    public Address(
        string? addressLine1,
        string? addressLine2,
        string? street,
        string? houseNumber,
        string? postalCode,
        string? city,
        string? state,
        string? country)
    {
        AddressLine1 = addressLine1;
        AddressLine2 = addressLine2;
        Street = street;
        HouseNumber = houseNumber;
        PostalCode = postalCode;
        City = city;
        State = state;
        Country = country;
    }

    public string? AddressLine1 { get; private set; }

    public string? AddressLine2 { get; private set; }

    public string? Street { get; private set; }

    public string? HouseNumber { get; private set; }

    public string? PostalCode { get; private set; }

    public string? City { get; private set; }

    public string? State { get; private set; }

    public string? Country { get; private set; }

    public override string ToString() => $"{Street} {HouseNumber}, {City}, {Country}";

    protected override IEnumerable<object> GetEqualityComponents()
    {
        if (AddressLine1 != null) yield return AddressLine1;
        if (AddressLine2 != null) yield return AddressLine2;
        if (Street != null) yield return Street;
        if (PostalCode != null) yield return PostalCode;
        if (City != null) yield return City;
        if (State != null) yield return State;
        if (Country != null) yield return Country;
        if (HouseNumber != null) yield return HouseNumber;
    }
}