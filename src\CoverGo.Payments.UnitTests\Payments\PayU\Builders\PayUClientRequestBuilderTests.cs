﻿using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Infrastructure.Payments.PayU.Builders;
using FluentAssertions;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments.PayU.Builders;

public class PayUClientRequestBuilderTests
{
    [Theory]
    [InlineData("POST")]
    [InlineData("GET")]
    public void BuildRequestMessage_ShouldCorrectlyInitializeHeaders_WithGivenMethod(string methodType)
    {
        // Arrange
        var method = new HttpMethod(methodType);
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(DateTime.UtcNow);

        // Act
        HttpRequestMessage result =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);

        // Assert
        result.Headers.Contains("X-Header-Merchant").Should().BeTrue();
        result.Headers.Contains("X-Header-Date").Should().BeTrue();
        result.Headers.Contains("X-Header-Signature").Should().BeTrue();
        result.Headers.Contains("X-Header-Idempotency-Key").Should().BeTrue();

        result.Headers.GetValues("X-Header-Merchant").Should().ContainSingle().Which.Should().Be(merchantCode);
        result.Method.Should().Be(method);
        result.RequestUri.Should().Be(testUri);
    }

    [Fact]
    public void BuildRequestMessage_WithNullContent_ShouldNotAddContentToRequest()
    {
        // Arrange
        HttpMethod method = HttpMethod.Get;
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(DateTime.UtcNow);

        // Act
        HttpRequestMessage result =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, null,
                dateTimeProvider.Object);

        // Assert
        result.Content.Should().BeNull();
    }

    [Fact]
    public void BuildRequestMessage_ShouldCalculateCorrectSignature()
    {
        // Arrange
        HttpMethod method = HttpMethod.Post;
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(new DateTime());
        const string expectedSignature = "384ed4df6396f9363766bddcb72924b4f6b5e8f2cbfaca08ac79a2977e7ec32d";

        // Act
        HttpRequestMessage result =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);

        // Assert
        result.Headers.GetValues("X-Header-Signature").Should().ContainSingle().Which.Should().Be(expectedSignature);
    }

    [Fact]
    public void BuildRequestMessage_ShouldGenerateUniqueIdempotencyKeyForEachRequest()
    {
        // Arrange
        HttpMethod method = HttpMethod.Post;
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();

        // Act
        HttpRequestMessage result1 =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);
        HttpRequestMessage result2 =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);

        // Assert
        string key1 = result1.Headers.GetValues("X-Header-Idempotency-Key").Single();
        string key2 = result2.Headers.GetValues("X-Header-Idempotency-Key").Single();
        key1.Should().NotBe(key2);
    }

    [Theory]
    [InlineData("POST")]
    [InlineData("PUT")]
    [InlineData("DELETE")]
    public void BuildRequestMessage_ShouldSetCorrectHttpMethod(string methodType)
    {
        // Arrange
        var method = new HttpMethod(methodType);
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(DateTime.UtcNow);

        // Act
        HttpRequestMessage result =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);

        // Assert
        result.Method.Should().Be(method);
    }

    [Fact]
    public void BuildRequestMessage_ShouldCorrectlyHandleQueryString()
    {
        // Arrange
        HttpMethod method = HttpMethod.Get;
        var testUri = new Uri("https://api.test.com/payments?query=123");
        const string merchantCode = "merchant123";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(DateTime.UtcNow);

        // Act
        HttpRequestMessage result =
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);

        // Assert
        string? queryString = result.RequestUri?.Query;
        queryString.Should().Contain("query=123");
    }

    [Fact]
    public void BuildRequestMessage_WithInvalidMerchantCode_ShouldThrowArgumentException()
    {
        // Arrange
        HttpMethod method = HttpMethod.Post;
        var testUri = new Uri("https://api.test.com/payments");
        const string merchantCode = "";
        const string secretKey = "secretKey";
        object content = new { amount = 100 };
        var dateTimeProvider = new Mock<IDateTimeProvider>();
        dateTimeProvider.Setup(dtp => dtp.NowUtc()).Returns(DateTime.UtcNow);

        // Act & Assert
        Action act = () =>
            PayUClientRequestBuilder.BuildRequestMessage(testUri, method, merchantCode, secretKey, content,
                dateTimeProvider.Object);
        act.Should().Throw<ArgumentException>();
    }
}