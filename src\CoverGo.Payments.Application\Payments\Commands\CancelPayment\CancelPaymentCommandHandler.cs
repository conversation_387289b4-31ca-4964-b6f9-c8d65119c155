﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPayment
{
    public class CancelPaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<CancelPaymentCommandHandler> logger)
        : ICommandHandler<CancelPaymentCommand, PaymentDto>
    {
        /// <param name="cancelPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(CancelPaymentCommand cancelPaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("CancelPaymentCommandHandler.Handle: Starting payment cancellation. PaymentId: {PaymentId}, CancellationStatus: {CancellationStatus}, Reason: {CancellationReason}",
                cancelPaymentCommand.PaymentId,
                cancelPaymentCommand.CancellationStatus?.ToString() ?? "Canceled",
                cancelPaymentCommand.CancellationReason ?? "No reason provided");

            try
            {
                PaymentAggregate payment = await paymentService.CancelPreauthPaymentAsync(
                    cancelPaymentCommand.PaymentId, 
                    cancelPaymentCommand.CancellationStatus ?? PaymentStatus.Canceled,
                    cancelPaymentCommand.CancellationReason,
                    cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("CancelPaymentCommandHandler.Handle: Payment cancellation completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "CancelPaymentCommandHandler.Handle: Unexpected error during payment cancellation. PaymentId: {PaymentId}",
                    cancelPaymentCommand.PaymentId);
                throw;
            }
        }
    }
}
