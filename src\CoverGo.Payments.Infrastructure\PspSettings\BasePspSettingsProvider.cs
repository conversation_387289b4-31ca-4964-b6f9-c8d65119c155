﻿using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.PspSettings.Providers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;

namespace CoverGo.Payments.Infrastructure.PspSettings;

public abstract class BasePspSettingsProvider(IRepository<PspSettingsAggregate, string> pspSettingsRepository) : IPspSettingsProvider
{
    public abstract PaymentProvider Type { get; }
    
    public async Task<PspSettingsAggregate?> GetPspSettingsAsync(CancellationToken cancellationToken)
        => await GetPspSettingsAsync(Type, cancellationToken);
    
    private async Task<PspSettingsAggregate?> GetPspSettingsAsync(PaymentProvider paymentProvider, CancellationToken cancellationToken) 
        => (await pspSettingsRepository.FindAllByAsync(psp 
                    => psp.PaymentProvider == paymentProvider, cancellationToken)
            ).FirstOrDefault();
}