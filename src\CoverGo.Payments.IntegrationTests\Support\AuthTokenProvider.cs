using CoverGo.Payments.Tests.GatewayClient;
using StrawberryShake;

namespace CoverGo.Payments.IntegrationTests.Support;

public class AuthTokenProvider(ITestGatewayClient gatewayClient)
{
    public readonly string TenantId = "covergo";
    private string? _token;

    public async Task<string> GetToken()
    {
        if (_token is null)
        {
            string tenantId = TenantId;
            string clientId = "admin";
            string username = "<EMAIL>";
            string password = "V9K&KobcZO3";

            IOperationResult<ITokenResult> response =
                await gatewayClient.Token.ExecuteAsync(tenantId, clientId, username, password);
            _token = response.Data!.Token!.AccessToken!;
        }

        return _token;
    }
}