﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <packageSources>
        <clear/>
        <add key="github" value="https://nuget.pkg.github.com/covergo/index.json"/>
        <add key="nuget.org" value="https://api.nuget.org/v3/index.json"/>
    </packageSources>
    <packageSourceMapping>
        <packageSource key="nuget.org">
            <package pattern="*"/>
        </packageSource>
        <packageSource key="github">
            <package pattern="CoverGo.*"/>
        </packageSource>
    </packageSourceMapping>
</configuration>