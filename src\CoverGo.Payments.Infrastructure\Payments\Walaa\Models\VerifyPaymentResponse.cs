﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Walaa.Models;

public class VerifyPaymentResponse
{
    [JsonProperty("code")]
    public int? Code { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }

    [JsonProperty("data")]
    public VerifyPaymentResponseData? Data { get; set; }

    public bool IsSuccess => Code == 1 && Message == "Success" && (Data?.Status == "PAID" || Data?.Status == "Success");
}

public class VerifyPaymentResponseData
{
    [JsonProperty("invoiceId")]
    public string? InvoiceId { get; set; }

    [JsonProperty("amount")]
    public string? Amount { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }
}
