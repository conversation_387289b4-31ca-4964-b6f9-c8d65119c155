﻿using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments;

namespace CoverGo.Payments.UnitTests.Payments;

using System;
using System.Text.Json;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

public class BasePaymentProviderServiceTests
{
    private readonly TestPaymentProviderService _service;

    public BasePaymentProviderServiceTests()
    {
        Mock<ILogger<BasePaymentProviderService>> loggerMock = new();
        _service = new TestPaymentProviderService(loggerMock.Object);
    }

    [Theory]
    [InlineData("{\"FirstName\":\"John\",\"LastName\":\"Doe\",\"Email\":\"<EMAIL>\"}", "John", "Doe",
        "<EMAIL>")]
    [InlineData(
        "\"{\\\"FirstName\\\":\\\"Jane\\\",\\\"LastName\\\":\\\"Smith\\\",\\\"Email\\\":\\\"<EMAIL>\\\"}\"",
        "Jane", "Smith", "<EMAIL>")]
    public void ParseDynamicFields_ShouldReturnCorrectData(string dynamicFieldsJson, string expectedFirstName,
        string expectedLastName, string expectedEmail)
    {
        // Arrange
        JsonElement dynamicFields = JsonDocument.Parse(dynamicFieldsJson).RootElement;

        // Act
        TestData? result = _service.ParseDynamicFields<TestData>(dynamicFields.GetRawText());

        // Assert
        result.Should().NotBeNull();
        result!.FirstName.Should().Be(expectedFirstName);
        result.LastName.Should().Be(expectedLastName);
        result.Email.Should().Be(expectedEmail);
    }

    [Fact]
    public void ParseDynamicFields_ShouldThrowArgumentNullException_WhenDynamicFieldsIsNull()
    {
        // Act
        Action act = () => _service.ParseDynamicFields<TestData>(null);

        // Assert
        act.Should().Throw<ArgumentException>().WithMessage("*argumentName is null or white space*");
    }

    private class TestPaymentProviderService(ILogger<BasePaymentProviderService> logger)
        : BasePaymentProviderService(logger)
    {
        public override PaymentProvider Type => throw new NotImplementedException();

        public override Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
            JsonElement? dynamicFields, CancellationToken cancellationToken = default) =>
            throw new NotImplementedException();

        public override Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        public override Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
            string providerPaymentId, CancellationToken cancellationToken = default) =>
            throw new NotImplementedException();

        public override Task<RecurringPaymentAggregate> RecurringPaymentAsync(
            RecurringPaymentAggregate recurringPayment,
            CancellationToken cancellationToken = default) =>
            throw new NotImplementedException();

        public override Task FailPaymentAsync(PaymentAggregate payment,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        public override Task FinalizePaymentAsync(PreauthPaymentAggregate payment, PreauthPaymentAggregate? prevPayment,
            JsonElement? dynamicFields,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        public override Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
            CancellationToken cancellationToken = default) => throw new NotImplementedException();

        public override
            Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference, PaymentStatus paymentStatus,
                decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)>
            HandleWebhookAsync(string webhookBody, PspSettingsAggregate pspSettings,
                CancellationToken cancellationToken = default) => throw new NotImplementedException();
    }

    private record TestData(string FirstName, string LastName, string Email);
}