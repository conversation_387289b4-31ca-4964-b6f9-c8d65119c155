﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.RecurringPayment
{
    public class RecurringPaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<RecurringPaymentCommandHandler> logger)
        : ICommandHandler<RecurringPaymentCommand, PaymentDto>
    {
        /// <param name="recurringPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(RecurringPaymentCommand recurringPaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("RecurringPaymentCommandHandler.Handle: Starting recurring payment processing. PolicyId: {PolicyId}, PayorId: {PayorId}, InvoiceNumber: {InvoiceNumber}, Amount: {Amount} {CurrencyDesc}, RenewedFromPolicyId: {RenewedFromPolicyId}",
                recurringPaymentCommand.PolicyId,
                recurringPaymentCommand.PayorId,
                recurringPaymentCommand.InvoiceNumber,
                recurringPaymentCommand.Amount,
                recurringPaymentCommand.CurrencyDesc,
                recurringPaymentCommand.RenewedFromPolicyId ?? "null");

            try
            {
                PaymentAggregate payment = await paymentService.ProcessRecurringPaymentAsync(
                    recurringPaymentCommand.Amount,
                    recurringPaymentCommand.DecimalPrecision,
                    recurringPaymentCommand.CurrencyDesc,
                    recurringPaymentCommand.PolicyId,
                    recurringPaymentCommand.InvoiceNumber,
                    recurringPaymentCommand.PayorId,
                    recurringPaymentCommand.RenewedFromPolicyId,
                    cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("RecurringPaymentCommandHandler.Handle: Recurring payment processing completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "RecurringPaymentCommandHandler.Handle: Unexpected error during recurring payment processing. PolicyId: {PolicyId}, PayorId: {PayorId}, InvoiceNumber: {InvoiceNumber}",
                    recurringPaymentCommand.PolicyId,
                    recurringPaymentCommand.PayorId,
                    recurringPaymentCommand.InvoiceNumber);
                throw;
            }
        }
    }
}