﻿namespace CoverGo.Payments.Infrastructure.Payments.PayU
{
    public static class PayUContainer
    {
        public static class PayUApiUrl
        {
            internal const string Sandbox = "https://sandbox.payu.ro";
            internal const string Production = "https://secure.payu.ro";
        }
        
        internal static class PropsName
        {
            internal const string SessionId = "sessionId";
            internal const string ExchangeRate = "exchangeRate";
            internal const string StrongCustomerAuthentication = "strongCustomerAuthentication";
            internal const string Home = "home";
            internal const string Mobile = "mobile";
            internal const string Work = "work";
            internal const string CountryPrefix = "countryPrefix";
            internal const string Subscriber = "subscriber";
            internal const string Address3 = "address3";
            internal const string StateCode = "stateCode";
            internal const string TokenHash = "tokenHash";
            internal const string Data = "data";
            internal const string Header = "header";
            internal const string Signature = "signature";
            internal const string ApplicationData = "applicationData";
            internal const string EphemeralPublicKey = "ephemeralPublicKey";
            internal const string WrappedKey = "wrappedKey";
            internal const string PublicKeyHash = "publicKeyHash";
            internal const string TransactionId = "transactionId";
            internal const string Token = "token";
            internal const string MerchantPaymentReference = "merchantPaymentReference";
            internal const string MerchantPaymentAttemptReference = "merchantPaymentAttemptReference";
            internal const string ReturnUrl = "returnUrl";
            internal const string Authorization = "authorization";
            internal const string Authorizations = "authorizations";
            internal const string Client = "client";
            internal const string Number = "number";
            internal const string ExpiryMonth = "expiryMonth";
            internal const string ExpiryYear = "expiryYear";
            internal const string Cvv = "cvv";
            internal const string Owner = "owner";
            internal const string TimeSpentTypingNumber = "timeSpentTypingNumber";
            internal const string TimeSpentTypingOwner = "timeSpentTypingOwner";
            internal const string City = "city";
            internal const string State = "state";
            internal const string CountryCode = "countryCode";
            internal const string FirstName = "firstName";
            internal const string LastName = "lastName";
            internal const string Email = "email";
            internal const string Phone = "phone";
            internal const string CompanyName = "companyName";
            internal const string TaxId = "taxId";
            internal const string AddressLine1 = "addressLine1";
            internal const string AddressLine2 = "addressLine2";
            internal const string ZipCode = "zipCode";
            internal const string IdentityDocument = "identityDocument";
            internal const string Type = "type";
            internal const string Billing = "billing";
            internal const string Delivery = "delivery";
            internal const string Ip = "ip";
            internal const string Time = "time";
            internal const string CommunicationLanguage = "communicationLanguage";
            internal const string PaymentMethod = "paymentMethod";
            internal const string CardDetails = "cardDetails";
            internal const string Credit = "credit";
            internal const string MerchantToken = "merchantToken";
            internal const string ApplePayToken = "applePayToken";
            internal const string GooglePayToken = "googlePayToken";
            internal const string OneTimeUseToken = "oneTimeUseToken";
            internal const string UsePaymentPage = "usePaymentPage";
            internal const string InstallmentsNumber = "installmentsNumber";
            internal const string UseLoyaltyPoints = "useLoyaltyPoints";
            internal const string LoyaltyPointsAmount = "loyaltyPointsAmount";
            internal const string CampaignType = "campaignType";
            internal const string Fx = "fx";
            internal const string PaymentPageOptions = "paymentPageOptions";
            internal const string OrderTimeout = "orderTimeout";
            internal const string Limits = "limits";
            internal const string CustomerUUID = "customerUUID";
            internal const string CustomerMasterUUID = "customerMasterUUID";
            internal const string Scoring = "scoring";
            internal const string Cnp = "cnp";
            internal const string SourceOfIncome = "sourceOfIncome";
            internal const string UncensoredScoring = "uncensoredScoring";
            internal const string NrInstalments = "nrInstalments";
            internal const string NrDaysDueDate = "nrDaysDueDate";
            internal const string FinancialPartner = "financialPartner";
            internal const string CampaignCode = "campaignCode";
            internal const string PaymentMethodLimitAmount = "paymentMethodLimitAmount";
            internal const string Products = "products";
            internal const string Sku = "sku";
            internal const string UnitPrice = "unitPrice";
            internal const string Quantity = "quantity";
            internal const string Vat = "vat";
            internal const string Marketplace = "marketplace";
            internal const string Version = "version";
            internal const string Id = "id";
            internal const string SellerId = "sellerId";
            internal const string CommissionAmount = "commissionAmount";
            internal const string CommissionCurrency = "commissionCurrency";
            internal const string AirlineInfo = "airlineInfo";
            internal const string PassengerName = "passengerName";
            internal const string TicketNumber = "ticketNumber";
            internal const string RefundPolicy = "refundPolicy";
            internal const string ReservationSystem = "reservationSystem";
            internal const string TravelAgency = "travelAgency";
            internal const string FlightSegments = "flightSegments";
            internal const string DepartureDate = "departureDate";
            internal const string DepartureAirport = "departureAirport";
            internal const string DestinationAirport = "destinationAirport";
            internal const string AirlineCode = "airlineCode";
            internal const string AirlineName = "airlineName";
            internal const string ServiceClass = "serviceClass";
            internal const string Stopover = "stopover";
            internal const string FareCode = "fareCode";
            internal const string FlightNumber = "flightNumber";
            internal const string ThreeDSecure = "threeDSecure";
            internal const string MpiData = "mpiData";
            internal const string Eci = "eci";
            internal const string Xid = "xid";
            internal const string Cavv = "cavv";
            internal const string DsTransactionId = "dsTransactionId";
            internal const string Cardholder = "cardholder";
            internal const string CardUniqueIdentifier = "cardUniqueIdentifier";
            internal const string CardHolderName = "cardHolderName";
            internal const string ExpirationDate = "expirationDate";
            internal const string TokenStatus = "tokenStatus";
            internal const string CardExpirationDate = "cardExpirationDate";
            internal const string Contact = "contact";
            internal const string AccountInformation = "accountInformation";
            internal const string Address = "address";
            internal const string Match = "match";
            internal const string AddressFirstUsedDate = "addressFirstUsedDate";
            internal const string AddressUsageIndicator = "addressUsageIndicator";
            internal const string FraudActivity = "fraudActivity";
            internal const string CreateDate = "createDate";
            internal const string PastOrdersYear = "pastOrdersYear";
            internal const string PastOrdersDay = "pastOrdersDay";
            internal const string PurchasesLastSixMonths = "purchasesLastSixMonths";
            internal const string ChangeDate = "changeDate";
            internal const string ChangeIndicator = "changeIndicator";
            internal const string AgeIndicator = "ageIndicator";
            internal const string PasswordChangedDate = "passwordChangedDate";
            internal const string PasswordChangeIndicator = "passwordChangeIndicator";
            internal const string NameToRecipientMatch = "nameToRecipientMatch";
            internal const string AddCardAttemptsDay = "addCardAttemptsDay";
            internal const string AuthMethod = "authMethod";
            internal const string AuthDateTime = "authDateTime";
            internal const string RequestorAuthenticationData = "requestorAuthenticationData";
            internal const string CardAddedIndicator = "cardAddedIndicator";
            internal const string CardAddedDate = "cardAddedDate";
            internal const string ClientEnvironment = "clientEnvironment";
            internal const string DeviceChannel = "deviceChannel";
            internal const string Browser = "browser";
            internal const string AcceptHeader = "acceptHeader";
            internal const string RequestIp = "requestIp";
            internal const string JavaEnabled = "javaEnabled";
            internal const string Language = "language";
            internal const string ColorDepth = "colorDepth";
            internal const string ScreenHeight = "screenHeight";
            internal const string ScreenWidth = "screenWidth";
            internal const string Timezone = "timezone";
            internal const string UserAgent = "userAgent";
            internal const string Purchase = "purchase";
            internal const string Recurring = "recurring";
            internal const string FrequencyDays = "frequencyDays";
            internal const string ExpiryDate = "expiryDate";
            internal const string TransactionType = "transactionType";
            internal const string ShipIndicator = "shipIndicator";
            internal const string PreOrderIndicator = "preOrderIndicator";
            internal const string PreOrderDate = "preOrderDate";
            internal const string DeliveryTimeFrame = "deliveryTimeFrame";
            internal const string ReorderedIndicator = "reorderedIndicator";
            internal const string MerchantFunds = "merchantFunds";
            internal const string ThreeDSRequestorPreferences = "threeDSRequestorPreferences";
            internal const string Challenge = "challenge";
            internal const string Indicator = "indicator";
            internal const string Ui = "ui";
            internal const string WindowSize = "windowSize";
            internal const string StoredCredentials = "storedCredentials";
            internal const string ConsentType = "consentType";
            internal const string UseType = "useType";
            internal const string UseId = "useId";
            internal const string AdditionalDetails = "additionalDetails";
            internal const string Name = "name";
            internal const string Code = "code";
            internal const string Message = "message";
            internal const string OriginalAmount = "originalAmount";
            internal const string PayuPaymentReference = "payuPaymentReference";
            internal const string Amount = "amount";
            internal const string Currency = "currency";
            internal const string PaymentResult = "paymentResult";
            internal const string PayuResponseCode = "payuResponseCode";
            internal const string AuthCode = "authCode";
            internal const string Rrn = "rrn";
            internal const string CardProgramName = "cardProgramName";
            internal const string BankResponseDetails = "bankResponseDetails";
            internal const string Details3ds = "3dsDetails";
            internal const string WireAccounts = "wireAccounts";
            internal const string Url = "url";
            internal const string TerminalId = "terminalId";
            internal const string Response = "response";
            internal const string HostRefNum = "hostRefNum";
            internal const string MerchantId = "merchantId";
            internal const string ShortName = "shortName";
            internal const string TxRefNo = "txRefNo";
            internal const string Oid = "oid";
            internal const string TransId = "transId";
            internal const string CustomBankNode = "customBankNode";
            internal const string Status = "status";
            internal const string PaymentStatus = "paymentStatus";
            internal const string Qr = "qr";
            internal const string Pan = "pan";
            internal const string MdStatus = "mdStatus";
            internal const string ErrorMessage = "errorMessage";
            internal const string TxStatus = "txStatus";
            internal const string BankIdentifier = "bankIdentifier";
            internal const string BankAccount = "bankAccount";
            internal const string RoutingNumber = "routingNumber";
            internal const string IbanAccount = "ibanAccount";
            internal const string BankSwift = "bankSwift";
            internal const string Country = "country";
            internal const string RecipientName = "recipientName";
            internal const string RecipientVatId = "recipientVatId";
            internal const string Timestamp = "timestamp";
            internal const string Authorized = "authorized";
            internal const string ResponseCode = "responseCode";
            internal const string ResponseMessage = "responseMessage";
            internal const string CardScheme = "cardScheme";
            internal const string CardType = "cardType";
            internal const string IssuerBank = "issuerBank";
            internal const string IssuerCountryCode = "issuerCountryCode";
            internal const string CardProfile = "cardProfile";
            internal const string LastFourDigits = "lastFourDigits";
            internal const string BinNumber = "binNumber";
            internal const string InstallmentsAmount = "installmentsAmount";
            internal const string MerchantRefundReference = "merchantRefundReference";
            internal const string Loyalty = "loyalty";
            internal const string UseFastRefund = "useFastRefund";
            internal const string MarketplaceV1 = "marketplaceV1";
            internal const string Merchant = "merchant";
            internal const string StatusCode = "statusCode";
            internal const string StatusDesc = "statusDesc";
            internal const string Refund = "refund";
            internal const string RefundRequestId = "refundRequestId";
            internal const string RefundAmount = "refundAmount";
            internal const string LifetimeMinutes = "lifetimeMinutes";
            internal const string CreatedAt = "createdAt";
            internal const string Commission = "commission";
            internal const string OrderDate = "orderDate";
            internal const string Capture = "capture";
            internal const string OrderData = "orderData";
            internal const string DateTime = "dateTime";
        }
    }
}