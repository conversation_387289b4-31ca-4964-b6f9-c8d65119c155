﻿using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Infrastructure.Payments.PayU.Builders;
using CoverGo.Payments.Infrastructure.Payments.PayU.Configurations;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Payments.PayU
{
    public abstract class BaseClient
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly IDateTimeProvider _dateTimeProvider;
        protected readonly PayUClientSettings Settings;
        private readonly ILogger _logger;

        protected BaseClient(PayUClientSettings settings, IHttpClientFactory clientFactory, IDateTimeProvider dateTimeProvider, ILogger logger)
        {
            Settings = settings;
            _clientFactory = clientFactory;
            _dateTimeProvider = dateTimeProvider;
            _logger = logger;
        }

        protected async Task<T?> ProcessAsync<T>(Uri requestUrl, HttpMethod httpMethod, CancellationToken ct,
            object? content = default(HttpContent))
            where T : class
        {
            HttpRequestMessage request = PayUClientRequestBuilder.BuildRequestMessage(requestUrl, httpMethod,
                Settings.MerchantCode, Settings.SecretKey, content, _dateTimeProvider);
            var communicator = new PayUApiHttpCommunicator<T>(_clientFactory, Settings, _logger);
            return await communicator.SendAsync(request, ct);
        }
    }
}