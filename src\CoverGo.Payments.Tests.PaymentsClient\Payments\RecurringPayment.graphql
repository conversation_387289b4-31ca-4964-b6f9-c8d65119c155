mutation RecurringPayment($input: RecurringPaymentInput!) {
    recurringPayment(input: $input) {
        payment {
            id
            money {
                paymentCurrencyCode
                paymentCurrencyDesc
                paymentDecimalPrecision
                paymentAmount
            }
            paymentProvider
            internalReference
            externalReference
            paymentStatus
            auditInfo {
                createdAt
                createdBy
                lastModifiedAt
                lastModifiedBy
                deletedAt
                deletedBy
            }
            refundedAmount
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                    code
                }
            }
            ... on DomainError {
                message
                code
            }
        }
    }
}