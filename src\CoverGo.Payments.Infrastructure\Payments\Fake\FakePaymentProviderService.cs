﻿using System.Globalization;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using GuardClauses;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Payments.Fake
{
    public class FakePaymentProviderService(ILogger<FakePaymentProviderService> logger)
        : BasePaymentProviderService(logger)
    {
        private const string WebhookAcknowledgeMessage = "Yes";

        public override PaymentProvider Type => PaymentProvider.Fake;

        public override async Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
            JsonElement? dynamicFields,
            CancellationToken cancellationToken = default)
        {
            GuardClause.ArgumentIsNotNull(payment, nameof(payment));

            FakePspSettingsAggregate? pspSettings = GetPspSettings<FakePspSettingsAggregate>(payment.PspSettings);

            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            var data = new Dictionary<string, string>
            {
                { "PAYMENT_NO", payment.Id },
                { "PAYMENT_AMOUNT", payment.Money.PaymentAmount.ToString(CultureInfo.InvariantCulture) },
                { "PAYMENT_DESC", payment.GetDescription() },
                { "PAYMENT_CURRENCY", payment.Money.PaymentCurrencyDesc }
            };

            return await Task.FromResult(new RedirectUrlOutput(new Uri(pspSettings.RedirectUrl), data));
        }

        public override async Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
            CancellationToken cancellationToken = default)
        {
            payment.AddPaymentStatusHistoryItem(PaymentStatus.Canceled, payment.Money);
            payment.SetPreauthStatus(PreauthPaymentStatus.Cancelled);
            await Task.CompletedTask;
        }

        public override async Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
            string providerPaymentId,
            CancellationToken cancellationToken = default)
        {
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, capturePayment.Money);
            return await Task.FromResult(capturePayment);
        }

        public override async Task<RecurringPaymentAggregate> RecurringPaymentAsync(
            RecurringPaymentAggregate recurringPayment,
            CancellationToken cancellationToken = default)
        {
            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, recurringPayment.Money);
            return await Task.FromResult(recurringPayment);
        }

        public override async Task FailPaymentAsync(PaymentAggregate payment,
            CancellationToken cancellationToken = default)
        {
            payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, "Failed by PSP.");
            await Task.CompletedTask;
        }

        public override async Task FinalizePaymentAsync(PreauthPaymentAggregate payment,
            PreauthPaymentAggregate? prevPayment, JsonElement? dynamicFields,
            CancellationToken cancellationToken = default)
        {
            payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, payment.Money);
            await Task.CompletedTask;
        }

        public override async Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
            CancellationToken cancellationToken = default)
        {
            paymentRefund.SetStatus(RefundStatus.Succeeded);
            return await Task.FromResult(paymentRefund);
        }

        public override
            Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
                PaymentStatus paymentStatus,
                decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)>
            HandleWebhookAsync(
                string webhookBody, PspSettingsAggregate pspSettings,
                CancellationToken cancellationToken = default)
            => Task
                .FromResult<(string paymentId, string providerPaymentId, string externalReference, string
                    internalReference, PaymentStatus
                    paymentStatus, decimal? amount,
                    string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)>((
                    Guid.NewGuid().ToString(), Guid.NewGuid().ToString(),
                    Guid.NewGuid().ToString(), Guid.NewGuid().ToString(),
                    PaymentStatus.Succeeded, 0, WebhookAcknowledgeMessage, false, false));
    }
}