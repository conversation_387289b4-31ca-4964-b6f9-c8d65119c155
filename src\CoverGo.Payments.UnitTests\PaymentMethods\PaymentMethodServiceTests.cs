using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Infrastructure.PaymentMethods;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PaymentMethod;
using Microsoft.Extensions.Logging;
using Moq;

namespace CoverGo.Payments.UnitTests.PaymentMethods;

public class PaymentMethodServiceTests
{
    private readonly Mock<IRepository<PayorPaymentMethodAggregate, string>> _mockRepository;
    private readonly Mock<ILogger<PaymentMethodService>> _mockLogger;
    private readonly PaymentMethodService _service;

    public PaymentMethodServiceTests()
    {
        _mockRepository = new Mock<IRepository<PayorPaymentMethodAggregate, string>>();
        _mockLogger = new Mock<ILogger<PaymentMethodService>>();
        _service = new PaymentMethodService(_mockRepository.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task CreatePayorPaymentMethodAsync_WithTokenizedPayment_CreatesPaymentMethod()
    {
        // Arrange
        var payment = CreatePaymentWithTokenizedBearer();

        // Act
        await _service.CreatePayorPaymentMethodAsync(payment);

        // Assert
        _mockRepository.Verify(r => r.InsertAsync(It.IsAny<PayorPaymentMethodAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    private PaymentAggregate CreatePaymentWithTokenizedBearer()
    {
        var payment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("USD", "US Dollar", 100.00m, 2),
            "policy123",
            "invoice123",
            "payor123",
            DateTime.UtcNow);

        var tokenizedBearer = new PSPBearerPseudoCC
        {
            Token = "token123",
            PseudoCardPan = "4242****4242",
            TruncatedCardPan = "4242****4242",
            CardType = "Visa",
            Country = "US",
            ExpiryMonth = 12,
            ExpiryYear = 2025,
            Holder = "John Doe"
        };

        payment.SetInitialBearer(tokenizedBearer);
        return payment;
    }

    private PaymentAggregate CreatePaymentWithoutToken()
    {
        var payment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("USD", "US Dollar", 100.00m, 2),
            "policy123",
            "invoice123",
            "payor123",
            DateTime.UtcNow);

        var nonTokenizedBearer = new PSPBearer
        {
            Token = null
        };

        payment.SetInitialBearer(nonTokenizedBearer);
        return payment;
    }
}
