﻿mutation TokenizePaymentInitialization($input: TokenizePaymentInitializationInput!) {
    tokenizePaymentInitialization(input: $input) {
        tokenizedPaymentInitialization {
            initializationToken
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                    code
                }
            }
        }
    }
}