﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment
{
    public class InitializeUpdatePaymentCommandValidator : AbstractValidator<InitializeUpdatePaymentCommand>
    {
        public InitializeUpdatePaymentCommandValidator(
            ILogger<InitializeUpdatePaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.CurrencyDesc).NotEmpty().WithMessage("No currency description found.");
            RuleFor(pc => pc.PaymentProvider).NotEmpty().WithMessage("No payment provider found.");
            RuleFor(pc => pc.PayorId).NotEmpty().WithMessage("No payor Id found.");
            RuleFor(pc => pc.PolicyId).NotEmpty().WithMessage("No policy Id found.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}