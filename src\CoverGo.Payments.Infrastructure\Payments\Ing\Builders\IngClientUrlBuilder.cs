﻿using GuardClauses;

namespace CoverGo.Payments.Infrastructure.Payments.Ing.Builders
{
    public static class IngClientUrlBuilder
    {
        public static Uri BuildGetPaymentProfileUrl(string baseUrl, string apiVersion, string merchantId, string customerId)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(apiVersion, nameof(apiVersion));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(merchantId, nameof(merchantId));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(customerId, nameof(customerId));
            
            return new Uri(string.Concat(baseUrl, "/",  apiVersion, "/merchant/", merchantId, "/profile/cid/", customerId));
        }
        
        public static Uri BuildDebitPaymentProfileUrl(string baseUrl, string apiVersion, string merchantId)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(apiVersion, nameof(apiVersion));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(merchantId, nameof(merchantId));
            
            return new Uri(string.Concat(baseUrl, "/",  apiVersion, "/merchant/", merchantId, "/transaction/profile"));
        }

        public static Uri BuildRefundUrl(string baseUrl, string apiVersion, string merchantId, string transactionId)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(apiVersion, nameof(apiVersion));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(merchantId, nameof(merchantId));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(transactionId, nameof(transactionId));

            return new Uri(string.Concat(baseUrl, "/", apiVersion, "/merchant/", merchantId, "/transaction/", transactionId, "/refund"));
        }
    }
}