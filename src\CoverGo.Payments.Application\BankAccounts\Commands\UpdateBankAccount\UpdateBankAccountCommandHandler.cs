using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.BankAccount;

namespace CoverGo.Payments.Application.BankAccounts.Commands.UpdateBankAccount;

public record UpdateBankAccountCommand : ICommand<BankAccount>
{
    public required string Id { get; init; }
    public required string Country { get; init; }
    public required string Currency { get; init; }
    public required string AccountHolderName { get; init; }
    public required string BankName { get; init; }
    public required string Bic { get; init; }
    public required List<BankAccountUsage> UsedFor { get; init; }
    public string? AccountNumber { get; init; } = null;
    public string? Iban { get; init; } = null;
}

public class UpdateBankAccountCommandHandler : ICommandHandler<UpdateBankAccountCommand, BankAccount>
{
    private readonly IRepository<BankAccount, string> _bankAccountRepository;

    public UpdateBankAccountCommandHandler(
        IRepository<BankAccount, string> bankAccountRepository)
    {
        _bankAccountRepository = bankAccountRepository;
    }

    public async Task<BankAccount> Handle(UpdateBankAccountCommand request, CancellationToken cancellationToken)
    {
        // Get existing bank account
        var bankAccount = await _bankAccountRepository.GetByIdAsync(request.Id, cancellationToken);

        // Update the aggregate
        bankAccount.UpdateBankAccount(
            request.Country,
            request.Currency,
            request.AccountHolderName,
            request.BankName,
            request.Bic,
            request.UsedFor,
            request.AccountNumber,
            request.Iban);

        // Save changes
        bankAccount = await _bankAccountRepository.UpdateAsync(bankAccount, cancellationToken);

        // Return aggregate directly
        return bankAccount;
    }
} 