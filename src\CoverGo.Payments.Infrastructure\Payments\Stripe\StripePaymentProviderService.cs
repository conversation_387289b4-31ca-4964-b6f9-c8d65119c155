﻿using System.Collections.Specialized;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Common;
using GuardClauses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Stripe;
using Stripe.Checkout;
using Address = CoverGo.Payments.Domain.Payment.Address;

namespace CoverGo.Payments.Infrastructure.Payments.Stripe;

public class StripePaymentProviderService(
    IStripeClientFactory stripeClientFactory,
    ILogger<StripePaymentProviderService> logger,
    IHttpContextAccessor httpContextAccessor) : BasePaymentProviderService(logger)
{
    public override PaymentProvider Type => PaymentProvider.Stripe;

    private IStripeClient CreateClientFromPayment(PaymentAggregate payment)
    {
        logger.LogDebug("Creating Stripe client from payment with ID: {PaymentId}", payment.Id);

        StripePspSettingsAggregate? pspSettings = GetPspSettings<StripePspSettingsAggregate>(payment.PspSettings);
        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

        return CreateClientFromPspSettings(pspSettings!);
    }

    private IStripeClient CreateClientFromPspSettings(StripePspSettingsAggregate pspSettings)
    {
        logger.LogDebug("Creating Stripe client with API key");

        GuardClause.IsNullOrEmptyStringOrWhiteSpace(pspSettings.ApiKey, nameof(pspSettings.ApiKey));

        return stripeClientFactory.CreateClient(pspSettings.ApiKey);
    }

    public override async Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to get pre-process redirect URL for payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        Session session;
        if (payment.IsUpdate)
        {
            session = await CreateUpdateCheckoutSessionAsync(payment, dynamicFields, cancellationToken);
        }
        else
        {
            session = await CreateCheckoutSessionAsync(payment, dynamicFields, cancellationToken);
        }

        logger.LogInformation("Obtained redirect URL for payment with ID: {PaymentId}", payment.Id);
        return new RedirectUrlOutput(new Uri(session.Url),
            new Dictionary<string, string> { { "sessionId", session.Id } });
    }

    public override async Task<RecurringPaymentAggregate> RecurringPaymentAsync(
        RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting recurring payment process with ID: {RecurringPaymentId}", recurringPayment.Id);

        GuardClause.ArgumentIsNotNull(recurringPayment, nameof(recurringPayment));

        try
        {
            string? paymentMethodId = recurringPayment.InitialBearer?.Token;
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(paymentMethodId, nameof(paymentMethodId));

            IStripeClient stripeClient = CreateClientFromPayment(recurringPayment);

            var paymentIntentService = new PaymentIntentService(stripeClient);
            var paymentIntentOptions = new PaymentIntentCreateOptions
            {
                Amount = CurrencyHelper.ConvertUnitsToSubunits(recurringPayment.Money.PaymentAmount),
                Currency = CurrencyHelper.GetCurrency(recurringPayment.Money.PaymentCurrencyCode).ToLower(),
                PaymentMethod = paymentMethodId,
                Customer = recurringPayment.PayerData?.ExternalCustomerId,
                OffSession = true,
                Confirm = true,
                Metadata = BuildStripeMetadata(recurringPayment)
            };

            PaymentIntent paymentIntent =
                await paymentIntentService.CreateAsync(paymentIntentOptions,
                    new RequestOptions { IdempotencyKey = recurringPayment.InternalReference },
                    cancellationToken: cancellationToken);

            logger.LogInformation(
                "PaymentIntent created successfully for recurring payment with ID: {RecurringPaymentId}, PaymentIntentId: {PaymentIntentId}",
                recurringPayment.Id, paymentIntent.Id);

            recurringPayment.AssignProviderTransaction(paymentIntent.Id);
            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, recurringPayment.Money);

            return recurringPayment;
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Failed to process recurring payment with ID: {RecurringPaymentId}",
                recurringPayment.Id);
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex,
                "An unexpected error occurred while processing recurring payment with ID: {RecurringPaymentId}",
                recurringPayment.Id);
            throw;
        }
    }

    private async Task<Session> CreateCheckoutSessionAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Creating checkout session for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);
        StripePspSettingsAggregate? pspSettings = GetPspSettings<StripePspSettingsAggregate>(payment.PspSettings);

        string successUrlWithPaymentId = AppendPaymentIdToUrl(pspSettings?.SuccessUrl, payment.Id, dynamicFields);

        var options = new SessionCreateOptions
        {
            CustomerCreation = "always",
            LineItems =
            [
                new SessionLineItemOptions
                {
                    PriceData = new SessionLineItemPriceDataOptions
                    {
                        UnitAmount = CurrencyHelper.ConvertUnitsToSubunits(payment.Money.PaymentAmount),
                        Currency = CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode).ToLower(),
                        ProductData = new SessionLineItemPriceDataProductDataOptions
                        {
                            Name = payment.InternalReference, Description = payment.GetDescription()
                        }
                    },
                    Quantity = 1
                }
            ],
            Mode = "payment",
            SuccessUrl = successUrlWithPaymentId,
            CancelUrl = pspSettings?.CancelUrl,
            PaymentIntentData = new SessionPaymentIntentDataOptions
            {
                CaptureMethod = pspSettings?.UseAutoCapture ?? false ? "automatic" : "manual",
                Metadata = BuildStripeMetadata(payment),
                SetupFutureUsage = "off_session"
            },
            Metadata = BuildStripeMetadata(payment)
        };

        try
        {
            var service = new SessionService(stripeClient);
            Session? session = await service.CreateAsync(options,
                new RequestOptions { IdempotencyKey = payment.InternalReference },
                cancellationToken: cancellationToken);

            logger.LogInformation("Stripe session created successfully: {SessionId}", session.Id);

            return session;
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Stripe exception occurred while creating session");
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unexpected error occurred while creating Stripe session");
            throw;
        }
    }

    private async Task<Session> CreateUpdateCheckoutSessionAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Creating update checkout session for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);
        StripePspSettingsAggregate? pspSettings = GetPspSettings<StripePspSettingsAggregate>(payment.PspSettings);

        string successUrlWithPaymentId = AppendPaymentIdToUrl(pspSettings?.SuccessUrl, payment.Id, dynamicFields);

        var options = new SessionCreateOptions
        {
            CustomerCreation = "always",
            Mode = "setup",
            SuccessUrl = successUrlWithPaymentId,
            CancelUrl = pspSettings?.CancelUrl,
            SetupIntentData = new SessionSetupIntentDataOptions { Metadata = BuildStripeMetadata(payment), },
            Metadata = BuildStripeMetadata(payment),
            Currency = CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode).ToLower(),
        };

        try
        {
            var service = new SessionService(stripeClient);
            Session? session = await service.CreateAsync(options, cancellationToken: cancellationToken);

            logger.LogInformation("Stripe session created successfully: {SessionId}", session.Id);

            return session;
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Stripe exception occurred while creating session");
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unexpected error occurred while creating Stripe session");
            throw;
        }
    }

    private string AppendPaymentIdToUrl(string? url, string paymentId, JsonElement? dynamicFields)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(url, nameof(url));
        string origin = string.Empty;
        if (dynamicFields != null)
        {
            origin = ExtractRequestOrigin(dynamicFields.Value.GetRawText());
        }

        string newUrl = UrlHelper.ReplaceOrigin(url!, origin);
        logger.LogInformation($"Return URL for {PaymentProvider.Stripe} with origin: {origin} is {newUrl}");

        var uriBuilder = new UriBuilder(newUrl);
        NameValueCollection query = System.Web.HttpUtility.ParseQueryString(uriBuilder.Query);
        query["paymentId"] = paymentId;
        uriBuilder.Query = query.ToString();

        return uriBuilder.ToString();
    }

    private static Dictionary<string, string> BuildStripeMetadata(PaymentAggregate payment)
    {
        Dictionary<string, string> metadata = new() { { "paymentId", payment.Id } };
        if (!string.IsNullOrWhiteSpace(payment.InvoiceNumber)) metadata.Add("invoiceNumber", payment.InvoiceNumber);

        return metadata;
    }


    public override async Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate payment,
        string providerPaymentId,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to capture payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        IStripeClient stripeClient = CreateClientFromPayment(payment);

        try
        {
            var service = new PaymentIntentService(stripeClient);

            PaymentIntent paymentIntent =
                await service.CaptureAsync(providerPaymentId, cancellationToken: cancellationToken);

            logger.LogInformation("PaymentIntent captured successfully: {PaymentIntentId}", paymentIntent.Id);
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Failed to capture PaymentIntent id: {PaymentIntentId}", payment.ProviderPaymentId);
            throw;
        }

        payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, payment.Money);

        return payment;
    }

    public override async Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to cancel payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        await CancelPaymentIntentAsync(payment, cancellationToken);

        payment.AddPaymentStatusHistoryItem(PaymentStatus.Canceled, payment.Money);

        logger.LogInformation("Payment with ID: {PaymentId} cancelled successfully", payment.Id);
    }

    public override async Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to fail payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        await CancelPaymentIntentAsync(payment, cancellationToken);

        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, "Failed by PSP.");

        logger.LogInformation("Payment with ID: {PaymentId} marked as failed", payment.Id);
    }

    private async Task CancelPaymentIntentAsync(PaymentAggregate payment, CancellationToken cancellationToken)
    {
        logger.LogDebug("Cancelling PaymentIntent for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);

        try
        {
            var service = new PaymentIntentService(stripeClient);

            PaymentIntent paymentIntent =
                await service.CancelAsync(payment.ProviderPaymentId, cancellationToken: cancellationToken);

            logger.LogInformation("PaymentIntent cancelled successfully: {PaymentIntentId}", paymentIntent.Id);
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Failed to cancel PaymentIntent");
            throw;
        }
    }

    public override async Task FinalizePaymentAsync(PreauthPaymentAggregate payment,
        PreauthPaymentAggregate? prevPayment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to finalize payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(payment.ProviderPaymentId, nameof(payment.ProviderPaymentId));

        if (payment.IsUpdate)
        {
            Customer? customer = await GetCustomerFromSetupIntentAsync(payment, cancellationToken);
            if (customer != null)
                MapCustomerToPayerData(payment, customer);

            PSPBearer? pspBearerToken = await GetPspBearerTokenFromSetupIntentAsync(payment, cancellationToken);
            if (pspBearerToken != null)
                payment.SetInitialBearer(pspBearerToken);
        }
        else
        {
            Customer? customer = await GetCustomerAsync(payment, cancellationToken);
            if (customer != null)
                MapCustomerToPayerData(payment, customer);

            PSPBearer? pspBearerToken = await GetPspBearerTokenAsync(payment, cancellationToken);
            if (pspBearerToken != null)
                payment.SetInitialBearer(pspBearerToken);
        }

        payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, payment.Money);

        logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);
    }


    private async Task<Customer?> GetCustomerAsync(PreauthPaymentAggregate payment, CancellationToken cancellationToken)
    {
        logger.LogDebug("Retrieving customer for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);

        var paymentIntentService = new PaymentIntentService(stripeClient);
        PaymentIntent paymentIntent =
            await paymentIntentService.GetAsync(payment.ProviderPaymentId, cancellationToken: cancellationToken);

        if (paymentIntent.CustomerId == null) return null;

        var customerService = new CustomerService(stripeClient);

        return await customerService.GetAsync(paymentIntent.CustomerId, cancellationToken: cancellationToken);
    }

    private async Task<Customer?> GetCustomerFromSetupIntentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Retrieving customer for setup intent with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);

        var setupIntentService = new SetupIntentService(stripeClient);
        SetupIntent setupIntent =
            await setupIntentService.GetAsync(payment.ProviderPaymentId, cancellationToken: cancellationToken);

        if (setupIntent.CustomerId == null) return null;

        var customerService = new CustomerService(stripeClient);

        return await customerService.GetAsync(setupIntent.CustomerId, cancellationToken: cancellationToken);
    }

    private async Task<PSPBearerPseudoCC?> GetPspBearerTokenAsync(PaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Retrieving PSP bearer token for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);

        var paymentIntentService = new PaymentIntentService(stripeClient);
        PaymentIntent paymentIntent = await paymentIntentService.GetAsync(payment.ProviderPaymentId,
            new PaymentIntentGetOptions
            {
                Expand =
                    ["payment_method"]
            }, null, cancellationToken);

        string? paymentMethodId = paymentIntent.PaymentMethodId;
        if (string.IsNullOrEmpty(paymentMethodId))
        {
            string errorMessage = "No payment method associated with the payment intent.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        var paymentMethodService = new PaymentMethodService(stripeClient);
        PaymentMethod paymentMethod =
            await paymentMethodService.GetAsync(paymentMethodId, cancellationToken: cancellationToken);

        var pspBearerPseudoCc = new PSPBearerPseudoCC
        {
            Token = paymentMethod.Id,
            PseudoCardPan = paymentMethod.Card?.Fingerprint,
            CardType = paymentMethod.Card?.Brand,
            Country = paymentMethod.Card?.Country,
            ExpiryMonth = (int?)paymentMethod.Card?.ExpMonth,
            ExpiryYear = (int?)paymentMethod.Card?.ExpYear,
            Holder = paymentMethod.BillingDetails?.Name,
            TruncatedCardPan = paymentMethod.Card?.Last4,
            OrderId = payment.ProviderPaymentId,
            IssuerId = paymentMethod.Card?.Issuer
        };

        return pspBearerPseudoCc;
    }

    private async Task<PSPBearerPseudoCC?> GetPspBearerTokenFromSetupIntentAsync(PaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        logger.LogDebug("Retrieving PSP bearer token for setup intent with ID: {PaymentId}", payment.Id);

        IStripeClient stripeClient = CreateClientFromPayment(payment);

        var setupIntentService = new SetupIntentService(stripeClient);
        SetupIntent setupIntent =
            await setupIntentService.GetAsync(payment.ProviderPaymentId, null, null, cancellationToken);

        string? paymentMethodId = setupIntent.PaymentMethodId;
        if (string.IsNullOrEmpty(paymentMethodId))
        {
            string errorMessage = "No payment method associated with the setup intent.";
            logger.LogError(errorMessage);
            throw new InvalidOperationException(errorMessage);
        }

        var paymentMethodService = new PaymentMethodService(stripeClient);
        PaymentMethod paymentMethod =
            await paymentMethodService.GetAsync(paymentMethodId, cancellationToken: cancellationToken);

        var pspBearerPseudoCc = new PSPBearerPseudoCC
        {
            Token = paymentMethod.Id,
            PseudoCardPan = paymentMethod.Card?.Fingerprint,
            CardType = paymentMethod.Card?.Brand,
            Country = paymentMethod.Card?.Country,
            ExpiryMonth = (int?)paymentMethod.Card?.ExpMonth,
            ExpiryYear = (int?)paymentMethod.Card?.ExpYear,
            Holder = paymentMethod.BillingDetails?.Name,
            TruncatedCardPan = paymentMethod.Card?.Last4,
            OrderId = payment.ProviderPaymentId,
            IssuerId = paymentMethod.Card?.Issuer
        };

        return pspBearerPseudoCc;
    }

    public override async Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting to refund payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentRefund, nameof(paymentRefund));
        GuardClause.ArgumentIsNotNull(paymentRefund.ProviderPaymentId, nameof(paymentRefund.ProviderPaymentId));


        Refund refund = await CreateRefundAsync(payment, paymentRefund, cancellationToken);

        bool isSucceeded = refund.Status == "succeeded";

        paymentRefund.SetStatus(isSucceeded ? RefundStatus.Succeeded : RefundStatus.Failed);

        logger.LogInformation("Refund {RefundStatus} for payment with ID: {PaymentId}, Refund ID: {RefundId}",
            isSucceeded ? "succeeded" : "failed", payment.Id, refund.Id);

        return paymentRefund;
    }

    private async Task<Refund> CreateRefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken)
    {
        logger.LogDebug("Creating refund for payment with ID: {PaymentId}", payment.Id);

        IStripeClient stripeService = CreateClientFromPayment(payment);

        var refundService = new RefundService(stripeService);
        var refundOptions = new RefundCreateOptions
        {
            PaymentIntent = paymentRefund.ProviderPaymentId,
            Amount = CurrencyHelper.ConvertUnitsToSubunits(paymentRefund.Money.PaymentAmount),
            Reason = RefundReasons.RequestedByCustomer
        };

        try
        {
            Refund? refund = await refundService.CreateAsync(refundOptions, cancellationToken: cancellationToken);
            logger.LogInformation("Refund created successfully: {RefundId}", refund.Id);
            return refund;
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Stripe exception occurred while creating refund");
            throw;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unexpected error occurred while creating refund");
            throw;
        }
    }


    public override async
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus, decimal
            ? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(
            string webhookBody, PspSettingsAggregate pspSettings, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Handling webhook with serialized PSP settings");

        var stripePspSettingsAggregate = pspSettings as StripePspSettingsAggregate;
        GuardClause.ArgumentIsNotNull(stripePspSettingsAggregate, nameof(stripePspSettingsAggregate));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(stripePspSettingsAggregate!.WebhookSecret,
            nameof(stripePspSettingsAggregate.WebhookSecret));

        HttpContext httpContext = httpContextAccessor.HttpContext ??
                                  throw new InvalidOperationException("No active HTTP context.");

        try
        {
            if (!httpContext.Request.Headers.TryGetValue("Stripe-Signature", out StringValues stripeSignature))
            {
                logger.LogWarning("Missing Stripe-Signature header");
                return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                    "Missing Stripe-Signature header", false, false);
            }

            Event? stripeEvent = EventUtility.ConstructEvent(webhookBody, stripeSignature,
                stripePspSettingsAggregate.WebhookSecret,
                throwOnApiVersionMismatch: false);
            return await HandleStripeEventAsync(stripeEvent);
        }
        catch (StripeException ex)
        {
            logger.LogError(ex, "Stripe exception occurred while handling webhook event");
            return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Stripe exception occurred",
                false, false);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "An unexpected error occurred while handling webhook event");
            return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Unexpected error occurred",
                false, false);
        }
    }

    private Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus,
            decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)>
        HandleStripeEventAsync(
            Event stripeEvent)
    {
        logger.LogDebug("Handling Stripe event: {StripeEventType}", stripeEvent.Type);

        string? paymentId;
        switch (stripeEvent.Type)
        {
            case Events.CheckoutSessionCompleted:
                if (stripeEvent.Data.Object is Session sessionCompleted &&
                    sessionCompleted.Metadata.TryGetValue("paymentId", out paymentId))
                {
                    logger.LogInformation(
                        "Checkout session completed: SessionId = {SessionId}, PaymentIntentId = {PaymentIntentId}, PaymentId = {PaymentId}",
                        sessionCompleted.Id, sessionCompleted.PaymentIntentId ?? sessionCompleted.SetupIntentId,
                        paymentId);
                    return Task.FromResult((paymentId,
                        sessionCompleted.PaymentIntentId ?? sessionCompleted.SetupIntentId, string.Empty, string.Empty,
                        PaymentStatus.Prepared, CurrencyHelper.ConvertSubunitsToUnits(sessionCompleted.AmountTotal),
                        string.Empty, false, false));
                }

                break;
            case Events.CheckoutSessionExpired:
                if (stripeEvent.Data.Object is Session sessionExpired &&
                    sessionExpired.Metadata.TryGetValue("paymentId", out paymentId))
                {
                    logger.LogInformation(
                        "Checkout session expired: SessionId = {SessionId}, PaymentIntentId = {PaymentIntentId}, PaymentId = {PaymentId}",
                        sessionExpired.Id, sessionExpired.PaymentIntentId, paymentId);
                    return Task.FromResult((paymentId, sessionExpired.PaymentIntentId, string.Empty, string.Empty,
                        PaymentStatus.Expired, CurrencyHelper.ConvertSubunitsToUnits(sessionExpired.AmountSubtotal),
                        string.Empty, false, false));
                }

                break;

            default:
                logger.LogWarning("Unhandled event type: {StripeEventType}", stripeEvent.Type);
                break;
        }

        return Task
            .FromResult<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
                PaymentStatus
                paymentStatus, decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip
                )>((
                string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Unhandled event type", false, false));
    }

    private static void MapCustomerToPayerData(PaymentAggregate payment, Customer customer) =>
        payment.SetPayerData(new PayerData(
            language: null,
            emailAddress: customer.Email,
            address: new Address(
                customer.Address.Line1,
                customer.Address.Line2,
                string.Empty,
                string.Empty,
                customer.Address.PostalCode,
                customer.Address.City,
                customer.Address.State,
                customer.Address.Country),
            lastName: customer.Name?.Split(' ').LastOrDefault() ?? string.Empty,
            firstName: customer.Name?.Split(' ').FirstOrDefault() ?? string.Empty,
            externalCustomerId: customer.Id,
            companyName: string.Empty,
            phoneNumber: customer.Phone));
}