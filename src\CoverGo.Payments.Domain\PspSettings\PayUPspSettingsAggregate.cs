﻿using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.Domain.PspSettings;

public class PayUPspSettingsAggregate : PspSettingsAggregate
{
    [Encrypted]
    public string SecretKey { get; set; }

    public string MerchantCode { get; set; }
    
    // "qa" or "prod"
    public string Environment { get; set; }
    
    public static string ApiVersion => "v4";

    public int SessionLifetimeMinutes { get; set; } = 10;
}