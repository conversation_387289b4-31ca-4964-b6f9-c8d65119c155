using CoverGo.BuildingBlocks.Auth.SSO;
using CoverGo.Multitenancy;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;

namespace CoverGo.Payments.IntegrationTests.Support;

public class PaymentsWebApplicationFactory : WebApplicationFactory<Program>
{
    public TenantId? Tenant { get; set; } = new("covergo");
    
    protected override void ConfigureWebHost(IWebHostBuilder builder)
    {
        builder.ConfigureTestServices(services =>
        {
            services.Configure<JwtBearerOptions>(JwtBearerDefaults.AuthenticationScheme, it =>
            {
                it.TokenValidationParameters.ValidateIssuer = false;
                it.ForwardDefaultSelector = null;
            });
            services.RemoveAll<SSOJwtBearerEvents>();
            services.RemoveAll<ITenantProvider>();
            if (Tenant is null) 
                return;
            services.AddSingleton<ITenantProvider>(new ValueTenantProvider(Tenant));
            services.AddSingleton(Tenant);
        });

        builder.UseEnvironment(TestEnvironment.GetEnvironment());
    }
}