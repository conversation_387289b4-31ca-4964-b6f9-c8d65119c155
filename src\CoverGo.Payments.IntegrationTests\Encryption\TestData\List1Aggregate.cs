﻿using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.IntegrationTests.Encryption.TestData
{
    public class List1Aggregate : AggregateRootBase<string>
    {
        public List1Aggregate(string id) : base(id)
        {
        }

        [Encrypted]
        public List<string>? SensitiveListString { get; set; }

        [Encrypted]
        public List<Class1>? SensitiveListObject { get; set; }

        public List<Class1>? NormalListObject { get; set; }
    }
}
