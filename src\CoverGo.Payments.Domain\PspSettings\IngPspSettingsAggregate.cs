﻿using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.Domain.PspSettings;

public class IngPspSettingsAggregate : PspSettingsAggregate
{
    public string MerchantId   { get; set; }
    public string ServiceId { get; set; }

    [Encrypted]
    public string ServiceKey { get; set; }

    [Encrypted]
    public string ApiKey { get; set; }
    public string ApiVersion { get; set; } = "v1";
    public int RetryCount { get; set; } = 3;
    // "qa" or "prod"
    public string Environment { get; set; }
}