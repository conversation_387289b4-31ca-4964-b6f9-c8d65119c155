using System.Net;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Infrastructure.Payments.Moneris;
using CoverGo.Payments.Infrastructure.Payments.Moneris.Models;
using Microsoft.AspNetCore.Mvc;
using GuardClauses;

namespace CoverGo.Payments.Api.Controllers;

[Route("api/v1/payments/{tenantId}/moneris")]
[ApiController]
public class MonerisController(
    IPaymentService paymentService,
    MonerisPaymentProviderService monerisPaymentProviderService,
    ILogger<MonerisController> logger) : Controller
{
    /// <summary>
    /// Fetch receipt data for a Moneris payment using payment ID and ticket
    /// </summary>
    /// <param name="paymentId">The payment ID</param>
    /// <param name="ticket">The Moneris ticket ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ResponseData containing receipt information</returns>
    [HttpGet("receipt/{paymentId}")]
    public async Task<IActionResult> GetReceipt(
        [FromRoute] string paymentId,
        [FromQuery] string ticket,
        CancellationToken cancellationToken = default)
    {
        try
        {
            GuardClause.ArgumentIsNotNullOrEmpty(paymentId, nameof(paymentId));
            GuardClause.ArgumentIsNotNullOrEmpty(ticket, nameof(ticket));

            logger.LogInformation("Fetching receipt for payment ID: {PaymentId} with ticket: {Ticket}", paymentId, ticket);

            // Get payment from service
            PaymentAggregate? payment = await paymentService.GetPaymentAsync(paymentId, cancellationToken);
            if (payment == null)
            {
                logger.LogWarning("Payment not found for ID: {PaymentId}", paymentId);
                return NotFound($"Payment with ID {paymentId} not found");
            }

            // Verify this is a Moneris payment
            if (payment.PaymentProvider != PaymentProvider.Moneris)
            {
                logger.LogWarning("Payment {PaymentId} is not a Moneris payment. Provider: {Provider}", paymentId, payment.PaymentProvider);
                return BadRequest($"Payment {paymentId} is not a Moneris payment");
            }

            // Fetch receipt data using the public method
            ResponseData responseData = await monerisPaymentProviderService.GetReceiptDataAsync(payment, ticket, cancellationToken);

            logger.LogInformation("Receipt fetched successfully for payment ID: {PaymentId}", paymentId);
            return Ok(responseData);
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning(ex, "Invalid arguments provided for payment ID: {PaymentId}", paymentId);
            return BadRequest(ex.Message);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error fetching receipt for payment ID: {PaymentId}", paymentId);
            return StatusCode((int)HttpStatusCode.InternalServerError, "Internal Server Error");
        }
    }
}
