﻿namespace CoverGo.Payments.Infrastructure.Payments.PayU.Helpers;

public static class CardHelper
{
    /// <summary>
    /// Extracts the expiry month and year from a card expiration date string.
    /// </summary>
    /// <param name="cardExpirationDate">The card expiration date in the format "yyyy-MM-dd".</param>
    /// <param name="expiryMonth">The extracted expiry month as an integer.</param>
    /// <param name="expiryYear">The extracted expiry year as an integer.</param>
    public static void ExtractExpiryMonthAndYear(string? cardExpirationDate, out int expiryMonth, out int expiryYear)
    {
        expiryMonth = 0;
        expiryYear = 0;
        if (cardExpirationDate == null) return;

        if (DateTime.TryParse(cardExpirationDate, out DateTime expirationDate))
        {
            expiryMonth = expirationDate.Month;
            expiryYear = expirationDate.Year;
        }
        else
        {
            throw new ArgumentException("Invalid date format. Expected format is yyyy-MM-dd.",
                nameof(cardExpirationDate));
        }
    }
}