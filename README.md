# Payments Service

## Overview

TODO

### CI/CD

CoverGo is using a combination of Github actions workflow, custom Github actions and templating tool gflows to produce a
workflow for your service. Workflow template is stored in Covergo/gflows public repository.

**Update Configuration**

- After using the template, you should go to the  **build-publish.settings.yml** file (*
  *.gflows/workflow-configuration/build-publish/build-publish.settings.yml**) and replace all occurrences of "
  ServiceName" with the target service name.
- Then, update the CI/CD related configuration files by running the command **gflows update**. If the gflows tool is not
  installed, follow the instructions in
  this -> [GFlows tool](https://covergo.atlassian.net/wiki/spaces/BE/pages/703397889/Install+gflows+system-wide)

### Prerequisites

- .NET 8.0 or later
- Docker (for containerization and orchestration)
- Any other specific dependencies our services typically require

### Observability

The setup includes an OpenTelemetry Collector for collecting and exporting traces, which are visualized in Zipkin.

- **OpenTelemetry Collector** is accessible on:
    - OTLP gRPC Receiver: `localhost:4317`
    - OTLP HTTP Receiver: `localhost:4318`

### Testing GraphQL API Instructions

Follow these steps to test the GraphQL API:

1. **Run Docker Compose**:
    - Make sure Docker is installed on your system.
    - Navigate to the directory containing the Docker Compose file.
    - Run the following command to start the services:
      ```bash
      docker-compose up
      ```

2. **GraphQL Service Address**:
    - After Docker Compose has successfully started, the example of service for business domain (GraphQL) will be
      available at the following address:
      ```http
      http://localhost:5201/graphql/
      ```

3. **Testing GraphQL Endpoint**:
    - You can test the GraphQL endpoint using Postman or any other tool of your choice.
    - Set up a new request in your chosen tool.
    - Use the HTTP POST method.
    - Set the request URL to the GraphQL service address mentioned above.
    - Include the GraphQL query or mutation in the request body.
    - Send the request and observe the response.

     ```http
      http://localhost:5201/graphql/
      ```

## Additional Resources

- [Application Architecture](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145643/Application+Architecture)
- [Building blocks](https://covergo.atlassian.net/wiki/spaces/BE/pages/1141145690/Building+Blocks+BB)
- [Coding Standards](https://covergo.atlassian.net/wiki/spaces/BE/pages/899710983/Code+Style+Guide)
- [API guidelines](https://covergo.atlassian.net/wiki/spaces/Engineering/pages/1171521775/API+Guidelines)
