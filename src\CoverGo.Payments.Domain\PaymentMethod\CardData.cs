using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;

namespace CoverGo.Payments.Domain.PaymentMethod;

public class CardData : ValueObject
{
    private CardData() { }

    public CardData(
        string? pseudoCardPan,
        string? truncatedCardPan,
        int? expiryMonth,
        int? expiryYear,
        string? cardType,
        string? country,
        string? holder)
    {
        PseudoCardPan = pseudoCardPan;
        TruncatedCardPan = truncatedCardPan;
        ExpiryMonth = expiryMonth;
        ExpiryYear = expiryYear;
        CardType = cardType;
        Country = country;
        Holder = holder;
    }

    public string? PseudoCardPan { get; private set; }
    
    public string? TruncatedCardPan { get; private set; }
    
    public int? ExpiryMonth { get; private set; }
    
    public int? ExpiryYear { get; private set; }
    
    public string? CardType { get; private set; }
    
    public string? Country { get; private set; }
    
    public string? Holder { get; private set; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        if (PseudoCardPan != null) yield return PseudoCardPan;
        if (TruncatedCardPan != null) yield return TruncatedCardPan;
        if (ExpiryMonth.HasValue) yield return ExpiryMonth.Value;
        if (ExpiryYear.HasValue) yield return ExpiryYear.Value;
        if (CardType != null) yield return CardType;
        if (Country != null) yield return Country;
        if (Holder != null) yield return Holder;
    }
}
