﻿using System.Net;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.AspNetCore.Mvc;

namespace CoverGo.Payments.Api.Controllers;

[Route("api/v1/payments/{tenantId}")]
[ApiController]
// ReSharper disable once InconsistentNaming
public class PSPWebhooksController(IPaymentService paymentService, ILogger<PSPWebhooksController> logger)
    : Controller
{
    [HttpPost("webhook/{paymentProvider}")]
    // ReSharper disable once RouteTemplates.MethodMissingRouteParameters
    public async Task<IActionResult> HandleWebhook(
        [FromRoute] PaymentProvider paymentProvider,
        CancellationToken cancellationToken)
    {
        string body = await new StreamReader(HttpContext.Request.Body).ReadToEndAsync(cancellationToken);
        return await ProcessWebhook(paymentProvider, body, cancellationToken);
    }

    [HttpGet("webhook/{paymentProvider}")]
    // ReSharper disable once RouteTemplates.MethodMissingRouteParameters
    public async Task<IActionResult> HandleWebhookViaGet(
        [FromRoute] PaymentProvider paymentProvider,
        [FromQuery] string? body = null,
        CancellationToken cancellationToken = default)
    {
        logger.LogWarning("GET request received; internally forwarding to POST logic for {PaymentProvider}", paymentProvider);
        return await ProcessWebhook(paymentProvider, body ?? "{}", cancellationToken);
    }

    private async Task<IActionResult> ProcessWebhook(
        PaymentProvider paymentProvider,
        string body,
        CancellationToken cancellationToken)
    {
        logger.LogInformation("Processing webhook for {PaymentProvider}: {WebhookBody}", paymentProvider, body);

        try
        {
            (string responseStr, HttpStatusCode httpStatusCode) =
                await paymentService.HandleWebhookAsync(body, paymentProvider, cancellationToken);

            logger.LogInformation("Webhook processed successfully for {PaymentProvider}. Response: {ResponseStr}", paymentProvider, responseStr);
            return CreateHttpResponseMessage(httpStatusCode, responseStr);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing webhook from {PaymentProvider}", paymentProvider);
            return StatusCode((int)HttpStatusCode.InternalServerError, "Internal Server Error");
        }
    }

    private IActionResult CreateHttpResponseMessage(HttpStatusCode httpStatusCode, string responseStr) =>
        httpStatusCode == HttpStatusCode.OK ? Ok(responseStr) : StatusCode((int)httpStatusCode, responseStr);
}