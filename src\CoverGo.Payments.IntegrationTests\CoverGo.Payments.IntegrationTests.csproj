<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <IsPackable>false</IsPackable>
        <IsTestProject>true</IsTestProject>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CoverGo.BuildingBlocks.Auth"/>
        <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.Mongo"/>
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer"/>
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing"/>
        <PackageReference Include="Snapshooter.Xunit" />
    </ItemGroup>

    <ItemGroup>
        <None Remove="testsettings.json"/>
        <None Remove="testsettings.Development.Tests.json"/>
        <None Remove="testsettings.Staging.CI.json"/>
        <Content Include="testsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
        </Content>
        <Content Include="testsettings.Development.Tests.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <DependentUpon>testsettings.json</DependentUpon>
        </Content>
        <Content Include="testsettings.Staging.CI.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
            <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
            <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
            <DependentUpon>testsettings.json</DependentUpon>
        </Content>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\CoverGo.Payments.Api\CoverGo.Payments.Api.csproj"/>
        <ProjectReference Include="..\CoverGo.Payments.Tests.GatewayClient\CoverGo.Payments.Tests.GatewayClient.csproj"/>
        <ProjectReference Include="..\CoverGo.Payments.Tests.PaymentsClient\CoverGo.Payments.Tests.PaymentsClient.csproj"/>
    </ItemGroup>
    
</Project>
