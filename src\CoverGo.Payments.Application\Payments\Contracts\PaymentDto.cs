﻿using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Contracts;

public class PaymentDto
{
    public string Id { get; set; } = null!;

    public MoneyDto Money { get; set; } = null!;

    public PaymentProvider PaymentProvider { get; set; }

    public string InternalReference { get; set; } = null!;

    public string? ExternalReference { get; set; }

    public PaymentStatus? PaymentStatus { get; set; }

    public AuditInfo AuditInfo { get; set; } = null!;

    public decimal RefundedAmount { get; set; }
    public string? DynamicFields { get; set; }
}