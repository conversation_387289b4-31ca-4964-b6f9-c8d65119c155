﻿using CoverGo.BuildingBlocks.Domain.Core.Audit;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Domain.PspSettings;

public abstract class PspSettingsAggregate()
    : AggregateRootBase<string>(Guid.NewGuid().ToString())
{
    public PaymentProvider PaymentProvider { get; set; }
    
    public string ApiUrl { get; set; }

    public string RedirectUrl { get; set; }
    
    public string SuccessUrl { get; set; }
    
    public string CancelUrl { get; set; }
    
    public string FailUrl { get; set; }
    
    public string NotifyUrl { get; set; }
    
    public bool UseAutoCapture { get; set; }
    
    public new EntityAuditInfo EntityAuditInfo { get; set; }
}