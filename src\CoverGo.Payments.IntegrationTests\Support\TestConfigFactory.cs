﻿using Microsoft.Extensions.Configuration;

namespace CoverGo.Payments.IntegrationTests.Support;

internal class TestConfigFactory
{
    private const string SettingsFileName = "testsettings";

    public static IConfiguration Create()
    {
        string environmentName = TestEnvironment.GetEnvironment();
        Console.WriteLine($"Loading settings for {environmentName} environment");

        IConfigurationBuilder config = new ConfigurationBuilder()
            .AddJsonFile($"{SettingsFileName}.json")
            .AddJsonFile($"{SettingsFileName}.{environmentName}.json", true)
            .AddEnvironmentVariables()
            .AddUserSecrets<TestConfigFactory>(true);

        return config.Build();
    }
}