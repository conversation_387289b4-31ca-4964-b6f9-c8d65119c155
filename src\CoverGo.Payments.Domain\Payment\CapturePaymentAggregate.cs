﻿using GuardClauses;

namespace CoverGo.Payments.Domain.Payment;

public class  CapturePaymentAggregate : PaymentAggregate
{
    public string PreauthPaymentId { get; set; }
    
    public CapturePaymentAggregate(PreauthPaymentAggregate preauthPayment)
    {
        GuardClause.ArgumentIsNotNull(preauthPayment, nameof(preauthPayment));
        
        PaymentProvider = preauthPayment.PaymentProvider;
        Money = preauthPayment.Money;
        InitialBearer = preauthPayment.InitialBearer;
        PayerData = preauthPayment.PayerData;
        PreauthPaymentId = preauthPayment.Id;
        PspSettings = preauthPayment.PspSettings;
        PaymentStatusHistoryItems = new List<PaymentStatusHistoryItem>();
        ProviderPaymentId = preauthPayment.ProviderPaymentId;
        InternalReference = preauthPayment.InternalReference;
        ExternalReference = preauthPayment.ExternalReference;
        PolicyId = preauthPayment.PolicyId;
        PayorId = preauthPayment.PayorId;
        InvoiceNumber = preauthPayment.InvoiceNumber;
        DynamicFields = preauthPayment.DynamicFields;
        
        AddPaymentStatusHistoryItem(PaymentStatus.Created, preauthPayment.Money);
    }
}