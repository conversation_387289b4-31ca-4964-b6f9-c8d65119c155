﻿using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Providers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Common;
using GuardClauses;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments;

public abstract class BasePaymentProviderService(ILogger<BasePaymentProviderService> logger) : IPaymentProviderService
{
    public abstract PaymentProvider Type { get; }

    public async Task<PreauthPaymentAggregate> PreparePaymentAsync(PreauthPaymentAggregate preauthPayment,
        CancellationToken cancellationToken = default)
    {
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);
        return await Task.FromResult(preauthPayment);
    }

    public abstract Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default);

    public abstract Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default);

    public abstract Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
        string providerPaymentId,
        CancellationToken cancellationToken = default);

    public abstract Task<RecurringPaymentAggregate> RecurringPaymentAsync(RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default);

    public abstract Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default);

    public abstract Task FinalizePaymentAsync(PreauthPaymentAggregate payment, PreauthPaymentAggregate? prevPayment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default);

    public abstract Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default);

    public abstract
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference, PaymentStatus paymentStatus, decimal
            ? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(
            string webhookBody, PspSettingsAggregate pspSettings,
            CancellationToken cancellationToken = default);

    protected static TPspSettingsAggregate? GetPspSettings<TPspSettingsAggregate>(string settings)
        where TPspSettingsAggregate : PspSettingsAggregate
        => JsonConvert.DeserializeObject<TPspSettingsAggregate>(settings);

    public T? ParseDynamicFields<T>(string dynamicFieldsJson)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(dynamicFieldsJson, nameof(dynamicFieldsJson));

        try
        {
            logger.LogDebug("Extracting data from dynamic fields: {DynamicFieldsJson}", dynamicFieldsJson);

            T? data;
            if (dynamicFieldsJson.Trim().StartsWith("\""))
            {
                string? encodedString = JsonConvert.DeserializeObject<string>(dynamicFieldsJson);
                logger.LogDebug("Decoded dynamic fields: {EncodedString}", encodedString);
                GuardClause.IsNullOrEmptyStringOrWhiteSpace(encodedString, nameof(encodedString));
                data = JsonConvert.DeserializeObject<T>(encodedString);
            }
            else
            {
                data = JsonConvert.DeserializeObject<T>(dynamicFieldsJson);
            }

            GuardClause.ArgumentIsNotNull(data, nameof(data));
            logger.LogInformation("Extracted data: {Data}", data);

            return data;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the data from dynamic fields.");
            throw;
        }
    }

    public string ExtractRequestOrigin(string dynamicFieldsJson)
    {
        return string.Empty;
    }
}