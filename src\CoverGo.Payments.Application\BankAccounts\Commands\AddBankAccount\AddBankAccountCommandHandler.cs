using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.BankAccount;

namespace CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;

public record AddBankAccountCommand : ICommand<BankAccount>
{
    public required string Country { get; init; }
    public required string Currency { get; init; }
    public required string AccountHolderName { get; init; }
    public required string BankName { get; init; }
    public required string Bic { get; init; }
    public required List<BankAccountUsage> UsedFor { get; init; }
    public required string PayorId { get; init; }
    public required PayorType PayorType { get; init; }
    public required string EntityId { get; init; }
    public string? AccountNumber { get; init; } = null;
    public string? Iban { get; init; } = null;
}

public class AddBankAccountCommandHandler(
        IRepository<BankAccount, string> bankAccountRepository) : ICommandHandler<AddBankAccountCommand, BankAccount>
{

    public async Task<BankAccount> Handle(AddBankAccountCommand request, CancellationToken cancellationToken)
    {
        // Create the aggregate
        var bankAccount = new BankAccount
        {
            Country = request.Country,
            Currency = request.Currency,
            AccountHolderName = request.AccountHolderName,
            BankName = request.BankName,
            Bic = request.Bic,
            UsedFor = request.UsedFor,
            PayorId = request.PayorId,
            PayorType = request.PayorType,
            EntityId = request.EntityId,
            AccountNumber = request.AccountNumber,
            Iban = request.Iban
        };

        // Save to repository
        bankAccount = await bankAccountRepository.InsertAsync(bankAccount, cancellationToken);

        // Return aggregate directly
        return bankAccount;
    }
} 