﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.InitializePayment
{
    public class InitializePaymentCommandValidator : AbstractValidator<InitializePaymentCommand>
    {
        public InitializePaymentCommandValidator(
            ILogger<InitializePaymentCommandValidator> logger)
        {
            When(pc => pc.InitializationToken == null, () =>
            {
                RuleFor(pc => pc.CurrencyDesc).NotEmpty().WithMessage("No currency description found.");
                RuleFor(pc => pc.CurrencyCode).NotEmpty().WithMessage("No currency code found.");
                RuleFor(pc => pc.Amount).NotNull().GreaterThan(decimal.Zero).WithMessage("Amount should be greater than 0.");
                RuleFor(pc => pc.DecimalPrecision).NotNull().GreaterThan(0).WithMessage("DecimalPrecision should be greater than 0.");
                RuleFor(pc => pc.PaymentProvider).NotEmpty().WithMessage("No payment provider found.");
                RuleFor(pc => pc.PayorId).NotEmpty().WithMessage("No payor Id found.");
                RuleFor(pc => pc.PolicyId).NotEmpty().WithMessage("No policy Id found.");
                RuleFor(pc => pc.InvoiceNumber).NotEmpty().WithMessage("No invoice number found.");
            });

            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
