﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.FinalizePayment
{
    public class FinalizePaymentCommandValidator : AbstractValidator<FinalizePaymentCommand>
    {
        public FinalizePaymentCommandValidator(
            ILogger<FinalizePaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PaymentId).NotEmpty().WithMessage("No paymentId found.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
