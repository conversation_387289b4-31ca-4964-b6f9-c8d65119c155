﻿using CoverGo.Payments.Infrastructure.Payments.PayU.Helpers;
using FluentAssertions;

namespace CoverGo.Payments.UnitTests.Payments.PayU.Helpers;

public class CardHelperTests
{
    [Theory]
    [InlineData("2023-08-31", 8, 2023)]
    [InlineData("2024-12-15", 12, 2024)]
    [InlineData("2021-01-01", 1, 2021)]
    public void ExtractExpiryMonthAndYear_ShouldReturnCorrectMonthAndYear(string cardExpirationDate, int expectedMonth, int expectedYear)
    {
        // Act
        CardHelper.ExtractExpiryMonthAndYear(cardExpirationDate, out int expiryMonth, out int expiryYear);

        // Assert
        expiryMonth.Should().Be(expectedMonth);
        expiryYear.Should().Be(expectedYear);
    }

    [Fact]
    public void ExtractExpiryMonthAndYear_ShouldSetMonthAndYearToZero_WhenCardExpirationDateIsNull()
    {
        // Act
        CardHelper.ExtractExpiryMonthAndYear(null, out int expiryMonth, out int expiryYear);

        // Assert
        expiryMonth.Should().Be(0);
        expiryYear.Should().Be(0);
    }

    [Theory]
    [InlineData("invalid-date")]
    [InlineData("2022-15-10")]
    [InlineData("2022-10-32")]
    public void ExtractExpiryMonthAndYear_ShouldThrowArgumentException_WhenDateIsInvalid(string invalidDate)
    {
        // Act
        Action act = () => CardHelper.ExtractExpiryMonthAndYear(invalidDate, out int _, out int _);

        // Assert
        act.Should().Throw<ArgumentException>()
            .WithMessage("Invalid date format. Expected format is yyyy-MM-dd.*")
            .And.ParamName.Should().Be("cardExpirationDate");
    }
}