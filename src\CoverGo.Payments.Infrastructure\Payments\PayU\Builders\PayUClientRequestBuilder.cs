﻿using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using CoverGo.Payments.Application.Payments;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Builders
{
    public static class PayUClientRequestBuilder
    {
        #region Constants

        private const string ContentTypeJson = "application/json";
        private const string MerchantHeaderKey = "X-Header-Merchant";
        private const string DateHeaderKey = "X-Header-Date";
        private const string SignatureHeaderKey = "X-Header-Signature";
        private const string IdempotencyKeyHeaderKey = "X-Header-Idempotency-Key";
        private const string AcceptHeaderKey = "Accept";
        private const string DateTimeFormat = "yyyy-MM-ddTHH:mm:ssK";

        #endregion


        #region Public Methods

        public static HttpRequestMessage BuildRequestMessage(Uri url, HttpMethod method, string merchantCode,
            string secretKey, object? content, IDateTimeProvider dateTimeProvider)
        {
            GuardClauses.GuardClause.ArgumentIsNotNull(url, nameof(url));
            GuardClauses.GuardClause.ArgumentIsNotNull(method, nameof(method));
            GuardClauses.GuardClause.ArgumentIsNotNull(dateTimeProvider, nameof(dateTimeProvider));
            GuardClauses.GuardClause.IsNullOrEmptyStringOrWhiteSpace(merchantCode, nameof(merchantCode));
            GuardClauses.GuardClause.ArgumentIsNotNull(secretKey, nameof(secretKey));

            HttpRequestMessage message = InitializeHttpRequestMessage(url, method);
            string requestDate = GetCurrentUtcDate(dateTimeProvider);
            string idempotencyKey = GenerateIdempotencyKey();

            ConfigureRequestHeaders(message, merchantCode, requestDate, idempotencyKey);
            string payloadMd5 = SerializeContentAndAddToMessage(message, content);
            string signature = GenerateSignature(method, merchantCode, requestDate, url, payloadMd5, secretKey);

            message.Headers.Add(SignatureHeaderKey, signature);
            return message;
        }

        #endregion


        #region Private Helper Methods

        private static HttpRequestMessage InitializeHttpRequestMessage(Uri url, HttpMethod method) =>
            new(method, url) { Headers = { { AcceptHeaderKey, ContentTypeJson } } };

        private static string GetCurrentUtcDate(IDateTimeProvider dateTimeProvider) =>
            dateTimeProvider.NowUtc().ToString(DateTimeFormat, CultureInfo.InvariantCulture);

        private static string GenerateIdempotencyKey()
            => Guid.NewGuid().ToString();

        private static void ConfigureRequestHeaders(HttpRequestMessage message, string merchantCode, string requestDate,
            string idempotencyKey)
        {
            message.Headers.Add(MerchantHeaderKey, merchantCode);
            message.Headers.Add(DateHeaderKey, requestDate);
            message.Headers.Add(IdempotencyKeyHeaderKey, idempotencyKey);
        }

        private static string SerializeContentAndAddToMessage(HttpRequestMessage message, object? content)
        {
            string serializedContent = string.Empty;
            if (content == null)
            {
                return ComputeMd5(serializedContent);
            }

            serializedContent = JsonConvert.SerializeObject(content);
            message.Content = new StringContent(serializedContent);
            message.Content.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(ContentTypeJson);

            return ComputeMd5(serializedContent);
        }

        private static string GenerateSignature(HttpMethod method, string merchantCode, string date, Uri url,
            string payloadMd5, string secretKey)
        {
            string basePath = url.AbsolutePath;
            string queryString = url.Query;
            string signatureData = $"{merchantCode}{date}{method}{basePath}{queryString}{payloadMd5}";
            return ComputeHmacSha256(secretKey, signatureData);
        }

        private static string ComputeMd5(string input)
        {
            byte[] inputBytes = Encoding.UTF8.GetBytes(input);
            byte[] hashBytes = MD5.HashData(inputBytes);
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
        }

        private static string ComputeHmacSha256(string key, string data)
        {
            using var hmacSha256 = new HMACSHA256(Encoding.UTF8.GetBytes(key));
            byte[] hashBytes = hmacSha256.ComputeHash(Encoding.UTF8.GetBytes(data));
            return BitConverter.ToString(hashBytes).Replace("-", "").ToLowerInvariant();
        }

        #endregion
    }
}