﻿using FluentValidation;

namespace CoverGo.Payments.Application.Common;

public class AggregatesQueryValidator<TAggregatesQuery, TWhere> : AbstractValidator<TAggregatesQuery>
where TAggregatesQuery : PagedQuery<TWhere>
{
    public const int MaxItems = 100;
    private const int Zero = 0;

    public AggregatesQueryValidator()
    {
        When(x => x?.Skip.HasValue == true, () => RuleFor(x => x.Skip)
            .GreaterThanOrEqualTo(Zero).WithMessage($"skip must be greater than or equal to {Zero}")
        );
        When(x => x?.Take.HasValue == true, () => RuleFor(x => x.Take)
            .GreaterThan(Zero).WithMessage($"take must be greater than {Zero}")
            .LessThanOrEqualTo(MaxItems).WithMessage($"take must be less than or equals to {MaxItems}")
        );
    }
}
