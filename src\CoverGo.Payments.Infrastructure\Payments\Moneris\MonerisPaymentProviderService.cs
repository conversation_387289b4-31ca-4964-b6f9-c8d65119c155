using System.Net.Http.Json;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments.Moneris.Models;
using GuardClauses;
using Microsoft.Extensions.Logging;
using Moneris;
using Newtonsoft.Json;
using Address = CoverGo.Payments.Domain.Payment.Address;
using Receipt = Moneris.Receipt;
using Response = CoverGo.Payments.Infrastructure.Payments.Moneris.Models.Response;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris;

public class MonerisPaymentProviderService(
    ILogger<MonerisPaymentProviderService> logger,
    IHttpClientFactory httpClientFactory) : BasePaymentProviderService(logger)
{
    public override PaymentProvider Type => PaymentProvider.Moneris;

    #region GetPreProcessRedirectUrlAsync

    public override async Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        return payment.IsUpdate switch
        {
            true => GetUpdatePreProcessRedirectUrl(payment),
            _ => await GetInitialPreProcessRedirectUrlAsync(payment, cancellationToken)
        };
    }

    private RedirectUrlOutput GetUpdatePreProcessRedirectUrl(PaymentAggregate payment)
    {
        try
        {
            logger.LogInformation("Starting pre-process for preauthPayment ID: {PaymentId}", payment.Id);

            MonerisPspSettingsAggregate? pspSettings = GetPspSettings<MonerisPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            if (payment.Money.PaymentCurrencyCode != MonerisPspSettingsAggregate.CurrencyCode)
                throw new DomainException("Unsupported currency.");

            logger.LogInformation("Pre-process completed for preauthPayment ID: {PaymentId}", payment.Id);

            return new RedirectUrlOutput(
                new Uri($"{pspSettings!.ApiUrl}/HPPtoken/index.php?id={pspSettings.ProfileId}"),
                new Dictionary<string, string>
                {
                    { "apiUrl", pspSettings.ApiUrl }, { "environment", pspSettings.Environment }
                });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-process for preauthPayment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private async Task<RedirectUrlOutput> GetInitialPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        try
        {
            logger.LogInformation("Starting pre-process for preauthPayment ID: {PaymentId}", payment.Id);

            MonerisPspSettingsAggregate? pspSettings = GetPspSettings<MonerisPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            if (payment.Money.PaymentCurrencyCode != MonerisPspSettingsAggregate.CurrencyCode)
            {
                throw new DomainException("Unsupported currency.");
            }

            object preloadRequest = CreatePreloadRequest(payment, pspSettings);

            ResponseData preloadResponse =
                await SendPreloadRequestAsync(preloadRequest, pspSettings.RedirectUrl, cancellationToken);

            if (!preloadResponse.IsSuccess) HandlePreloadFailure(payment, preloadResponse);

            logger.LogInformation("Pre-process completed for preauthPayment ID: {PaymentId}", payment.Id);

            return new RedirectUrlOutput(null,
                new Dictionary<string, string>
                {
                    { "ticket", preloadResponse.Ticket }, { "environment", pspSettings.Environment }
                });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-process for preauthPayment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private static object CreatePreloadRequest(PaymentAggregate payment, MonerisPspSettingsAggregate pspSettings) =>
        new
        {
            store_id = pspSettings.StoreId,
            api_token = pspSettings.ApiToken,
            checkout_id = pspSettings.CheckoutId,
            txn_total = payment.Money.PaymentAmount.ToString("F2"),
            environment = pspSettings.Environment,
            action = "preload",
            order_no = payment.InternalReference,
            ask_cvv = "Y"
        };

    private async Task<ResponseData> SendPreloadRequestAsync(object preloadRequest, string redirectUrl,
        CancellationToken cancellationToken)
    {
        using HttpClient httpClient = httpClientFactory.CreateClient();

        HttpResponseMessage response =
            await httpClient.PostAsJsonAsync($"{redirectUrl}/request/request.php", preloadRequest, cancellationToken);

        string respStr = await response.Content.ReadAsStringAsync(cancellationToken);
        logger.LogDebug("Received response string: {RespStr}", respStr);

        Response? preloadResponse = JsonConvert.DeserializeObject<Response>(respStr);
        GuardClause.ArgumentIsNotNull(preloadResponse, nameof(preloadResponse));
        GuardClause.ArgumentIsNotNull(preloadResponse.ResponseData, nameof(preloadResponse.ResponseData));

        return preloadResponse.ResponseData;
    }

    private static void HandlePreloadFailure(PaymentAggregate payment, ResponseData preloadResponse)
    {
        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money,
            GetErrorAsString(preloadResponse.Error));
        throw new DomainException("Failed to preload Moneris checkout.");
    }

    private static string GetErrorAsString(Dictionary<string, object>? error) 
        => error != null ? JsonConvert.SerializeObject(error) : string.Empty;

    #endregion


    #region FinalizePaymentAsync

    public override async Task FinalizePaymentAsync(PreauthPaymentAggregate payment,
        PreauthPaymentAggregate? prevPayment,
        JsonElement? dynamicFields, CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(dynamicFields, nameof(dynamicFields));
        logger.LogInformation("Finalizing preauthPayment with ID: {PaymentId}", payment.Id);

        if (payment.IsUpdate)
        {
            await FinalizeUpdatePaymentAsync(payment, prevPayment, dynamicFields, cancellationToken);
        }
        else
        {
            await FinalizeInitialPaymentAsync(payment, dynamicFields, cancellationToken);
        }
    }

    private async Task FinalizeUpdatePaymentAsync(PreauthPaymentAggregate payment, PreauthPaymentAggregate? prevPayment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken)
    {
        try
        {
            string token = ExtractToken(dynamicFields);

            MonerisPspSettingsAggregate? pspSettings =
                GetPspSettings<MonerisPspSettingsAggregate>(prevPayment!.PspSettings);

            ResAddToken resAddTokenRequest = CreateResAddTokenRequest(token);

            var mpgReq = new HttpsPostRequest();
            mpgReq.SetProcCountryCode(MonerisPspSettingsAggregate.ProcessingCountryCode);
            mpgReq.SetTestMode(pspSettings!.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase));
            mpgReq.SetStoreId(pspSettings.StoreId);
            mpgReq.SetApiToken(pspSettings.ApiToken);
            mpgReq.SetTransaction(resAddTokenRequest);
            mpgReq.SetStatusCheck(false);

            await Task.Run(() => mpgReq.Send(), cancellationToken);

            Receipt? receipt = mpgReq.GetReceipt();

            PaymentMoney paymentMoney = MapAllToPaymentAndGetMoney(payment, prevPayment, receipt);

            string responseCode = receipt.GetResponseCode();
            if (!IsSuccessfulResponse(responseCode))
            {
                string? resultText = receipt.GetMessage();
                string result = $"{responseCode} - {resultText}".Trim();

                logger.LogWarning("FinalizeUpdatePaymentAsync: finalizing fail  with invoice: {invoiceNumber}, response: {response}", payment.InvoiceNumber, result);

                payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, paymentMoney, result);
                return;
            }

            payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, paymentMoney);

            logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finalizing preauthPayment with ID: {PaymentId}", payment.Id);
            payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, ex.Message);
            throw;
        }
    }

    private async Task FinalizeInitialPaymentAsync(PreauthPaymentAggregate payment, JsonElement? dynamicFields,
        CancellationToken cancellationToken)
    {
        try
        {
            string ticket = ExtractTicket(dynamicFields);
            payment.SetTicketId(ticket);
            ResponseData responseData = await FetchReceiptAsync(payment, ticket, cancellationToken);
            PaymentMoney paymentMoney = MapReceiptToPaymentAndGetMoney(payment, responseData);

            string? responseCode = responseData.Receipt?.Cc?.ResponseCode;
            if (!IsSuccessfulResponse(responseCode))
            {
                string? resultText = responseData.Receipt?.Cc?.Result; 
                string result = $"{responseCode} - {resultText}".Trim();

                logger.LogWarning("FinalizeInitialPaymentAsync: finalizing fail  with invoice: {invoiceNumber}, response: {response}", payment.InvoiceNumber, result);
             
                payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, paymentMoney, result);
                return;
            }

            payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, paymentMoney);

            logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finalizing preauthPayment with ID: {PaymentId}", payment.Id);
            payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, ex.Message);
            throw;
        }
    }

    private string ExtractTicket(JsonElement? dynamicFields)
    {
        GuardClause.ArgumentIsNotNull(dynamicFields, nameof(dynamicFields));

        try
        {
            ResponseData? response = ParseDynamicFields<ResponseData>(dynamicFields.Value.GetRawText());
            GuardClause.ArgumentIsNotNull(response, nameof(response));

            if (!string.IsNullOrEmpty(response.Ticket))
            {
                logger.LogInformation("Extracted ticket: {Ticket}", response.Ticket);
                return response.Ticket;
            }

            logger.LogError("The ticket property is null or empty.");
            throw new ArgumentException("The ticket property is null or empty.", nameof(dynamicFields));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the ticket from dynamic fields.");
            throw;
        }
    }

    private string ExtractToken(JsonElement? dynamicFields)
    {
        GuardClause.ArgumentIsNotNull(dynamicFields, nameof(dynamicFields));

        try
        {
            ResponseData? response = ParseDynamicFields<ResponseData>(dynamicFields.Value.GetRawText());
            GuardClause.ArgumentIsNotNull(response, nameof(response));

            if (!string.IsNullOrEmpty(response.Token))
            {
                logger.LogInformation("Extracted token: {Token}", response.Token);
                return response.Token;
            }

            logger.LogError("The token property is null or empty.");
            throw new ArgumentException("The token property is null or empty.", nameof(dynamicFields));
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the token from dynamic fields.");
            throw;
        }
    }

    private async Task<ResponseData> FetchReceiptAsync(PaymentAggregate payment, string? ticket,
        CancellationToken cancellationToken)
    {
        MonerisPspSettingsAggregate? pspSettings = GetPspSettings<MonerisPspSettingsAggregate>(payment.PspSettings);
        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

        try
        {
            logger.LogInformation("Fetching receipt for capturePayment with ID: {PaymentId}", payment.Id);
            ResponseData responseData = await RequestReceiptAsync(pspSettings, ticket, cancellationToken);
            GuardClause.ArgumentIsNotNull(responseData, nameof(responseData));

            logger.LogInformation("Receipt fetched successfully for capturePayment with ID: {PaymentId}", payment.Id);
            return responseData;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error fetching receipt for capturePayment with ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private async Task<ResponseData> RequestReceiptAsync(MonerisPspSettingsAggregate pspSettings, string? ticket,
        CancellationToken cancellationToken)
    {
        var requestBody = new
        {
            store_id = pspSettings.StoreId,
            api_token = pspSettings.ApiToken,
            checkout_id = pspSettings.CheckoutId,
            ticket,
            environment = pspSettings.Environment,
            action = "receipt"
        };

        using HttpClient httpClient = httpClientFactory.CreateClient();
        HttpResponseMessage response =
            await httpClient.PostAsJsonAsync($"{pspSettings.RedirectUrl}/request/request.php", requestBody,
                cancellationToken);
        response.EnsureSuccessStatusCode();

        string responseString = await response.Content.ReadAsStringAsync(cancellationToken);
        return JsonConvert.DeserializeObject<Response>(responseString)?.ResponseData ??
               throw new InvalidOperationException("Failed to deserialize receipt.");
    }

    private static PaymentMoney MapAllToPaymentAndGetMoney(PreauthPaymentAggregate payment,
        PreauthPaymentAggregate prevPayment, Receipt? receipt)
    {
        GuardClause.ArgumentIsNotNull(receipt, nameof(receipt));
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(prevPayment, nameof(prevPayment));
        
        payment.SetPayerData(prevPayment.PayerData!);

        PSPBearerPseudoCC initialBearer = CreateInitialBearer(receipt);
        payment.SetInitialBearer(initialBearer);

        return payment.Money;
    }

    private static PaymentMoney MapReceiptToPaymentAndGetMoney(PaymentAggregate payment, ResponseData responseData)
    {
        GuardClause.ArgumentIsNotNull(responseData, nameof(responseData));
        GuardClause.ArgumentIsNotNull(responseData.Receipt, nameof(responseData.Receipt));
        GuardClause.ArgumentIsNotNull(responseData.Receipt.Cc, nameof(responseData.Receipt.Cc));

        CreditCardReceipt? receipt = responseData.Receipt.Cc;

        payment.AssignProviderTransaction(receipt.TransactionNo);
        payment.SetExternalReference(receipt.ReferenceNo);

        PayerData payerData = CreatePayerData(responseData);
        payment.SetPayerData(payerData);

        PSPBearerPseudoCC initialBearer = CreateInitialBearer(receipt, responseData.Request);
        payment.SetInitialBearer(initialBearer);

        PaymentMoney paymentMoney = CreateMoney(payment, receipt);

        return paymentMoney;
    }

    private static PaymentMoney CreateMoney(PaymentAggregate payment, CreditCardReceipt receipt)
    {
        PaymentMoney paymentMoney = payment.Money;

        if (receipt.Mcp == null) return paymentMoney;

        var mcpDetails = new MCPDetails(
            receipt.Mcp.MerchantSettlementAmount,
            receipt.Mcp.CardholderCurrencyCode,
            receipt.Mcp.McpRate,
            string.IsNullOrWhiteSpace(receipt.Mcp.DecimalPrecision) ? 0 : int.Parse(receipt.Mcp.DecimalPrecision),
            receipt.Mcp.CardholderAmount,
            receipt.Mcp.CardholderCurrencyDesc
        );
        paymentMoney.SetMcpDetails(mcpDetails);

        return paymentMoney;
    }

    private static PayerData CreatePayerData(ResponseData responseData) =>
        new(
            language: null,
            emailAddress: responseData.Request?.CustomerInfo?.Email,
            address: responseData.Request?.Billing != null
                ? new Address(
                    addressLine1: responseData.Request.Billing.AddressLine1,
                    addressLine2: responseData.Request.Billing.AddressLine2,
                    street: null,
                    houseNumber: null,
                    postalCode: responseData.Request.Billing.PostalCode,
                    city: responseData.Request.Billing.City,
                    state: responseData.Request.Billing.Province,
                    country: responseData.Request.Billing.Country
                )
                : null,
            lastName: responseData.Request?.CustomerInfo?.LastName ?? string.Empty,
            firstName: responseData.Request?.CustomerInfo?.FirstName ?? string.Empty,
            externalCustomerId: null,
            companyName: null,
            phoneNumber: responseData.Request?.CustomerInfo?.Phone
        );

    private static PSPBearerPseudoCC CreateInitialBearer(CreditCardReceipt receipt, Request request)
    {
        GuardClause.ArgumentIsNotNull(receipt, nameof(receipt));
        GuardClause.ArgumentIsNotNull(request, nameof(request));

        int? expiryMonth = int.TryParse(receipt.ExpiryDate?.Substring(0, 2), out int month) ? month : null;
        int? expiryYear = int.TryParse(receipt.ExpiryDate?.Substring(2, 2), out int year) ? year + 2000 : null;

        return new PSPBearerPseudoCC
        {
            Token = receipt.Tokenize?.DataKey,
            PseudoCardPan = receipt.First6Last4,
            CardType = receipt.CardType,
            Country = null,
            ExpiryMonth = expiryMonth,
            ExpiryYear = expiryYear,
            Holder = request.Cc.Cardholder,
            TruncatedCardPan = receipt.First6Last4,
            OrderId = receipt.OrderNo,
            IssuerId = receipt.IssuerId
        };
    }
    
    private static PSPBearerPseudoCC CreateInitialBearer(Receipt receipt)
    {
        GuardClause.ArgumentIsNotNull(receipt, nameof(receipt));

        int? expiryMonth = int.TryParse(receipt.GetResExpDate()?.Substring(2, 2), out int month) ? month : null;
        int? expiryYear = int.TryParse(receipt.GetResExpDate()?.Substring(0, 2), out int year) ? year + 2000 : null;

        return new PSPBearerPseudoCC
        {
            Token = receipt.GetDataKey(),
            PseudoCardPan = receipt.GetResDataMaskedPan(),
            CardType = receipt.GetCardType(),
            Country = null,
            ExpiryMonth = expiryMonth,
            ExpiryYear = expiryYear,
            TruncatedCardPan = receipt.GetResDataMaskedPan()
        };
    }

    #endregion


    #region CancelPreauthPaymentAsync

    public override async Task<PreauthPaymentAggregate> CancelPreauthPaymentAsync(
        PreauthPaymentAggregate preauthPayment,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(preauthPayment, nameof(preauthPayment));

        if (preauthPayment.Status == PaymentStatus.Prepared)
        {
            preauthPayment.SetPreauthStatus(PreauthPaymentStatus.Cancelled);
            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Canceled, preauthPayment.Money);

            logger.LogInformation(
                "Pre-authorization cancellation for preauthPayment ID: {PaymentId} completed successfully",
                preauthPayment.Id);

            return preauthPayment;
        }

        try
        {
            logger.LogInformation("Starting pre-authorization cancellation for preauthPayment ID: {PaymentId}",
                preauthPayment.Id);

            MonerisPspSettingsAggregate? pspSettings =
                GetPspSettings<MonerisPspSettingsAggregate>(preauthPayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            Completion cancelRequest = CreateCancelRequest(preauthPayment);

            var mpgReq = new HttpsPostRequest();
            mpgReq.SetProcCountryCode(MonerisPspSettingsAggregate.ProcessingCountryCode);
            mpgReq.SetTestMode(pspSettings.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase));
            mpgReq.SetStoreId(pspSettings.StoreId);
            mpgReq.SetApiToken(pspSettings.ApiToken);
            mpgReq.SetTransaction(cancelRequest);
            mpgReq.SetStatusCheck(false);

            await Task.Run(() => mpgReq.Send(), cancellationToken);

            Receipt? receipt = mpgReq.GetReceipt();
            ProcessCancelResponse(receipt, preauthPayment);

            logger.LogInformation(
                "Pre-authorization cancellation for preauthPayment ID: {PaymentId} completed successfully",
                preauthPayment.Id);
            return preauthPayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing pre-authorization cancellation for preauthPayment ID: {PaymentId}",
                preauthPayment.Id);
            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money, ex.Message);
            throw;
        }
    }

    private static Completion CreateCancelRequest(PaymentAggregate preauthPayment)
    {
        var cancel = new Completion();
        cancel.SetOrderId((preauthPayment.InitialBearer as PSPBearerPseudoCC)!.OrderId);
        cancel.SetCompAmount("0.00");
        cancel.SetTxnNumber(preauthPayment.ProviderPaymentId);
        cancel.SetCryptType("7");

        return cancel;
    }

    private static void ProcessCancelResponse(Receipt receipt, PreauthPaymentAggregate preauthPayment)
    {
        string responseCode = receipt.GetResponseCode();

        if (!IsSuccessfulResponse(responseCode))
        {
            string error = receipt.GetMessage();
            preauthPayment.SetPreauthStatus(PreauthPaymentStatus.CancelFailed);
            throw new DomainException($"Pre-authorization cancellation request failed: {error}");
        }

        preauthPayment.SetPreauthStatus(PreauthPaymentStatus.Cancelled);
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Canceled, preauthPayment.Money, "Canceled by PSP.");
    }

    #endregion


    public override async Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
    {
        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, "Failed by PSP.");
        await Task.CompletedTask;
    }


    #region RefundAsync

    public override async Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentRefund, nameof(paymentRefund));

        try
        {
            logger.LogInformation("Starting refund for payment ID: {PaymentId}", payment.Id);

            MonerisPspSettingsAggregate? pspSettings = GetPspSettings<MonerisPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            Refund refundRequest = CreateRefundRequest(paymentRefund, payment);

            var mpgReq = new HttpsPostRequest();
            mpgReq.SetProcCountryCode(MonerisPspSettingsAggregate.ProcessingCountryCode);
            mpgReq.SetTestMode(pspSettings.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase));
            mpgReq.SetStoreId(pspSettings.StoreId);
            mpgReq.SetApiToken(pspSettings.ApiToken);
            mpgReq.SetTransaction(refundRequest);
            mpgReq.SetStatusCheck(false);

            await Task.Run(() => mpgReq.Send(), cancellationToken);

            Receipt receipt = mpgReq.GetReceipt();
            ProcessRefundResponse(receipt, paymentRefund);

            logger.LogInformation("Refund for payment ID: {PaymentId} completed successfully", payment.Id);
            return paymentRefund;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing refund for payment ID: {PaymentId}", payment.Id);
            paymentRefund.SetStatus(RefundStatus.Failed, ex.Message);
            throw;
        }
    }

    private static Refund CreateRefundRequest(RefundAggregate paymentRefund, PaymentAggregate payment)
    {
        var refund = new Refund();
        refund.SetTxnNumber(payment.ProviderPaymentId);
        refund.SetOrderId((payment.InitialBearer as PSPBearerPseudoCC)?.OrderId);
        refund.SetAmount(paymentRefund.Money.PaymentAmount.ToString("F2"));
        refund.SetCryptType("7");

        return refund;
    }

    private static void ProcessRefundResponse(Receipt receipt, RefundAggregate paymentRefund)
    {
        string responseCode = receipt.GetResponseCode();
        if (!IsSuccessfulResponse(responseCode))
        {
            string error = receipt.GetMessage();
            paymentRefund.SetStatus(RefundStatus.Failed, error);
            throw new DomainException($"Refund request failed: {error}");
        }

        string? transactionId = receipt.GetTransactionId();
        if (string.IsNullOrWhiteSpace(transactionId))
        {
            transactionId = receipt.GetResult()["TransID"]?.ToString();
        }

        paymentRefund.AssignProviderTransaction(transactionId);

        paymentRefund.SetStatus(RefundStatus.Succeeded);
    }

    #endregion


    #region CapturePaymentAsync

    public override async Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
        string providerPaymentId,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(capturePayment, nameof(capturePayment));

        try
        {
            logger.LogInformation("Starting capture for capturePayment ID: {PaymentId}", capturePayment.Id);

            MonerisPspSettingsAggregate? pspSettings =
                GetPspSettings<MonerisPspSettingsAggregate>(capturePayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            Completion captureRequest = CreateCaptureRequest(capturePayment, providerPaymentId);

            var mpgReq = new HttpsPostRequest();
            mpgReq.SetProcCountryCode(MonerisPspSettingsAggregate.ProcessingCountryCode);
            mpgReq.SetTestMode(pspSettings.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase));
            mpgReq.SetStoreId(pspSettings.StoreId);
            mpgReq.SetApiToken(pspSettings.ApiToken);
            mpgReq.SetTransaction(captureRequest);
            mpgReq.SetStatusCheck(false);

            await Task.Run(() => mpgReq.Send(), cancellationToken);

            Receipt? receipt = mpgReq.GetReceipt();
            ProcessCaptureResponse(receipt, capturePayment);

            logger.LogInformation("Capture for capturePayment ID: {PaymentId} completed successfully",
                capturePayment.Id);
            return capturePayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing capture for capturePayment ID: {PaymentId}", capturePayment.Id);
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, ex.Message);
            throw;
        }
    }

    private static ResAddToken CreateResAddTokenRequest(string token)
    {
        var resAddTokenRequest = new ResAddToken();
        resAddTokenRequest.SetDataKey(token);
        resAddTokenRequest.SetCryptType("7");
        return resAddTokenRequest;
    }

    private static Completion CreateCaptureRequest(PaymentAggregate capturePayment, string providerPaymentId)
    {
        var capture = new Completion();
        capture.SetOrderId((capturePayment.InitialBearer as PSPBearerPseudoCC)?.OrderId);
        capture.SetCompAmount(capturePayment.Money.PaymentAmount.ToString("F2"));
        capture.SetTxnNumber(providerPaymentId);
        capture.SetCryptType("7");

        return capture;
    }

    private static void ProcessCaptureResponse(Receipt receipt, PaymentAggregate capturePayment)
    {
        string responseCode = receipt.GetResponseCode();

        if (!IsSuccessfulResponse(responseCode))
        {
            string error = receipt.GetMessage();
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, error);
            throw new DomainException($"Capture request failed: {error}");
        }

        string? transactionId = receipt.GetTransactionId();
        if (string.IsNullOrWhiteSpace(transactionId))
        {
            transactionId = receipt.GetResult()["TransID"]?.ToString();
        }

        capturePayment.AssignProviderTransaction(transactionId);

        capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, capturePayment.Money);
    }

    #endregion


    #region RecurringPaymentAsync

    public override async Task<RecurringPaymentAggregate> RecurringPaymentAsync(
        RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(recurringPayment, nameof(recurringPayment));

        try
        {
            logger.LogInformation("Starting recurring payment for payment ID: {PaymentId}", recurringPayment.Id);

            MonerisPspSettingsAggregate? pspSettings =
                GetPspSettings<MonerisPspSettingsAggregate>(recurringPayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            ResPurchaseCC recurringRequest = CreateRecurringRequest(recurringPayment);

            var mpgReq = new HttpsPostRequest();
            mpgReq.SetProcCountryCode(MonerisPspSettingsAggregate.ProcessingCountryCode);
            mpgReq.SetTestMode(pspSettings.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase));
            mpgReq.SetStoreId(pspSettings.StoreId);
            mpgReq.SetApiToken(pspSettings.ApiToken);
            mpgReq.SetTransaction(recurringRequest);
            mpgReq.SetStatusCheck(false);

            await Task.Run(() => mpgReq.Send(), cancellationToken);

            Receipt? receipt = mpgReq.GetReceipt();
            ProcessRecurringResponse(receipt, recurringPayment);

            logger.LogInformation("Recurring payment for payment ID: {PaymentId} completed successfully",
                recurringPayment.Id);
            return recurringPayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing recurring payment for payment ID: {PaymentId}", recurringPayment.Id);
            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, recurringPayment.Money, ex.Message);
            throw;
        }
    }

    private static ResPurchaseCC CreateRecurringRequest(PaymentAggregate recurringPayment)
    {
        var resPurchaseCC = new ResPurchaseCC();
        resPurchaseCC.SetDataKey((recurringPayment.InitialBearer as PSPBearerPseudoCC)?.Token);
        resPurchaseCC.SetOrderId(recurringPayment.InternalReference);
        resPurchaseCC.SetAmount(recurringPayment.Money.PaymentAmount.ToString("F2"));
        resPurchaseCC.SetCryptType("1");
        resPurchaseCC.SetDynamicDescriptor("Recurring Payment");

        var cof = new CofInfo();
        cof.SetPaymentIndicator("R");
        cof.SetPaymentInformation("2");
        cof.SetIssuerId((recurringPayment.InitialBearer as PSPBearerPseudoCC)?.IssuerId);
        resPurchaseCC.SetCofInfo(cof);

        return resPurchaseCC;
    }

    private static void ProcessRecurringResponse(Receipt receipt, PaymentAggregate recurringPayment)
    {
        string responseCode = receipt.GetResponseCode();

        if (!IsSuccessfulResponse(responseCode))
        {
            string error = receipt.GetMessage();
            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, recurringPayment.Money, error);
            throw new DomainException($"Recurring payment request failed: {error}");
        }

        string? transactionId = receipt.GetTransactionId();
        if (string.IsNullOrWhiteSpace(transactionId))
        {
            transactionId = receipt.GetResult()["TransID"]?.ToString();
        }

        recurringPayment.AssignProviderTransaction(transactionId);

        if (recurringPayment.InitialBearer is PSPBearerPseudoCC initialBearer)
        {
            initialBearer.OrderId = recurringPayment.InternalReference;
            recurringPayment.UpdateInitialBearer(initialBearer);
        }

        recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, recurringPayment.Money);
    }

    #endregion

    #region Public API Methods

    /// <summary>
    /// Public method to fetch receipt data for API endpoints
    /// </summary>
    /// <param name="payment">The payment aggregate</param>
    /// <param name="ticket">The Moneris ticket ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>ResponseData containing receipt information</returns>
    public async Task<ResponseData> GetReceiptDataAsync(PaymentAggregate payment, string ticket,
        CancellationToken cancellationToken = default)
    {
        return await FetchReceiptAsync(payment, ticket, cancellationToken);
    }

    #endregion

    public override
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference, PaymentStatus paymentStatus, decimal
            ? amount, string
            webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(string webhookBody,
            PspSettingsAggregate? pspSettings, CancellationToken cancellationToken = default) =>
        throw new NotImplementedException();

    private static bool IsSuccessfulResponse(string? responseCode)
    {
        if (string.IsNullOrEmpty(responseCode))
        {
            return false;
        }

        if (int.TryParse(responseCode, out int code))
        {
            return code < 50;
        }

        return false;
    }
}