﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment
{
    public class InitializeUpdatePaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<InitializeUpdatePaymentCommandHandler> logger)
        : ICommandHandler<InitializeUpdatePaymentCommand, ProcessInitialPaymentResultDto>
    {
        /// <param name="initializeUpdatePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ProcessInitialPaymentResultDto> Handle(
            InitializeUpdatePaymentCommand initializeUpdatePaymentCommand,
            CancellationToken cancellationToken)
        {
            var initialAmount = GetInitialAmount(initializeUpdatePaymentCommand.PaymentProvider);
            
            logger.LogInformation("InitializeUpdatePaymentCommandHandler.Handle: Starting payment update initialization. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}, CurrencyDesc: {CurrencyDesc}, InitialAmount: {InitialAmount}",
                initializeUpdatePaymentCommand.PolicyId,
                initializeUpdatePaymentCommand.PayorId,
                initializeUpdatePaymentCommand.PaymentProvider,
                initializeUpdatePaymentCommand.CurrencyDesc,
                initialAmount);

            try
            {
                var payment = new PreauthPaymentAggregate(
                    initializeUpdatePaymentCommand.PaymentProvider,
                    new PaymentMoney(
                        CurrencyHelper.GetIsoCode(initializeUpdatePaymentCommand.CurrencyDesc),
                        initializeUpdatePaymentCommand.CurrencyDesc,
                        initialAmount,
                        2
                    ),
                    initializeUpdatePaymentCommand.PolicyId,
                    null,
                    initializeUpdatePaymentCommand.PayorId,
                    null,
                    null,
                    true
                );

                ProcessInitialPaymentResult processInitialPaymentResult =
                    await paymentService.ProcessInitialPaymentAsync(
                        payment,
                        null,
                        cancellationToken
                    );

                ProcessInitialPaymentResultDto? result =
                    mapper.Map<ProcessInitialPaymentResultDto>(processInitialPaymentResult);

                logger.LogInformation("InitializeUpdatePaymentCommandHandler.Handle: Payment update initialization completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}, HasRedirectUrl: {HasRedirectUrl}",
                    processInitialPaymentResult.Payment.Id,
                    processInitialPaymentResult.Payment.PolicyId,
                    processInitialPaymentResult.Payment.PayorId,
                    processInitialPaymentResult.Payment.Status,
                    processInitialPaymentResult.RedirectUrl != null);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "InitializeUpdatePaymentCommandHandler.Handle: Unexpected error during payment update initialization. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                    initializeUpdatePaymentCommand.PolicyId,
                    initializeUpdatePaymentCommand.PayorId,
                    initializeUpdatePaymentCommand.PaymentProvider);
                throw;
            }
        }

        private decimal GetInitialAmount(PaymentProvider paymentProvider)
        {
            var amount = paymentProvider == PaymentProvider.Ing ? 0.05M : 0.00M;
            return amount;
        }
    }
}