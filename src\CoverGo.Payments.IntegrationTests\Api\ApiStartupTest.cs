using System.Net;
using CoverGo.Payments.IntegrationTests.Support;

namespace CoverGo.Payments.IntegrationTests.Api;

[Collection(TestCollectionName.ApiTest)]
public class ApiStartupTest(PaymentsWebApplicationFactory webApplicationFactory)
    : ApiTestBase(webApplicationFactory)
{
    [Fact]
    public async Task Test_Api_Can_Start()
    {
        // Arrange
        HttpClient client = PaymentsHttpClient;

        // Act
        HttpResponseMessage response = await client.GetAsync("/startupz");

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}