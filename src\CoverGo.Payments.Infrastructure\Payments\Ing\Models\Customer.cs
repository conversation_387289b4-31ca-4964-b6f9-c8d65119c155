﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.ING.Models;

public class Customer
{
    [JsonProperty(IngContainer.PropsName.CustomerFirstName, NullValueHandling = NullValueHandling.Ignore)]
    public string FirstName { get; set; }

    [JsonProperty(IngContainer.PropsName.CustomerLastName, NullValueHandling = NullValueHandling.Ignore)]
    public string LastName { get; set; }

    [JsonProperty(IngContainer.PropsName.CustomerId, NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty(IngContainer.PropsName.CustomerEmail, NullValueHandling = NullValueHandling.Ignore)]
    public string Email { get; set; }

    [JsonProperty(IngContainer.PropsName.CustomerPhone, NullValueHandling = NullValueHandling.Ignore)]
    public string Phone { get; set; }
}