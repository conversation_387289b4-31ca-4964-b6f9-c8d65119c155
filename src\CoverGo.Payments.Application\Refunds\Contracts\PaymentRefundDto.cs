﻿using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Refund;

namespace CoverGo.Payments.Application.Refunds.Contracts;

public class PaymentRefundDto
{
    public string Id { get; set; } = null!;
    
    public string PaymentId { get; set; } = null!;

    public RefundStatus Status { get; set; }

    public MoneyDto Money { get; private set; } = null!;

    public DateTime CreatedAtDateUtc { get; set; }

    public string? ProviderPaymentId { get; set; }
}