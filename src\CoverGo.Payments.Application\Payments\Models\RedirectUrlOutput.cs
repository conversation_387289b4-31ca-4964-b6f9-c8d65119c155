﻿using System.Collections.ObjectModel;
using GuardClauses;

namespace CoverGo.Payments.Application.Payments.Models;

public class RedirectUrlOutput
{
    protected RedirectUrlOutput()
    {
            
    }

    public RedirectUrlOutput(Uri? redirectUrl)
    {
        RedirectUrl = redirectUrl;
    }

    public RedirectUrlOutput(Uri? redirectUrl, IDictionary<string, string>? data)
        : this(redirectUrl)
    {
        GuardClause.ArgumentIsNotNull(data, nameof(data));

        Data = new ReadOnlyDictionary<string, string>(data);
    }

    public Uri? RedirectUrl { get; }
        
    public IReadOnlyDictionary<string, string> Data { get; }
}