﻿using CoverGo.Payments.Infrastructure.Payments.Ing.Builders;
using FluentAssertions;

namespace CoverGo.Payments.UnitTests.Payments.Ing.Builders;

public class IngClientUrlBuilderTests
{
    [Fact]
    public void GIVEN_valid_parameters_WHEN_BuildGetPaymentProfileUrl_called_THEN_should_return_correct_url()
    {
        const string baseUrl = "https://api.ing.com";
        const string apiVersion = "v1";
        const string merchantId = "merchant123";
        const string customerId = "customer123";
        
        Uri result = IngClientUrlBuilder.BuildGetPaymentProfileUrl(baseUrl, apiVersion, merchantId, customerId);
        
        result.Should().NotBeNull();
        result.AbsoluteUri.Should().Be("https://api.ing.com/v1/merchant/merchant123/profile/cid/customer123");
    }

    [Fact]
    public void GIVEN_valid_parameters_WHEN_BuildDebitPaymentProfileUrl_called_THEN_should_return_correct_url()
    {
        const string baseUrl = "https://api.ing.com";
        const string apiVersion = "v1";
        const string merchantId = "merchant123";
        
        Uri result = IngClientUrlBuilder.BuildDebitPaymentProfileUrl(baseUrl, apiVersion, merchantId);
        
        result.Should().NotBeNull();
        result.AbsoluteUri.Should().Be("https://api.ing.com/v1/merchant/merchant123/transaction/profile");
    }
    
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void GIVEN_null_or_empty_baseUrl_WHEN_BuildGetPaymentProfileUrl_called_THEN_should_throw_ArgumentException(string invalidBaseUrl)
    {
        const string apiVersion = "v1";
        const string merchantId = "merchant123";
        const string customerId = "customer123";
        
        Action act = () => IngClientUrlBuilder.BuildGetPaymentProfileUrl(invalidBaseUrl, apiVersion, merchantId, customerId);
        
        act.Should().Throw<ArgumentException>();
    }
    
    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void GIVEN_null_or_empty_apiVersion_WHEN_BuildGetPaymentProfileUrl_called_THEN_should_throw_ArgumentException(string invalidApiVersion)
    {
        const string baseUrl = "https://api.ing.com";
        const string merchantId = "merchant123";
        const string customerId = "customer123";
        
        Action act = () => IngClientUrlBuilder.BuildGetPaymentProfileUrl(baseUrl, invalidApiVersion, merchantId, customerId);
        
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void GIVEN_null_or_empty_baseUrl_WHEN_BuildDebitPaymentProfileUrl_called_THEN_should_throw_ArgumentException(string invalidBaseUrl)
    {
        const string apiVersion = "v1";
        const string merchantId = "merchant123";
        
        Action act = () => IngClientUrlBuilder.BuildDebitPaymentProfileUrl(invalidBaseUrl, apiVersion, merchantId);
        
        act.Should().Throw<ArgumentException>();
    }

    [Theory]
    [InlineData(null)]
    [InlineData("")]
    [InlineData("   ")]
    public void GIVEN_null_or_empty_apiVersion_WHEN_BuildDebitPaymentProfileUrl_called_THEN_should_throw_ArgumentException(string invalidApiVersion)
    {
        const string baseUrl = "https://api.ing.com";
        const string merchantId = "merchant123";
        
        Action act = () => IngClientUrlBuilder.BuildDebitPaymentProfileUrl(baseUrl, invalidApiVersion, merchantId);
        
        act.Should().Throw<ArgumentException>();
    }
}