﻿using System.Net;
using CoverGo.Payments.Infrastructure.Payments.Ing.Builders;
using CoverGo.Payments.Infrastructure.Payments.Ing.Configurations;
using CoverGo.Payments.Infrastructure.Payments.Ing.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Polly;
using Polly.Retry;

namespace CoverGo.Payments.Infrastructure.Payments.Ing
{
    public class IngApiHttpCommunicator<T>
        where T : class
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly IngClientSettings _settings;
        private readonly ILogger _logger;
        
        public IngApiHttpCommunicator(IHttpClientFactory clientFactory, IngClientSettings settings, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(settings.FactoryClientName))
                throw new ArgumentException("settings.FactoryClientName must be configured", nameof(settings));

            _clientFactory = clientFactory;
            _settings = settings;
            _logger = logger;
        }
        
        
        public async Task<T?> SendAsync(HttpRequestMessage requestMessage, CancellationToken cancellationToken)
        {
            HttpClient client = _clientFactory.CreateClient(_settings.FactoryClientName);
            return await SendReceiveAsync(requestMessage, client, cancellationToken);
        }
        
        private async Task<T?> SendReceiveAsync(HttpRequestMessage requestMessage, HttpClient client, CancellationToken cancellationToken)
        {
            AsyncRetryPolicy<HttpResponseMessage>? retryPolicy = Policy
                .HandleResult<HttpResponseMessage>(r => 
                    r.StatusCode is HttpStatusCode.InternalServerError 
                        or HttpStatusCode.RequestTimeout 
                        or HttpStatusCode.ServiceUnavailable 
                        or HttpStatusCode.GatewayTimeout
                        or HttpStatusCode.NotFound)
                .WaitAndRetryAsync(
                    _settings.RetryCount,
                    attempt => TimeSpan.FromSeconds(Math.Pow(2, attempt)), 
                    (result, _, attempt, _) =>
                    {
                        _logger.LogWarning("Retry {Attempt} for {Url} failed with status {StatusCode}", attempt, requestMessage.RequestUri, result.Result.StatusCode);
                    });
            try
            {
                HttpResponseMessage response = await retryPolicy.ExecuteAsync(async () =>
                {
                    using HttpRequestMessage httpRequestMessage = await IngClientRequestBuilder.CloneRequestMessage(requestMessage);
                    return await client.SendAsync(httpRequestMessage, cancellationToken);
                });

                if (response.IsSuccessStatusCode || response.StatusCode == HttpStatusCode.Found)
                    return await DeserializeResponseAsync(response, cancellationToken);

                string responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                throw new IngApiException(response.StatusCode, response.ReasonPhrase ?? string.Empty, responseContent);
            }
            catch (Exception ex) when (ex is not IngApiException)
            {
                throw new IngApiException(HttpStatusCode.InternalServerError, "Unexpected error occurred.", ex.Message);
            }
        }

        private static async Task<T?> DeserializeResponseAsync(HttpResponseMessage response, CancellationToken cancellationToken)
        {
            string respStr = await response.Content.ReadAsStringAsync(cancellationToken);
            return JsonConvert.DeserializeObject<T>(respStr);
        }
        
       
    }
}