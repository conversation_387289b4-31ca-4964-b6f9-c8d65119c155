﻿using CoverGo.Payments.Application.Encryption;
using MongoDB.Bson.Serialization;
using MongoDB.Bson;
using MongoDB.Bson.IO;
using System.Collections;
using System.Reflection;
using System.Text.Json;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.Infrastructure.DataAccess
{
    public class EncryptedNestedFieldSerializer<T> : IBsonSerializer<T>
    {
        private readonly IEncryptionService _encryptionService;

        public EncryptedNestedFieldSerializer(IEncryptionService encryptionService)
        {
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
        }

        public Type ValueType => typeof(T);

        public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, T value)
        {
            if (value == null)
            {
                context.Writer.WriteNull();
                return;
            }

            SerializeValue(context, args, value);
        }

        public T Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            var bsonType = context.Reader.GetCurrentBsonType();
            if (bsonType == BsonType.Null)
            {
                context.Reader.ReadNull();
                return default!;
            }

            return (T)DeserializeValue(context, args, typeof(T));
        }

        object IBsonSerializer.Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            return Deserialize(context, args) ?? throw new InvalidOperationException("Deserialization returned null.");
        }

        public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object value)
        {
            Serialize(context, args, (T)value);
        }

        private void SerializeValue(BsonSerializationContext context, BsonSerializationArgs args, object value)
        {
            var valueType = value.GetType();

            if (IsClassType(valueType))
            {
                SerializeClassType(context, args, value);
            }
            else if (IsCollectionType(valueType))
            {
                SerializeCollection(context, args, (IEnumerable)value);
            }
            else
            {
                SerializeUsingDefaultSerializer(context, args, value);
            }
        }

        private void SerializeClassType(BsonSerializationContext context, BsonSerializationArgs args, object value)
        {
            context.Writer.WriteStartDocument();

            foreach (var property in value.GetType().GetProperties())
            {
                var propertyValue = property.GetValue(value);

                if (propertyValue == null)
                    continue;

                context.Writer.WriteName(property.Name);

                if (property.GetCustomAttribute<EncryptedAttribute>() != null)
                {
                    var json = JsonSerializer.Serialize(propertyValue);
                    var encryptedJson = _encryptionService.Encrypt(json);
                    context.Writer.WriteString(encryptedJson);
                }
                else if (IsClassType(property.PropertyType))
                {
                    SerializeClassType(context, args, propertyValue);
                }
                else if (IsCollectionType(property.PropertyType))
                {
                    SerializeCollection(context, args, (IEnumerable)propertyValue);
                }
                else
                {
                    SerializeValue(context, args, propertyValue);
                }
            }

            context.Writer.WriteEndDocument();
        }

        private void SerializeCollection(BsonSerializationContext context, BsonSerializationArgs args, IEnumerable collection)
        {
            context.Writer.WriteStartArray();

            foreach (var item in collection)
            {
                if (item == null)
                {
                    context.Writer.WriteNull();
                    continue;
                }

                SerializeValue(context, args, item);
            }

            context.Writer.WriteEndArray();
        }

        private object DeserializeValue(BsonDeserializationContext context, BsonDeserializationArgs args, Type valueType)
        {
            if (context.Reader.GetCurrentBsonType() == BsonType.Array && IsCollectionType(valueType))
            {
                return DeserializeCollection(context, args, valueType);
            }
            else if (context.Reader.GetCurrentBsonType() == BsonType.Document && IsClassType(valueType))
            {
                return DeserializeClassType(context, args, valueType);
            }
            else
            {
                return DeserializeUsingDefaultSerializer(context, args, valueType);
            }
        }

        private object DeserializeClassType(BsonDeserializationContext context, BsonDeserializationArgs args, Type type)
        {
            var instance = Activator.CreateInstance(type);

            context.Reader.ReadStartDocument();

            while (context.Reader.ReadBsonType() != BsonType.EndOfDocument)
            {
                var propertyName = context.Reader.ReadName();
                var property = type.GetProperty(propertyName);

                if (property == null)
                {
                    context.Reader.SkipValue();
                    continue;
                }

                if (property.GetCustomAttribute<EncryptedAttribute>() != null)
                {
                    var encryptedString = context.Reader.ReadString();
                    var decryptedValue = _encryptionService.Decrypt(encryptedString);
                    var propertyValue = JsonSerializer.Deserialize(decryptedValue, property.PropertyType);
                    property.SetValue(instance, propertyValue);
                }
                else
                {
                    var propertyValue = DeserializeValue(context, args, property.PropertyType);
                    property.SetValue(instance, propertyValue);
                }
            }

            context.Reader.ReadEndDocument();
            return instance!;
        }

        private object DeserializeCollection(BsonDeserializationContext context, BsonDeserializationArgs args, Type collectionType)
        {
            var itemType = collectionType.GetGenericArguments()[0];
            var listType = typeof(List<>).MakeGenericType(itemType);
            var listInstance = (IList)Activator.CreateInstance(listType)!;

            context.Reader.ReadStartArray();

            while (context.Reader.ReadBsonType() != BsonType.EndOfDocument)
            {
                var itemValue = DeserializeValue(context, args, itemType);
                listInstance.Add(itemValue);
            }

            context.Reader.ReadEndArray();
            return listInstance;
        }

        private bool IsClassType(Type type)
        {
            return type.IsClass && type != typeof(string) && !type.IsArray && !type.IsGenericType && type.GetProperties().Length > 0;
        }

        private bool IsCollectionType(Type type)
        {
            return typeof(IEnumerable).IsAssignableFrom(type) && type.IsGenericType;
        }

        private void SerializeUsingDefaultSerializer(BsonSerializationContext context, BsonSerializationArgs args, object value)
        {
            BsonSerializer.Serialize(context.Writer, value.GetType(), value);
        }

        private object DeserializeUsingDefaultSerializer(BsonDeserializationContext context, BsonDeserializationArgs args, Type valueType)
        {
            return BsonSerializer.Deserialize(context.Reader, valueType);
        }
    }
    
}
