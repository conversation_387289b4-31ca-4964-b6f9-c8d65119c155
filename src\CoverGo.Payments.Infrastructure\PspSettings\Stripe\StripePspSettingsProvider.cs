﻿using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;

namespace CoverGo.Payments.Infrastructure.PspSettings.Stripe;

public class StripePspSettingsProvider(IRepository<PspSettingsAggregate, string> pspSettingsRepository) : BasePspSettingsProvider(pspSettingsRepository)
{
    public override PaymentProvider Type => PaymentProvider.Stripe;
}