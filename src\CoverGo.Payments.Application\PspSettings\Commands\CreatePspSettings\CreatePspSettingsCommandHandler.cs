﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.PspSettings.Contracts;
using CoverGo.Payments.Application.PspSettings.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Payments.Application.PspSettings.Commands.CreatePspSettings;

public class CreatePspSettingsCommandHandler(
    IRepository<PspSettingsAggregate, string> pspSettingsRepository,
    ILogger<CreatePspSettingsCommandHandler> logger)
    : ICommandHandler<CreatePspSettingsCommand, ResultDto>
{
    public async Task<ResultDto> Handle(CreatePspSettingsCommand command, CancellationToken cancellationToken)
    {
        logger.LogInformation("Handling CreatePspSettingsCommand for PaymentProvider: {PaymentProvider}", command.PaymentProvider);

        PspSettingsAggregate? existingSettings = await CheckIfSettingsExistAsync(command.PaymentProvider, cancellationToken);
        if (existingSettings != null) return HandleExistingSettings(existingSettings);

        PspSettingsAggregate? pspSettingsAggregate = CreatePspSettingsAggregate(command);
        if (pspSettingsAggregate == null) return HandleUnsupportedPaymentProvider(command);

        pspSettingsAggregate.PaymentProvider = command.PaymentProvider;
        
        return await InsertPspSettingsAggregateAsync(pspSettingsAggregate, cancellationToken);
    }

    private async Task<PspSettingsAggregate?> CheckIfSettingsExistAsync(PaymentProvider paymentProvider, CancellationToken cancellationToken) 
        => (await pspSettingsRepository.FindAllByAsync(psp 
            => psp.PaymentProvider == paymentProvider, cancellationToken)
            ).FirstOrDefault();

    private ResultDto HandleExistingSettings(PspSettingsAggregate existingSettings)
    {
        logger.LogWarning("PSP settings already exist for PaymentProvider: {PaymentProvider}", existingSettings.Id);
        throw new PspSettingsAlreadyExistsException(existingSettings.Id);
    }

    private PspSettingsAggregate? CreatePspSettingsAggregate(CreatePspSettingsCommand command)
    {
        string json = command.PspSettings.GetRawText();
        switch (command.PaymentProvider)
        {
            case PaymentProvider.Fake:
                logger.LogInformation($"Deserializing {nameof(FakePspSettingsAggregate)} from provided settings.");
                return JsonConvert.DeserializeObject<FakePspSettingsAggregate>(json);
            case PaymentProvider.Moneris:
                logger.LogInformation($"Deserializing {nameof(MonerisPspSettingsAggregate)} from provided settings.");
                return JsonConvert.DeserializeObject<MonerisPspSettingsAggregate>(json);
            case PaymentProvider.Stripe:
                logger.LogInformation($"Deserializing {nameof(StripePspSettingsAggregate)} from provided settings.");
                return JsonConvert.DeserializeObject<StripePspSettingsAggregate>(json);
            case PaymentProvider.None:
                logger.LogWarning("PaymentProvider {PaymentProvider} is not implemented.", command.PaymentProvider);
                throw new NotImplementedException($"PaymentProvider {command.PaymentProvider} is not implemented.");
            case PaymentProvider.Walaa:
                logger.LogInformation($"Deserializing {nameof(WalaaPspSettingsAggregate)} from provided settings.");
                return JsonConvert.DeserializeObject<WalaaPspSettingsAggregate>(json);
            case PaymentProvider.PayU:
                logger.LogInformation($"Deserializing {nameof(PayUPspSettingsAggregate)} from provided settings.");
                return JsonConvert.DeserializeObject<PayUPspSettingsAggregate>(json);
            case PaymentProvider.Ing:
                logger.LogInformation($"Deserializing {nameof(IngPspSettingsAggregate)} from provided settings");
                return JsonConvert.DeserializeObject<IngPspSettingsAggregate>(json);
            default:
                logger.LogError("Unknown PaymentProvider: {PaymentProvider}", command.PaymentProvider);
                throw new ArgumentOutOfRangeException(nameof(command.PaymentProvider), command.PaymentProvider, "Unknown PaymentProvider");
        }
    }

    private ResultDto HandleUnsupportedPaymentProvider(CreatePspSettingsCommand command)
    {
        logger.LogWarning("Unsupported PaymentProvider: {PaymentProvider}", command.PaymentProvider);
        return new ResultDto { Success = false };
    }

    private async Task<ResultDto> InsertPspSettingsAggregateAsync(PspSettingsAggregate pspSettingsAggregate, CancellationToken cancellationToken)
    {
        logger.LogInformation("Inserting PspSettingsAggregate into repository.");
        pspSettingsAggregate = await pspSettingsRepository.InsertAsync(pspSettingsAggregate, cancellationToken);
        logger.LogInformation("Successfully inserted PspSettingsAggregate with ID: {Id}", pspSettingsAggregate.Id);
        return new ResultDto { Success = true, Id = pspSettingsAggregate.Id };
    }
}