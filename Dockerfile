﻿FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS restore
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app
COPY . .
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN find ./ -type f -name "*.csproj" -exec dotnet restore {} \;

FROM restore AS build
ARG BUILDCONFIG=Release
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
ARG CI_BUILD=true
ARG GH_ACCOUNT
ARG GH_TOKEN
COPY . .
RUN dotnet build -c "$BUILDCONFIG" --no-restore /p:PackageVersion="$APP_VERSION" /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"


FROM build AS publish
ARG BUILDCONFIG=Release
RUN dotnet publish src/CoverGo.Payments.Api/CoverGo.Payments.Api.csproj -c "$BUILDCONFIG" -o /app --no-build


FROM mcr.microsoft.com/dotnet/aspnet:8.0-alpine AS runtime

ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=0
RUN apk add --no-cache icu-libs
WORKDIR /app
COPY --from=publish /app .

# change openssl conf
RUN mkdir /usr/lib/ssl
RUN touch /usr/lib/ssl/openssl.cnf
RUN sed -i "s/DEFAULT@SECLEVEL=2/DEFAULT@SECLEVEL=1/" /usr/lib/ssl/openssl.cnf

RUN adduser -D buildadmin
RUN chown buildadmin:buildadmin /app /app/*
USER buildadmin
EXPOSE 8080
ARG COMMIT_SHA
ENV SENTRY_RELEASE=${COMMIT_SHA} REVISION=${COMMIT_SHA}
ENV ASPNETCORE_URLS http://*:8080
ENTRYPOINT ["dotnet", "CoverGo.Payments.Api.dll"]

FROM build AS tests
ARG BUILDCONFIG=Release
ENV TESTBUILDCONFIG=$BUILDCONFIG
RUN dotnet tool restore
ENTRYPOINT dotnet test --collect:"XPlat Code Coverage" -c "$TESTBUILDCONFIG" --no-build --verbosity normal --settings coverlet.runsettings\
  --logger:"junit;LogFileName=TestResults.{assembly}.{framework}.xml;verbosity=normal"\
  --logger:"console;verbosity=normal"

FROM mcr.microsoft.com/dotnet/sdk:8.0-alpine AS nuget
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./*.sln .

COPY ./src/CoverGo.Payments.Integration.Events/*.csproj ./src/CoverGo.Payments.Integration.Events/
COPY ./nuget.config .

COPY ./src ./src

ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
ENV CI_BUILD=true
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN dotnet pack  ./src/CoverGo.Payments.Integration.Events/CoverGo.Payments.Integration.Events.csproj -c $BUILDCONFIG -o nuget -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"
