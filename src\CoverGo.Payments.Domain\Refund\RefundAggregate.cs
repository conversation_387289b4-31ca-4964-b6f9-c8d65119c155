﻿using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using GuardClauses;

namespace CoverGo.Payments.Domain.Refund;

public class RefundAggregate(
    string paymentId,
    RefundStatus status,
    PaymentMoney money,
    string? errorDetails,
    string? providerPaymentId)
    : AggregateRootBase<string>(Guid.NewGuid().ToString())
{
    public string PaymentId { get; set; } = paymentId;

    public RefundStatus Status { get; private set; } = status;

    public void SetStatus(RefundStatus status, string? error = null)
    {
        if (status == RefundStatus.Failed) GuardClause.ArgumentIsNotNull(error, nameof(error));

        Status = status;
    }

    public PaymentMoney Money { get; private set; } = money;

    public DateTime CreatedAtDateUtc { get; private set; } = PreciseClock.UtcNow;

    public string? ErrorDetails { get; private set; } = errorDetails;

    public bool IsSuccessful => Status == RefundStatus.Succeeded;

    public string? ProviderPaymentId { get; private set; } = providerPaymentId;
    
    public void AssignProviderTransaction(string providerTransactionId) 
        => ProviderPaymentId = providerTransactionId;
}