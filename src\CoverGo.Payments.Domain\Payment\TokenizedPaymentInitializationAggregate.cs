﻿using System.Text.Json;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Exceptions;

namespace CoverGo.Payments.Domain.Payment;

public class TokenizedPaymentInitializationAggregate(
    PaymentProvider paymentProvider,
    decimal paymentAmount,
    string paymentCurrencyCode,
    string paymentCurrencyDesc,
    int paymentDecimalPrecision,
    JsonElement? dynamicFields,
    JsonElement? publicFields,
    string policyId,
    string invoiceNumber,
    string payorId,
    bool? isCardUpdate = false)
    : AggregateRootBase<string>(id: Guid.NewGuid().ToString())
{
    public PaymentProvider PaymentProvider { get; private init; } = paymentProvider;

    public decimal PaymentAmount { get; private init; } = paymentAmount;

    public string PaymentCurrencyCode { get; private init; } = paymentCurrencyCode;

    public string PaymentCurrencyDesc { get; private init; } = paymentCurrencyDesc;

    public int PaymentDecimalPrecision { get; private init; } = paymentDecimalPrecision;

    public string? DynamicFields { get; private init; } = dynamicFields?.ToString();

    public string? PublicFields { get; private set; } = publicFields?.ToString();

    public bool? IsCanceled { get; private set; }

    public bool? IsExpired { get; set; }

    public string PolicyId { get; private set; } = policyId;

    public string InvoiceNumber { get; private set; } = invoiceNumber;

    public string PayorId { get; private set; } = payorId;

    public bool? IsCardUpdate { get; private init; } = isCardUpdate;

    public void Cancel() => IsCanceled = true;
    public void Expired() => IsExpired = true;

    public void ThrowIfCanceled()
    {
        if (IsCanceled == true) throw new DomainException("The payment initialization is canceled.");
    }
}