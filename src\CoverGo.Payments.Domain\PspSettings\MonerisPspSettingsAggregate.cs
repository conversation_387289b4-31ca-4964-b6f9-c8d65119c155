﻿using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.Domain.PspSettings;

public class MonerisPspSettingsAggregate : PspSettingsAggregate
{
    public string StoreId { get; set; }

    [Encrypted]
    public string ApiToken { get; set; }
    
    public string CheckoutId { get; set; }
    
    public string ProfileId { get; set; }
    
    // "qa" or "prod"
    public string Environment { get; set; }

    public static string ProcessingCountryCode => "CA";
    
    public static string CurrencyCode => "124";
}