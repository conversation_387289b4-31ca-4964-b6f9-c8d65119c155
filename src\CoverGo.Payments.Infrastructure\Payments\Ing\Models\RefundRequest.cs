﻿using CoverGo.Payments.Infrastructure.Payments.ING;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Ing.Models
{
    public class RefundRequest
    {

        [JsonProperty(IngContainer.PropsName.Type)]
        public string Type { get; set; }

        [JsonProperty(IngContainer.PropsName.ServiceId)]
        public string ServiceId { get; set; }

        [JsonProperty(IngContainer.PropsName.Amount)]
        public decimal Amount { get; set; }
    }
}
