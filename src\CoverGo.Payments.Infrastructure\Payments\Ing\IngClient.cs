﻿using CoverGo.Payments.Infrastructure.Payments.Ing.Builders;
using CoverGo.Payments.Infrastructure.Payments.Ing.Configurations;
using CoverGo.Payments.Infrastructure.Payments.Ing.Models;
using CoverGo.Payments.Infrastructure.Payments.ING.Models;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Payments.Ing;

public class IngClient(IngClientSettings settings, IHttpClientFactory clientFactory, ILogger logger) : BaseClient(settings, clientFactory, logger), IIngClient
{
    private readonly IngClientSettings _settings = settings;

    public Task<PaymentProfileResponse?> GetPaymentProfileAsync(string customerId, CancellationToken cancellationToken) =>
        ProcessAsync<PaymentProfileResponse>(
            IngClientUrlBuilder.BuildGetPaymentProfileUrl(_settings.Url, _settings.ApiVersion, _settings.MerchantId, customerId),
            HttpMethod.Get,
            cancellationToken);

    public Task<DebitPaymentProfileResponse?> DebitPaymentProfileAsync(DebitPaymentProfileRequest request,
        CancellationToken cancellationToken) =>
        ProcessAsync<DebitPaymentProfileResponse>(
            IngClientUrlBuilder.BuildDebitPaymentProfileUrl(_settings.Url, _settings.ApiVersion, _settings.MerchantId),
            HttpMethod.Post,
            cancellationToken,
            request);

    public Task<RefundResponse?> PostRefundAsync(RefundRequest request, 
        string transactionId,
        CancellationToken cancellationToken) =>
        ProcessAsync<RefundResponse>(
            IngClientUrlBuilder.BuildRefundUrl(_settings.Url, _settings.ApiVersion, _settings.MerchantId, transactionId),
            HttpMethod.Post,
            cancellationToken,
            request);
}