using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.InitializePayment;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.InitializePayment;

[MutationType]
public class InitializePaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "initialPaymentResult")]
    //[Authorize]
    public async Task<ProcessInitialPaymentResultDto> InitializePayment(
        InitializePaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}