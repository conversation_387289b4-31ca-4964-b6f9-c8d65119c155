using System.Security.Cryptography;
using CoverGo.BuildingBlocks.Bootstrapper.Configuration;
using CoverGo.BuildingBlocks.DataAccess.Mongo;
using CoverGo.BuildingBlocks.DataAccess.Mongo.Configuration;
using CoverGo.BuildingBlocks.MessageBus;
using CoverGo.BuildingBlocks.MessageBus.Dapr;
using CoverGo.BuildingBlocks.MessageBus.Dapr.Subscriptions;
using CoverGo.BuildingBlocks.MessageBus.InMemory;
using CoverGo.BuildingBlocks.MessageBus.Outbox.Mongo;
using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Application.PaymentMethods.Services;
using CoverGo.Payments.Infrastructure.PaymentMethods;
using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Application.Payments.Providers;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Application.PspSettings.Providers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Payment.DomainEvents;
using CoverGo.Payments.Domain.PaymentMethod;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Domain.BankAccount;
using CoverGo.Payments.Infrastructure.Adapters;
using CoverGo.Payments.Infrastructure.Common;
using CoverGo.Payments.Infrastructure.Common.Auth;
using CoverGo.Payments.Infrastructure.DataAccess;
using CoverGo.Payments.Infrastructure.Encryption.Factories;
using CoverGo.Payments.Infrastructure.Payments;
using CoverGo.Payments.Infrastructure.Payments.Fake;
using CoverGo.Payments.Infrastructure.Payments.Ing;
using CoverGo.Payments.Infrastructure.Payments.ING;
using CoverGo.Payments.Infrastructure.Payments.Moneris;
using CoverGo.Payments.Infrastructure.Payments.PayU;
using CoverGo.Payments.Infrastructure.Payments.Stripe;
using CoverGo.Payments.Infrastructure.Payments.Walaa;
using CoverGo.Payments.Infrastructure.PspSettings.Fake;
using CoverGo.Payments.Infrastructure.PspSettings.Ing;
using CoverGo.Payments.Infrastructure.PspSettings.Moneris;
using CoverGo.Payments.Infrastructure.PspSettings.PayU;
using CoverGo.Payments.Infrastructure.PspSettings.Stripe;
using CoverGo.Payments.Infrastructure.PspSettings.Walaa;
using CoverGo.Payments.Integration.Events;
using CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;
using CoverGo.Payments.Application.BankAccounts.Commands.UpdateBankAccount;
using CoverGo.Payments.Infrastructure.BankAccounts;
using MediatR.Pipeline;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MongoDB.Bson;
using MongoDB.Bson.Serialization;
using MongoDB.Bson.Serialization.Conventions;
using MongoDB.Bson.Serialization.Serializers;
using System.Security.Cryptography;

namespace CoverGo.Payments.Infrastructure;

public static class InfrastructureServiceExtensions
{
    /// <summary>
    ///     Adds infrastructure related services to the provided service collection.
    /// </summary>
    public static IServiceCollection AddInfrastructure(this IServiceCollection services,
        IConfiguration configuration,
        bool useInMemoryBus = false)
    {
        IEncryptionService encryptionService = RegisterEncryption(services);

        RegisterClassMaps(encryptionService);

        BsonSerializer.TryRegisterSerializer(DateOnlySerializer.Instance);
        BsonSerializer.TryRegisterSerializer(new ObjectSerializer(type =>
            ObjectSerializer.DefaultAllowedTypes(type) ||
            type.IsAssignableTo(typeof(DateOnly))));

        MongoDatabaseConfiguration dbConfig = configuration.GetConfiguration<MongoDatabaseConfiguration>();
        services.AddMongoDb(options =>
        {
            options.ConnectionString = dbConfig.ConnectionString;
            options.DatabaseName = dbConfig.DatabaseName;
            options.UseTransactions = dbConfig.UseTransactions;
        });

        RegisterCommonTypes();

        RegisterPspSettingsAggregate(services);
        RegisterTokenizedPaymentInitializationAggregate(services);
        RegisterPaymentAggregate(services);
        RegisterRefundAggregate(services);
        RegisterBankAccountAggregate(services);
        RegisterPaymentMethodAggregate(services);

        services.TryAddScoped<IDateTimeProvider, DateTimeProvider>();
        
        services.TryAddScoped<AdminLoginCacheProvider>();
        
        services.TryAddScoped<ServiceAccountLoginCacheProvider>();

        RegisterPaymentServices(services);

        MapDomainEventsToIntegrationEvents(services);

        services.AddMessageBus(builder =>
        {
            if (useInMemoryBus)
            {
                builder.AddInMemoryMessageBus();
            }
            else
            {
                builder.AddDaprMessageBus(configuration);
                builder.AddMongoOutbox();
            }
        });
        
        services.AddTransportMessageInterceptor<JwtMessageForwarder>();
        services.AddMessageInterceptor<TenantInterceptor>();
        services.AddMessageInterceptor<UserInterceptor>();

        return services;
    }

    private static void RegisterCommonTypes() =>
        BsonSerializer.TryRegisterSerializer(BsonSerializers.CurrencyCodeSerializer);

    private static void RegisterTokenizedPaymentInitializationAggregate(IServiceCollection services) 
        => services.AddMongoRepository<TokenizedPaymentInitializationAggregate, string>();

    private static void RegisterPaymentAggregate(IServiceCollection services) 
        => services.AddMongoRepository<PaymentAggregate, string>();

    private static void RegisterRefundAggregate(IServiceCollection services)
        => services.AddMongoRepository<RefundAggregate, string>();

    private static void RegisterPaymentMethodAggregate(IServiceCollection services)
        => services.AddMongoRepository<PayorPaymentMethodAggregate, string>();

    private static void RegisterPspSettingsAggregate(IServiceCollection services) 
        => services.AddMongoRepository<PspSettingsAggregate, string>();

    private static void RegisterBankAccountAggregate(IServiceCollection services)
    {
        services.AddMongoRepository<BankAccount, string>();
        services.TryAddScoped<BankAccountIndexing>();
    }

    private static void RegisterPaymentServices(IServiceCollection services)
    {
        services.TryAddScoped<IPaymentService, PaymentService>();
        services.TryAddScoped<IPaymentMethodService, PaymentMethodService>();

        #region FakePaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, FakePaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, FakePspSettingsProvider>());

        #endregion

        #region MonerisPaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, MonerisPaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, MonerisPspSettingsProvider>());
        services.TryAddScoped<MonerisPaymentProviderService>();

        #endregion

        #region PayUPaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, PayUPaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, PayUPspSettingsProvider>());

        #endregion

        #region StripePaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, StripePaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, StripePspSettingsProvider>());
        services.AddStripeServices();

        #endregion

        #region WalaaPaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, WalaaPaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, WalaaPspSettingsProvider>());
        services.AddSingleton<IWalaaCentralizedPaymentClient, WalaaCentralizedPaymentClient>();

        #endregion

        #region IngPaymentProvider

        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPaymentProviderService, IngPaymentProviderService>());
        services.TryAddEnumerable(ServiceDescriptor.Scoped<IPspSettingsProvider, IngPspSettingsProvider>());
        services.AddIngServices();

        #endregion
    }

    private static void RegisterClassMaps(IEncryptionService encryptionService)
    {
        var conventionPack = new ConventionPack { new IgnoreExtraElementsConvention(true) };

        ConventionRegistry.Register("IgnoreExtraElements", conventionPack, _ => true);

        BsonClassMap.TryRegisterClassMap<TokenizedPaymentInitializationAggregate>(cm =>
        {
            cm.AutoMap();
        });

        #region Payment

        EncryptedFieldConfigurator.RegisterClassMap<PaymentAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetIsRootClass(true);
        });

        BsonClassMap.TryRegisterClassMap<PreauthPaymentAggregate>(cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(PreauthPaymentAggregate));
        });

        BsonClassMap.TryRegisterClassMap<CapturePaymentAggregate>(cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(CapturePaymentAggregate));
            
        });

        BsonClassMap.TryRegisterClassMap<RecurringPaymentAggregate>(cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(RecurringPaymentAggregate));
        });

        #endregion Payment

        #region PspSettings

        BsonClassMap.TryRegisterClassMap<PspSettingsAggregate>(cm =>
        {
            cm.AutoMap();
            cm.SetIsRootClass(true);
        });

        BsonClassMap.TryRegisterClassMap<FakePspSettingsAggregate>(cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(FakePspSettingsAggregate));
        });

        EncryptedFieldConfigurator.RegisterClassMap<MonerisPspSettingsAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(MonerisPspSettingsAggregate));
        });

        EncryptedFieldConfigurator.RegisterClassMap<WalaaPspSettingsAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(WalaaPspSettingsAggregate));
        });

        EncryptedFieldConfigurator.RegisterClassMap<StripePspSettingsAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(StripePspSettingsAggregate));
        });

        EncryptedFieldConfigurator.RegisterClassMap<PayUPspSettingsAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(PayUPspSettingsAggregate));
        });

        EncryptedFieldConfigurator.RegisterClassMap<IngPspSettingsAggregate>(encryptionService, cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(IngPspSettingsAggregate));
        });

        #endregion PspSettings

        #region PSPBearer

        BsonClassMap.TryRegisterClassMap<PSPBearer>(cm =>
        {
            cm.AutoMap();
            cm.SetIsRootClass(true);
        });

        BsonClassMap.TryRegisterClassMap<PSPBearerPseudoCC>(cm =>
        {
            cm.AutoMap();
            cm.SetDiscriminator(nameof(PSPBearerPseudoCC));
        });

        #endregion

        BsonClassMap.TryRegisterClassMap<PaymentStatusHistoryItem>(cm =>
        {
            cm.AutoMap();
        });

        BsonClassMap.TryRegisterClassMap<PayerData>(cm =>
        {
            cm.AutoMap();
        });

        #region BankAccount

        BsonSerializer.TryRegisterSerializer(new EnumSerializer<BankAccountUsage>(BsonType.String));
        BsonSerializer.TryRegisterSerializer(new EnumSerializer<PayorType>(BsonType.String));

        #endregion
        BsonClassMap.TryRegisterClassMap<PayorPaymentMethodAggregate>(cm =>
        {
            cm.AutoMap();
        });

        BsonClassMap.TryRegisterClassMap<CardData>(cm =>
        {
            cm.AutoMap();
        });
    }
    
    public static void SubscribeToIntegrationEvents(this ISubscription subscription) => subscription.SubscribeToEvent<TestIntegrationEvent>();

    private static void MapDomainEventsToIntegrationEvents(IServiceCollection services)
    {
        services.MapDomainEventToIntegrationEvent<PaymentSucceededDomainEvent, PaymentSucceededEvent>(@event =>
            new PaymentSucceededEvent
            {
                PolicyId = @event.PolicyId,
                PayorId = @event.PayorId,
                InvoiceNumber = @event.InvoiceNumber,
                Status = @event.Status,
                EffectiveDate = @event.EffectiveDate,
                Money =
                    new Money
                    {
                        CurrencyCode = @event.Money.CurrencyCode,
                        CurrencyDesc = @event.Money.CurrencyDesc,
                        Amount = @event.Money.Amount,
                        DecimalPrecision = @event.Money.DecimalPrecision
                    },
                CreditCard =
                    @event.CreditCard != null
                        ? new CreditCard
                        {
                            CardType = @event.CreditCard.CardType,
                            Country = @event.CreditCard.Country,
                            ExpiryMonth = @event.CreditCard.ExpiryMonth,
                            ExpiryYear = @event.CreditCard.ExpiryYear,
                            Holder = @event.CreditCard.Holder,
                            CardNumber = @event.CreditCard.CardNumber
                        }
                        : null,
                PaymentProviderInfo = @event.PaymentProviderInfo != null
                    ? new PaymentProviderInfo
                    {
                        PaymentProvider = @event.PaymentProviderInfo.PaymentProvider,
                        ExternalPaymentReference = @event.PaymentProviderInfo.ExternalPaymentReference,
                        InternalPaymentReference = @event.PaymentProviderInfo.InternalPaymentReference
                    }
                    : null,
                Type = @event.Type,
                ReferenceId = @event.ReferenceId,
                PaymentMethod = @event.PaymentMethod,
                PaymentBankInfo = @event.PaymentBankInfo != null
                    ? new PaymentBankInfo
                    {
                        BranchTransitNumber = @event.PaymentBankInfo.BranchTransitNumber,
                        InstitutionId = @event.PaymentBankInfo.InstitutionId,
                        BankingInstitution = @event.PaymentBankInfo.BankingInstitution,
                        AccountNumber = @event.PaymentBankInfo.AccountNumber
                    }
                    : null
            }
        );
        
        services.MapDomainEventToIntegrationEvent<PaymentFailedDomainEvent, PaymentFailedEvent>(@event =>
            new PaymentFailedEvent
            {
                PolicyId = @event.PolicyId,
                InvoiceNumber = @event.InvoiceNumber,
                PayorId = @event.PayorId,
                Status = @event.Status,
                EffectiveDate = @event.EffectiveDate,
                Money =
                    new Money
                    {
                        CurrencyCode = @event.Money.CurrencyCode,
                        CurrencyDesc = @event.Money.CurrencyDesc,
                        Amount = @event.Money.Amount,
                        DecimalPrecision = @event.Money.DecimalPrecision
                    },
                CreditCard =
                    @event.CreditCard != null
                        ? new CreditCard
                        {
                            CardType = @event.CreditCard.CardType,
                            Country = @event.CreditCard.Country,
                            ExpiryMonth = @event.CreditCard.ExpiryMonth,
                            ExpiryYear = @event.CreditCard.ExpiryYear,
                            Holder = @event.CreditCard.Holder,
                            CardNumber = @event.CreditCard.CardNumber
                        }
                        : null,
                PaymentProviderInfo = @event.PaymentProviderInfo != null
                    ? new PaymentProviderInfo
                    {
                        PaymentProvider = @event.PaymentProviderInfo.PaymentProvider,
                        ExternalPaymentReference = @event.PaymentProviderInfo.ExternalPaymentReference,
                        InternalPaymentReference = @event.PaymentProviderInfo.InternalPaymentReference
                    }
                    : null,
                FailureMessage = @event.FailureMessage,
                Type = @event.Type,
                ReferenceId = @event.ReferenceId,
                PaymentMethod = @event.PaymentMethod
            }
        );
        
        services.MapDomainEventToIntegrationEvent<CreditCardUpdateSucceededDomainEvent, CreditCardUpdateSucceededEvent>(@event =>
            new CreditCardUpdateSucceededEvent
            {
                PolicyId = @event.PolicyId,
                PayorId = @event.PayorId,
                EffectiveDate = @event.EffectiveDate,
                CreditCard =
                    @event.CreditCard != null
                        ? new CreditCard
                        {
                            CardType = @event.CreditCard.CardType,
                            Country = @event.CreditCard.Country,
                            ExpiryMonth = @event.CreditCard.ExpiryMonth,
                            ExpiryYear = @event.CreditCard.ExpiryYear,
                            Holder = @event.CreditCard.Holder,
                            CardNumber = @event.CreditCard.CardNumber
                        }
                        : null,
                PaymentMethod = @event.PaymentMethod
            }
        );
        
        services.MapDomainEventToIntegrationEvent<CreditCardUpdateFailedDomainEvent, CreditCardUpdateFailedEvent>(@event =>
            new CreditCardUpdateFailedEvent
            {
                PolicyId = @event.PolicyId,
                PayorId = @event.PayorId,
                EffectiveDate = @event.EffectiveDate,
                CreditCard =
                    @event.CreditCard != null
                        ? new CreditCard
                        {
                            CardType = @event.CreditCard.CardType,
                            Country = @event.CreditCard.Country,
                            ExpiryMonth = @event.CreditCard.ExpiryMonth,
                            ExpiryYear = @event.CreditCard.ExpiryYear,
                            Holder = @event.CreditCard.Holder,
                            CardNumber = @event.CreditCard.CardNumber
                        }
                        : null,
                FailureMessage = @event.FailureMessage,
                PaymentMethod = @event.PaymentMethod
            }
        );
    }

    public static IEncryptionService RegisterEncryption(IServiceCollection services)
    {
        string algorithm = Environment.GetEnvironmentVariable("ENCRYPTION_ALGO") ?? "AES";
        string key = Environment.GetEnvironmentVariable("PAYMENT_METHOD_TOKEN_CIPHER_KEY") ?? "zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=";
        string iv = Environment.GetEnvironmentVariable("PAYMENT_METHOD_TOKEN_CIPHER_IV") ?? "94jCf53NO1acZ3pO7UE+gA==";

        IEncryptionService encryptionService = EncryptionServiceFactory.CreateEncryptionService(algorithm, key, iv, 256, CipherMode.CBC, PaddingMode.PKCS7);
        services.AddSingleton(encryptionService);
        return encryptionService;
    }

    public static MediatRServiceConfiguration AddBehaviorsAndPreprocessors(
        this MediatRServiceConfiguration configuration)
        => configuration.AddRequestPreProcessor<BankAccountIndexingBehavior>();
}