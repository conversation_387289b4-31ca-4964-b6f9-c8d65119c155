services:

  redis:
    image: redis:latest
    restart: always
    ports:
      - "6379:6379"

  covergo-mongo:
    image: ghcr.io/covergo/payments-mongo:latest
    build:
      dockerfile: Mongo.Dockerfile
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: local_dev
      MONGO_INITDB_DATABASE: payments
    command: --replSet covergo-mongo-set --keyFile mongo.key --bind_ip_all --port 27017
    healthcheck:
      test: echo "try { var statusResult = rs.status(); if (statusResult.ok != 1) { throw new Error('Not Ok'); } } catch (err) { var initiateResult = rs.initiate({_id:'covergo-mongo-set',members:[{_id:0,host:'covergo-mongo'}]}); if (initiateResult.ok != 1) { quit(1); } }" | mongosh --port 27017 -u root -p local_dev --authenticationDatabase admin --quiet
      interval: 5s
      timeout: 15s
      start_period: 5s
      retries: 10

  rabbitmq:
    container_name: rabbit
    image: rabbitmq:3-management-alpine
    environment:
      RABBITMQ_DEFAULT_USER: "admin"
      RABBITMQ_DEFAULT_PASS: "admin"
      RABBITMQ_DEFAULT_VHOST: "/"
    ports:
      - "5672:5672"
      - "15672:15672"
    healthcheck:
      test: rabbitmq-diagnostics -q status && rabbitmq-diagnostics -q check_local_alarms
      interval: 5s
      timeout: 10s
      retries: 3

    volumes:
      - ./rabbitmq/rabbitmqdata/lib/:/var/lib/rabbitmq/
      - ./rabbitmq/rabbitmqdata/log/:/var/log/rabbitmq/
  
  otel-collector:
    image: otel/opentelemetry-collector
    command: [ --config=/etc/otel-collector-config.yaml ]
    volumes:
      - ./otel-collector-config.yaml:/etc/otel-collector-config.yaml
    ports:
      - 4317:4317 # OTLP gRPC receiver
      - 4318:4318 # OTLP http receiver
      - 55679:55679 # zpages extension
    depends_on:
      - zipkin

  zipkin:
    image: openzipkin/zipkin:latest
    restart: always
    ports:
      - "9411:9411"
  
  covergo-payments:
    environment:
      - ObservabilityConfiguration__CollectorUrl=http://otel-collector:4317
      - ObservabilityConfiguration__ServiceName=covergo-payments
      - ObservabilityConfiguration__Timeout=1000
      - MongoDatabaseConfiguration__ConnectionString=********************************************?replicaSet=covergo-mongo-set
      - GraphQLStitching__Enabled=false
    image: ${REGISTRY:-covergo}/payments:${TAG:-latest}
    ports:
      - "5201:8080"
    build:
      context: .
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      dockerfile: src/CoverGo.Payments.Api/Dockerfile
    depends_on:
      rabbitmq:
        condition: service_healthy
      redis:
        condition: service_started
      covergo-mongo:
        condition: service_healthy
      

  covergo-payments-dapr:
    image: "daprio/daprd:edge"
    network_mode: "service:covergo-payments"
    depends_on:
      rabbitmq:
        condition: service_healthy
    command: [ "./daprd",
               "--app-id", "covergo-payments",
               "--app-port", "8080",
               "-resources-path", "/components"
    ]
    volumes:
      - "./dapr/components/:/components"
      - "./dapr/configuration/:/configuration"
  
  covergo-payments-health:
    image: busybox
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-payments:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 45
    depends_on:
      - covergo-payments

  covergo-auth:
    image: ghcr.io/covergo/auth:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - isDeployment=true
      - datacenterId=developer-machine
      - DBCONFIG-providerId=mongoDb
      - DATABASE_CONNECT_STRING=**************************************
      - OTP_LOGIN_CIPHER_KEY=zaWgPou46nNmfMrYivTS2waJO9VKI277iLlPkGL56yc=
      - OTP_LOGIN_CIPHER_IV=94jCf53NO1acZ3pO7UE+gA==
      - OTP_LOGIN_HASHER_KEY=key
      - COVERGO_PASSWORD=V9K&KobcZO3
    ports:
      - "60000:8080"
    depends_on:
      covergo-mongo:
        condition: service_healthy
  
  covergo-auth-health:
    image: busybox
    #use trap to exit gracefully from sleep
    command:
      [
        "/bin/sh",
        "-c",
        "trap \"echo exiting; exit 0\" TERM; sleep 1d & wait"
      ]
    healthcheck:
      test: "wget http://covergo-auth:8080/healthz -q -O -  || exit 1"
      interval: 1s
      timeout: 1s
      retries: 60
    depends_on:
      - covergo-auth
  
  covergo-gateway:
    image: ghcr.io/covergo/gateway:master
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging
      - REDIS_CONNECTION_STRING=redis:6379
      - datacenterId=covergo-dockerComposeOnJenkins-hk
    ports:
      - "60060:8080"
    depends_on:
      covergo-auth:
        condition: service_started
      covergo-auth-health:
        condition: service_healthy
      redis:
        condition: service_started

  covergo-api-gateway:
    image: ghcr.io/covergo/api-gateway:master
    environment:
      - ConnectionStrings__redis=redis:6379
    ports:
      - "8080"
    depends_on:
      covergo-payments:
        condition: service_started
      covergo-payments-health:
        condition: service_healthy
      redis:
        condition: service_started