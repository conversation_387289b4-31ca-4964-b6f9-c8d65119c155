﻿using System.Net;
using CoverGo.Payments.Infrastructure.Payments.PayU.Configurations;
using CoverGo.Payments.Infrastructure.Payments.PayU.Exceptions;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU
{
    public class PayUApiHttpCommunicator<T>
        where T : class
    {
        private readonly IHttpClientFactory _clientFactory;
        private readonly PayUClientSettings _settings;
        private readonly ILogger _logger;

        public PayUApiHttpCommunicator(IHttpClientFactory clientFactory, PayUClientSettings settings,
            ILogger logger)
        {
            _clientFactory = clientFactory;
            _settings = settings;
            _logger = logger;
        }

        public async Task<T?> SendAsync(HttpRequestMessage rq, CancellationToken ct)
        {
            using HttpClient client = _clientFactory.CreateClient(_settings.FactoryClientName);
            return await SendReceiveAsync(rq, client, ct);
        }

        private async Task<T?> SendReceiveAsync(HttpRequestMessage rq, HttpClient client, CancellationToken ct)
        {
            HttpResponseMessage response = await client.SendAsync(rq, ct);

            if (response.IsSuccessStatusCode || response.StatusCode == HttpStatusCode.Found)
            {
                return await DeserializeResponseAsync(response, ct);
            }

            string respStr = await response.Content.ReadAsStringAsync(ct);
            
            _logger.LogWarning(
                "PayU API request failed with status code {StatusCode} and reason {ReasonPhrase}. Response: {Response}",
                response.StatusCode, response.ReasonPhrase, respStr);
            
            throw new PayUApiException(response.StatusCode, response.ReasonPhrase, respStr);
        }

        private async Task<T?> DeserializeResponseAsync(HttpResponseMessage response, CancellationToken ct)
        {
            await using Stream contentStream = await response.Content.ReadAsStreamAsync(ct);
            using var streamReader = new StreamReader(contentStream);
            string respStr = await streamReader.ReadToEndAsync(ct);
            _logger.LogInformation("PayU API response: {Response}", respStr);
            return JsonConvert.DeserializeObject<T>(respStr);
        }
    }
}