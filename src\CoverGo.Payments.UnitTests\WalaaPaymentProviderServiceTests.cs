using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Infrastructure.Payments.Walaa;
using CoverGo.Payments.Infrastructure.Payments.Walaa.Models;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Primitives;
using Moq;
using Newtonsoft.Json;

namespace CoverGo.Payments.UnitTests;

public class WalaaPaymentProviderServiceTests
{
    private const string PspSettingsJson =
        "{ \"ApiUrl\": \"https://apiuat.walaa.com:5000\", \"WebhookUsername\": \"username\", \"WebhookPassword\": \"password\" }";

    private readonly Mock<IWalaaCentralizedPaymentClient> _walaaCentralizedPaymentClientMock;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
    private readonly WalaaPaymentProviderService _service;

    public WalaaPaymentProviderServiceTests()
    {
        _walaaCentralizedPaymentClientMock = new Mock<IWalaaCentralizedPaymentClient>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
        _service = new WalaaPaymentProviderService(Mock.Of<ILogger<WalaaPaymentProviderService>>(),
            _walaaCentralizedPaymentClientMock.Object, _httpContextAccessorMock.Object);
    }

    [Fact]
    public async Task GIVEN_ValidPayment_WHEN_GetPreProcessRedirectUrlAsync_THEN_Expected_Result_Returned()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Walaa, new PaymentMoney("682", "SAR", 1818.58m, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);

        using JsonDocument dynamicFields = JsonDocument.Parse(@"{
            ""InvoiceDetails"": [{
                ""InvoiceNumber"": ""Q-24-330-0001""
            }]
        }");

        _walaaCentralizedPaymentClientMock
            .Setup(x => x.InvoiceUploadAsync(It.IsAny<InvoiceUploadRequest>(), It.IsAny<Uri>(), It.IsAny<string>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(new InvoiceUploadResponse
            {
                Result = "Success",
                InvoiceDetails =
                [
                    new InvoiceUploadResponseInvoiceDetail
                    {
                        LinkExpiryDate = "2024-10-02",
                        PaymentLink = "https://ivox-uat.walaa.com/onlinepayment/home?rn=US05OS0zMzAtMDAxMA"
                    }
                ]
            });

        // Act
        RedirectUrlOutput result =
            await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields: dynamicFields.RootElement);

        // Assert
        result.Should().NotBeNull();
        result.RedirectUrl.Should().Be(new Uri("https://ivox-uat.walaa.com/onlinepayment/home?rn=US05OS0zMzAtMDAxMA"));
        result.Data.Should().BeEquivalentTo(new Dictionary<string, string> { ["linkExpiryDate"] = "2024-10-02" });
    }

    [Fact]
    public async Task GIVEN_ValidPayment_WHEN_HandleWebhookAsync_THEN_Expected_Result_Returned()
    {
        // Arrange
        string webhookBody = @"{
            ""InvoiceNumber"": ""Q-24-330-0001""
        }";

        _walaaCentralizedPaymentClientMock
            .Setup(x => x.VerifyPaymentAsync(It.IsAny<VerifyPaymentRequest>(), It.IsAny<Uri>(), It.IsAny<string>(),
                It.IsAny<string>(), It.IsAny<CancellationToken>())).ReturnsAsync(new VerifyPaymentResponse
            {
                Code = 1,
                Message = "Success",
                Data = new VerifyPaymentResponseData
                {
                    InvoiceId = "Q-24-330-0001", Amount = "57.50", Status = "PAID"
                }
            });

        Mock<HttpContext> httpContextMock = new();
        _httpContextAccessorMock.SetupGet(x => x.HttpContext).Returns(httpContextMock.Object);
        Mock<HttpRequest> httpRequestMock = new();
        httpContextMock.SetupGet(x => x.Request).Returns(httpRequestMock.Object);
        Mock<IHeaderDictionary> headersMock = new();
        httpRequestMock.SetupGet(x => x.Headers).Returns(headersMock.Object);
        headersMock.SetupGet(x => x.Authorization)
            .Returns(new StringValues("Basic " + new BasicAuthenticationHeaderValue("username", "password").Parameter));

        // Act
        (string paymentId, string providerPaymentId, string externalReference, string _, PaymentStatus paymentStatus,
                decimal? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool _) =
            await _service.HandleWebhookAsync(webhookBody,
                JsonConvert.DeserializeObject<WalaaPspSettingsAggregate>(PspSettingsJson));

        // Assert
        paymentId.Should().BeEmpty();
        providerPaymentId.Should().BeEmpty();
        externalReference.Should().Be("Q-24-330-0001");
        paymentStatus.Should().Be(PaymentStatus.Finalizing);
        amount.Should().BeApproximately(57.50m, 0.001m);
        webhookAcknowledgeMessage.Should().BeEmpty();
        isFinalizationRequired.Should().BeTrue();
    }

    [Fact]
    public async Task GIVEN_ValidPayment_WHEN_FinalizePaymentAsync_THEN_Expected_Side_Effects()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Walaa, new PaymentMoney("682", "SAR", 1818.58m, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.AddPaymentStatusHistoryItem(PaymentStatus.Finalizing, new PaymentMoney("682", "SAR", 57.50m, 2),
            webhookBody: "{}");

        // Act
        await _service.FinalizePaymentAsync(payment, null, dynamicFields: null);

        // Assert
        payment.Status.Should().Be(PaymentStatus.Succeeded);
        payment.PaymentStatusHistoryItems.Last().Money.PaymentAmount.Should().BeApproximately(57.50m, 0.001m);
    }
}