﻿namespace CoverGo.Payments.Infrastructure.Payments.Ing.Configurations
{
    public class IngClientSettings
    {
        public IngClientSettings(string url, string apiVersion, string merchantId, string apiKey,
            string factoryClientName, int retryCount = 3)
        {
            AssignMandatoryProperties(url, apiVersion, merchantId, apiKey);
            Url = url;
            ApiVersion = apiVersion;
            MerchantId = merchantId;
            ApiKey = apiKey;
            FactoryClientName = factoryClientName;
            RetryCount = retryCount;
        }

        public string Url { get; private set; }
        public string ApiVersion { get; private set; }
        public string MerchantId { get; private set; }
        public string ApiKey { get; private set; }
        public string FactoryClientName { get; }
        public int RetryCount { get; set; }

        private void AssignMandatoryProperties(string url, string apiVersion, string merchantId, string apiKey)
        {
            Valid(url, apiVersion, merchantId, apiKey);

            Url = url;
            ApiVersion = apiVersion;
            MerchantId = merchantId;
            ApiKey = apiKey;
        }

        private static void Valid(string url, string apiVersion, string merchantId, string apiKey)
        {
            if (string.IsNullOrEmpty(url) ||
                string.IsNullOrEmpty(apiVersion) ||
                string.IsNullOrEmpty(merchantId) ||
                string.IsNullOrEmpty(apiKey))
                throw new ArgumentException("Some of settings properties are null or empty");
        }
    }
}