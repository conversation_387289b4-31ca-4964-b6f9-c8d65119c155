﻿using System.Net;
using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.Multitenancy;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Commands.RegisterPayment;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Providers;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Application.PaymentMethods.Commands.CreatePaymentMethod;
using CoverGo.Payments.Application.PspSettings.Providers;
using CoverGo.Payments.Application.Refunds.Commands.RefundPayment;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Payment.DomainEvents;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Common;
using CoverGo.Payments.Infrastructure.Exceptions;
using CoverGo.Payments.Integration.Events;
using CoverGo.Proxies.Auth;
using GuardClauses;
using MediatR;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using MongoDB.Driver.Linq;
using Newtonsoft.Json;
using PaymentAggregate = CoverGo.Payments.Domain.Payment.PaymentAggregate;

namespace CoverGo.Payments.Infrastructure.Payments;

public class PaymentService : IPaymentService
{
    public const string TransactionTypePremiumCollectedInitial = "Premium Collected Initial";
    public const string TransactionTypePremiumCollectedRecurring = "Premium Collected Recurring";
    public const string TransactionTypePremiumCollectedEndorsement = "Premium Collected Endorsement";
    public const string TransactionTypeRefund = "Refund";
    private const string TransactionTypeRefundPaid = "Refund Paid";

    public const string TransactionSucceededStatus = "Succeeded";
    public const string TransactionFailedStatus = "Failed";

    private const string MemberPortalClientKey = "member";

    private readonly ILogger<PaymentService> _logger;

    private readonly IRepository<TokenizedPaymentInitializationAggregate, string>
        _tokenizedPaymentInitializationRepository;

    private readonly IRepository<PaymentAggregate, string> _paymentRepository;
    private readonly IRepository<RefundAggregate, string> _refundRepository;
    private readonly IEnumerable<IPaymentProviderService> _paymentProviderServices;
    private readonly IEnumerable<IPspSettingsProvider> _pspSettingsProviders;
    private readonly IMongoCollection<PaymentAggregate> _mongoCollection;
    private readonly IAuthService _authService;
    private readonly ITenantProvider _tenantProvider;
    private readonly IMediator _mediator;

    public PaymentService(
        ILogger<PaymentService> logger,
        IRepository<TokenizedPaymentInitializationAggregate, string> tokenizedPaymentInitializationRepository,
        IRepository<PaymentAggregate, string> paymentRepository,
        IRepository<RefundAggregate, string> refundRepository,
        IEnumerable<IPaymentProviderService> paymentProviderServices,
        IEnumerable<IPspSettingsProvider> pspSettingsProviders,
        IMongoCollection<PaymentAggregate> mongoCollection,
        IAuthService authService,
        ITenantProvider tenantProvider,
        IMediator mediator)
    {
        GuardClause.ArgumentIsNotNull(logger, nameof(logger));
        GuardClause.ArgumentIsNotNull(tokenizedPaymentInitializationRepository,
            nameof(tokenizedPaymentInitializationRepository));
        GuardClause.ArgumentIsNotNull(paymentRepository, nameof(paymentRepository));
        GuardClause.ArgumentIsNotNull(refundRepository, nameof(refundRepository));
        GuardClause.ArgumentIsNotNull(authService, nameof(authService));
        GuardClause.ArgumentIsNotNull(tenantProvider, nameof(tenantProvider));
        GuardClause.ArgumentIsNotNull(mediator, nameof(mediator));

        _logger = logger;
        _tokenizedPaymentInitializationRepository = tokenizedPaymentInitializationRepository;
        _paymentRepository = paymentRepository;
        _refundRepository = refundRepository;
        _paymentProviderServices = paymentProviderServices;
        _pspSettingsProviders = pspSettingsProviders;
        _mongoCollection = mongoCollection;
        _authService = authService;
        _tenantProvider = tenantProvider;
        _mediator = mediator;
    }

    public IMongoQueryable<PaymentAggregate> GetPayments()
        => _mongoCollection.AsQueryable();

    public async Task<TokenizedPaymentInitialization> TokenizePaymentInitializationAsync(
        TokenizedPaymentInitializationAggregate tokenizedPaymentInitialization, CancellationToken cancellationToken)
    {
        _logger.LogInformation("PaymentService.TokenizePaymentInitializationAsync: Starting payment initialization tokenization. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}, Amount: {Amount} {CurrencyDesc}, InvoiceNumber: {InvoiceNumber}",
            tokenizedPaymentInitialization.PolicyId,
            tokenizedPaymentInitialization.PayorId,
            tokenizedPaymentInitialization.PaymentProvider,
            tokenizedPaymentInitialization.PaymentAmount,
            tokenizedPaymentInitialization.PaymentCurrencyDesc,
            tokenizedPaymentInitialization.InvoiceNumber ?? "null");

        try
        {
            await _tokenizedPaymentInitializationRepository.InsertAsync(tokenizedPaymentInitialization, cancellationToken);

            var result = new TokenizedPaymentInitialization { InitializationToken = tokenizedPaymentInitialization.Id };

            _logger.LogInformation("PaymentService.TokenizePaymentInitializationAsync: Payment initialization tokenization completed. InitializationToken: {InitializationToken}, PolicyId: {PolicyId}, PayorId: {PayorId}",
                result.InitializationToken,
                tokenizedPaymentInitialization.PolicyId,
                tokenizedPaymentInitialization.PayorId);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.TokenizePaymentInitializationAsync: Error tokenizing payment initialization. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                tokenizedPaymentInitialization.PolicyId,
                tokenizedPaymentInitialization.PayorId,
                tokenizedPaymentInitialization.PaymentProvider);
            throw;
        }
    }

    public async Task CancelPaymentInitializationsAsync(List<string> initializationTokens,
        PaymentStatus? cancellationStatus = null,
        string? cancellationReason = null,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("PaymentService.CancelPaymentInitializationsAsync: Starting payment initializations cancellation. TokensCount: {TokensCount}, CancellationStatus: {CancellationStatus}, Reason: {CancellationReason}",
            initializationTokens.Count,
            cancellationStatus?.ToString() ?? "Canceled",
            cancellationReason ?? "No reason provided");

        try
        {
            List<TokenizedPaymentInitializationAggregate> aggregates =
                await _tokenizedPaymentInitializationRepository.FindAllAsync(ids: initializationTokens, cancellationToken);

            if (aggregates.Count == 0)
            {
                _logger.LogWarning("PaymentService.CancelPaymentInitializationsAsync: No payment initialization aggregates found for cancellation. TokensCount: {TokensCount}", initializationTokens.Count);
                return;
            }

            _logger.LogInformation("PaymentService.CancelPaymentInitializationsAsync: Found {AggregatesCount} payment initialization aggregates for cancellation",
                aggregates.Count);

            // If the cancellation status is not expired, cancel the initializations
            // The payment link will be expired as automatically by public fields to make flow consistent
            if (cancellationStatus == PaymentStatus.Expired)
            {
                aggregates.ForEach(a => a.Expired());
            }
            else
            {
                aggregates.ForEach(a => a.Cancel());
            }
            await _tokenizedPaymentInitializationRepository.UpdateBatchAsync(aggregates, cancellationToken);

            var invoiceNumbers = aggregates
            .Where(a => !string.IsNullOrWhiteSpace(a.InvoiceNumber))
            .Select(a => a.InvoiceNumber!)
            .Distinct()
            .ToList();

            var payments = await _paymentRepository.FindAllByAsync(
                p => invoiceNumbers.Contains(p.InvoiceNumber!), cancellationToken);

            if (!payments.Any())
            {
                _logger.LogInformation("PaymentService.CancelPaymentInitializationsAsync: Payment initializations cancellation completed. TokensCount: {TokensCount}, RelatedPaymentsFound: 0",
                    initializationTokens.Count);
                return;
            }

            _logger.LogInformation("PaymentService.CancelPaymentInitializationsAsync: Found {PaymentsCount} related payments to cancel", payments.Count());

            var cancelTasks = new List<Task>();
            foreach (PaymentAggregate payment in payments)
            {
                if (payment is PreauthPaymentAggregate preauthPayment &&
                initializationTokens.Contains(preauthPayment.InitializationToken!))
                {
                    cancelTasks.Add(_mediator.Send(new CancelPaymentCommand(
                        preauthPayment.Id,
                        cancellationStatus,
                        cancellationReason), cancellationToken));
                }
            }

            await Task.WhenAll(cancelTasks);

            _logger.LogInformation("PaymentService.CancelPaymentInitializationsAsync: Payment initializations cancellation completed. TokensCount: {TokensCount}, PaymentsCancelled: {CancelTasksCount}",
                initializationTokens.Count, cancelTasks.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.CancelPaymentInitializationsAsync: Error cancelling payment initializations. TokensCount: {TokensCount}",
                initializationTokens.Count);
            throw;
        }
    }

    public async Task<TokenizedPaymentInitializationAggregate?> GetTokenizedPaymentInitializationAsync(
        string initializationToken, bool throwIfNotFound, CancellationToken cancellationToken)
    {
        _logger.LogInformation("PaymentService.GetTokenizedPaymentInitializationAsync: Retrieving tokenized payment initialization. InitializationToken: {InitializationToken}, ThrowIfNotFound: {ThrowIfNotFound}",
            initializationToken, throwIfNotFound);

        try
        {
            TokenizedPaymentInitializationAggregate? result;

            if (throwIfNotFound)
                result = await _tokenizedPaymentInitializationRepository.GetByIdAsync(initializationToken, cancellationToken);
            else
                result = await _tokenizedPaymentInitializationRepository.FindByIdAsync(initializationToken, cancellationToken);

            if (result != null)
            {
                _logger.LogInformation("PaymentService.GetTokenizedPaymentInitializationAsync: Tokenized payment initialization retrieved. InitializationToken: {InitializationToken}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                    initializationToken, result.PolicyId, result.PayorId, result.PaymentProvider);
            }
            else
            {
                _logger.LogWarning("PaymentService.GetTokenizedPaymentInitializationAsync: Tokenized payment initialization not found. InitializationToken: {InitializationToken}",
                    initializationToken);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.GetTokenizedPaymentInitializationAsync: Error retrieving tokenized payment initialization. InitializationToken: {InitializationToken}",
                initializationToken);
            throw;
        }
    }

    public async Task<PreauthPaymentAggregate?> FindExistingPaymentAsync(string policyId, string payorId, string invoiceNumber,
        string initializationToken, CancellationToken cancellationToken)
    {
        _logger.LogInformation("PaymentService.FindExistingPaymentAsync: Searching for existing payment. Policy: {Policy}, PayorId: {PayorId},  InvoiceNumber: {InvoiceNumber}, InitializationToken: {InitializationToken}",
            policyId, payorId, invoiceNumber, initializationToken);

        try
        {
            IEnumerable<PaymentAggregate> payments = await _paymentRepository.FindAllByAsync(
                p => p.InvoiceNumber == invoiceNumber 
                && p.PolicyId == policyId
                && p.PayorId == payorId,
                cancellationToken);

            PreauthPaymentAggregate? existingPayment = payments
                .OfType<PreauthPaymentAggregate>()
                .FirstOrDefault(p => p.InitializationToken == initializationToken);

            if (existingPayment != null)
            {
                _logger.LogInformation("PaymentService.FindExistingPaymentAsync: Found existing payment. PaymentId: {PaymentId}, Status: {Status}",
                    existingPayment.Id, existingPayment.Status);
            }
            else
            {
                _logger.LogInformation("PaymentService.FindExistingPaymentAsync: No existing payment found with matching invoice number and initialization token");
            }

            return existingPayment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.FindExistingPaymentAsync: Error searching for existing payment. Policy: {Policy}, PayorId: {PayorId}, InvoiceNumber: {InvoiceNumber}, InitializationToken: {InitializationToken}",
                policyId, payorId, invoiceNumber, initializationToken);
            throw;
        }
    }

    public async Task<ProcessInitialPaymentResult> ProcessInitWithExistingPaymentAsync(PreauthPaymentAggregate preauthPayment,
        JsonElement? dynamicFields, CancellationToken cancellationToken)
    {
        _logger.LogInformation("PaymentService.ProcessInitWithExistingPaymentAsync: Processing initialization with existing payment. PaymentId: {PaymentId}",
            preauthPayment.Id);

        try
        {
            await VerifyPreviousPaymentByInvoiceNumberAsync(preauthPayment, cancellationToken);

            if (preauthPayment.IsFailure || preauthPayment.PaymentProvider == PaymentProvider.Moneris)
            {
                preauthPayment.SetAttempt(preauthPayment.Attempt + 1);
                preauthPayment.SetInternalReference();
            }

            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.InProgress, preauthPayment.Money);

            await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);

            // Handle initial payment processing
            IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
            return await HandleInitialPaymentAsync(preauthPayment, pspService, dynamicFields, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.ProcessInitWithExistingPaymentAsync: Error processing existing payment. PaymentId: {PaymentId}",
                preauthPayment.Id);
            throw;
        }
    }

    public async Task<ProcessInitialPaymentResult> ProcessInitialPaymentAsync(PreauthPaymentAggregate preauthPayment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPayment.Id }))
        {
            _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Starting initial payment processing. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}, Amount: {Amount} {CurrencyDesc}, IsUpdate: {IsUpdate}, InvoiceNumber: {InvoiceNumber}, HasDynamicFields: {HasDynamicFields}",
                preauthPayment.Id, preauthPayment.PolicyId, preauthPayment.PayorId, preauthPayment.PaymentProvider,
                preauthPayment.Money.PaymentAmount, preauthPayment.Money.PaymentCurrencyDesc, preauthPayment.IsUpdate,
                preauthPayment.InvoiceNumber ?? "null", dynamicFields.HasValue);

            try
            {
                ValidatePayment(preauthPayment);
                await PopulatePspSettingsAsync(preauthPayment, cancellationToken);
                preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.InProgress, preauthPayment.Money);

                PaymentAggregate? prevPayment =
                    await GetPreauthPaymentByPolicyIdAndPayorId(preauthPayment.PolicyId, preauthPayment.PayorId,
                        cancellationToken);
                IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);

                if (prevPayment != null)
                {
                    _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Previous payment found. PaymentId: {PaymentId}, PrevPaymentId: {PrevPaymentId}, PrevPaymentStatus: {PrevPaymentStatus}",
                        preauthPayment.Id, prevPayment.Id, prevPayment.Status);
                    preauthPayment.SetPrevPaymentId(prevPayment.Id);
                }

                if (prevPayment != null && preauthPayment.IsUpdate)
                {
                    _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Processing payment update flow. PaymentId: {PaymentId}, PrevPaymentId: {PrevPaymentId}",
                        preauthPayment.Id, prevPayment.Id);

                    if (!string.IsNullOrWhiteSpace(prevPayment.DynamicFields))
                    {
                        dynamicFields = System.Text.Json.JsonSerializer.Deserialize<JsonElement>(prevPayment.DynamicFields!);
                    }
                    dynamicFields = await SetPostCheckoutRedirectUrlForUpdateAsync(pspService, dynamicFields,
                        cancellationToken);
                    preauthPayment.SetDynamicFields(dynamicFields);
                }
                else
                {
                    _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Processing new payment flow. PaymentId: {PaymentId}",
                        preauthPayment.Id);

                    dynamicFields =
                        await SetPostCheckoutRedirectUrlAsync(pspService, dynamicFields, cancellationToken);
                    preauthPayment.SetDynamicFields(dynamicFields);
                }

                SetInternalReference(prevPayment, preauthPayment);
                await VerifyPreviousPaymentByInvoiceNumberAsync(preauthPayment, cancellationToken);
                await _paymentRepository.InsertAsync(preauthPayment, cancellationToken);

                // Handle initial payment processing
                return await HandleInitialPaymentAsync(preauthPayment, pspService, dynamicFields, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.ProcessInitialPaymentAsync: Unexpected error during initial payment processing. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                    preauthPayment.Id, preauthPayment.PolicyId, preauthPayment.PayorId, preauthPayment.PaymentProvider);
                throw;
            }
        }
    }

    private async Task<ProcessInitialPaymentResult> HandleInitialPaymentAsync(
        PreauthPaymentAggregate preauthPayment,
        IPaymentProviderService pspService,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken)
    {
        Uri? redirectUrl = default;
        Dictionary<string, string>? data = null;

        try
        {
            preauthPayment = await PreparePaymentAsync(preauthPayment, pspService, cancellationToken);

            if (preauthPayment.Status == PaymentStatus.Failed)
            {
                _logger.LogWarning("PaymentService.ProcessInitialPaymentAsync: Payment preparation failed. PaymentId: {PaymentId}, Status: {Status}, Error: {Error}",
                    preauthPayment.Id, preauthPayment.Status, preauthPayment.PaymentStatusHistoryItem?.Error ?? "No error details");

                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                return new ProcessInitialPaymentResult(preauthPayment);
            }

            RedirectUrlOutput redirectInfo =
                await pspService.GetPreProcessRedirectUrlAsync(preauthPayment, dynamicFields, cancellationToken);

            redirectUrl = redirectInfo.RedirectUrl;
            data = redirectInfo.Data.ToDictionary(kvp => kvp.Key, kvp => kvp.Value);
        }
        catch (Exception exception)
        {
            _logger.LogError(exception, "PaymentService.ProcessInitialPaymentAsync: Error during payment preparation or redirect URL generation. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                preauthPayment.Id, preauthPayment.PaymentProvider);

            if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
            {
                preauthPayment.AddPaymentStatusHistoryItem(
                    PaymentStatus.Failed,
                    preauthPayment.Money,
                    exception.Message);
            }
        }
        finally
        {
            await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
        }

        // Validate Preauth status
        if (preauthPayment.PreauthStatus != PreauthPaymentStatus.Cancelled &&
            preauthPayment.PreauthStatus != PreauthPaymentStatus.CancelFailed &&
            preauthPayment.PreauthStatus != PreauthPaymentStatus.ToBePaid)
        {
            _logger.LogError("PaymentService.ProcessInitialPaymentAsync: Invalid preauth payment status after processing. PaymentId: {PaymentId}, PreauthStatus: {PreauthStatus}",
                preauthPayment.Id, preauthPayment.PreauthStatus);
            throw new IllegalPaymentStatusException(preauthPayment.PreauthStatus);
        }

        // Validate Payment status
        if (preauthPayment.Status == PaymentStatus.Chargeback ||
            preauthPayment.Status == PaymentStatus.Created ||
            preauthPayment.Status == PaymentStatus.InProgress ||
            preauthPayment.Status == PaymentStatus.Refunded ||
            preauthPayment.Status == PaymentStatus.Unmapped)
        {
            _logger.LogError("PaymentService.ProcessInitialPaymentAsync: Invalid payment status after processing. PaymentId: {PaymentId}, PaymentStatus: {PaymentStatus}",
                preauthPayment.Id, preauthPayment.Status);
            throw new IllegalPaymentStatusException(preauthPayment.Status);
        }

        // Determine redirect result type
        if (redirectUrl == null && data == null)
        {
            _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}, ResultType: NoRedirect",
                preauthPayment.Id, preauthPayment.Status);
            return new ProcessInitialPaymentResult(preauthPayment);
        }

        if (redirectUrl != null && data == null)
        {
            _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}, ResultType: RedirectOnly, RedirectUrl: {RedirectUrl}",
                preauthPayment.Id, preauthPayment.Status, redirectUrl.ToString());
            return new ProcessInitialPaymentResult(preauthPayment, redirectUrl);
        }

        _logger.LogInformation("PaymentService.ProcessInitialPaymentAsync: Processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}, ResultType: RedirectWithData, RedirectUrl: {RedirectUrl}, DataItemsCount: {DataItemsCount}",
            preauthPayment.Id, preauthPayment.Status, redirectUrl?.ToString() ?? "null", data?.Count ?? 0);
        return new ProcessInitialPaymentResult(preauthPayment, redirectUrl, data);
    }

    private async Task<JsonElement?> SetPostCheckoutRedirectUrlForUpdateAsync(
        IPaymentProviderService paymentProviderService,
        JsonElement? previousDynamicFields,
        CancellationToken cancellationToken)
    {

        if (!_tenantProvider.TryGetCurrent(out TenantId? tenantId))
            return previousDynamicFields;

        TenantSettings tenantSettings = await _authService.GetTenantSettingsAsync(tenantId.Value, cancellationToken);

        string? memberPortalOrigin = GetSuccessRedirectUrlFromSettings(tenantSettings, MemberPortalClientKey);
        if (string.IsNullOrWhiteSpace(memberPortalOrigin))
            return previousDynamicFields;

        memberPortalOrigin = EnsureUrlHasSchema(memberPortalOrigin);

        // Add clientKey to dynamic fields
        JsonElement? updatedDynamicFields = AddClientKeyToExistingJson(previousDynamicFields, MemberPortalClientKey);

        return updatedDynamicFields is null
            ? SerializePostCheckoutRedirectUrl(memberPortalOrigin)
            : AddPostCheckoutRedirectUrlToExistingJson(updatedDynamicFields, memberPortalOrigin);
    }
    private async Task<JsonElement?> SetPostCheckoutRedirectUrlAsync(
            IPaymentProviderService paymentProviderService,
            JsonElement? previousDynamicFields,
            CancellationToken cancellationToken)
    {
        // Extract clientKey from previousDynamicFields and use it to get specific host from tenantSettings
        string? clientKey = ExtractClientKeyFromDynamicFields(previousDynamicFields);

        if (!string.IsNullOrWhiteSpace(clientKey))
        {
            string? clientSpecificRedirectUrl = await GetTenantConfigRedirectUrlAsync(clientKey, cancellationToken);
            if (!string.IsNullOrWhiteSpace(clientSpecificRedirectUrl))
            {
                return previousDynamicFields is null
                    ? SerializePostCheckoutRedirectUrl(clientSpecificRedirectUrl)
                    : AddPostCheckoutRedirectUrlToExistingJson(previousDynamicFields, clientSpecificRedirectUrl);
            }
        }
        return previousDynamicFields;
    }

    private static string? GetSuccessRedirectUrlFromSettings(TenantSettings? tenantSettings, string clientKey) =>
        tenantSettings?.Hosts?.FirstOrDefault(h => h.Contains(clientKey));

    private static string EnsureUrlHasSchema(string url) =>
        url.StartsWith("http://") || url.StartsWith("https://") ? url : $"https://{url}";

    private static JsonElement SerializePostCheckoutRedirectUrl(string origin)
    {
        var dictionary = new Dictionary<string, string>
        {
            ["postCheckoutRedirectUrl"] = origin,
            ["clientKey"] = MemberPortalClientKey
        };

        return System.Text.Json.JsonSerializer.SerializeToElement(dictionary);
    }

    private static JsonElement AddPostCheckoutRedirectUrlToExistingJson(JsonElement? existingJson, string origin)
    {
        if (existingJson is null)
            return SerializePostCheckoutRedirectUrl(origin);

        var jsonDictionary = existingJson.Value
            .EnumerateObject()
            .ToDictionary(property => property.Name, property => property.Value.Clone());

        jsonDictionary["postCheckoutRedirectUrl"] = System.Text.Json.JsonSerializer.SerializeToElement(origin);

        return System.Text.Json.JsonSerializer.SerializeToElement(jsonDictionary);
    }

    private static JsonElement AddClientKeyToExistingJson(JsonElement? existingJson, string clientKey)
    {
        if (existingJson is null)
        {
            var dictionary = new Dictionary<string, string>
            {
                ["clientKey"] = clientKey
            };
            return System.Text.Json.JsonSerializer.SerializeToElement(dictionary);
        }

        var jsonDictionary = existingJson.Value
            .EnumerateObject()
            .ToDictionary(property => property.Name, property => property.Value.Clone());

        jsonDictionary["clientKey"] = System.Text.Json.JsonSerializer.SerializeToElement(clientKey);

        return System.Text.Json.JsonSerializer.SerializeToElement(jsonDictionary);
    }

    public async Task<PaymentAggregate> CapturePreauthPaymentAsync(string preauthPaymentId,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            _logger.LogInformation("PaymentService.CapturePreauthPaymentAsync: Starting preauth payment capture. PaymentId: {PaymentId}", preauthPaymentId);

            try
            {
                GuardClause.IsNullOrEmptyStringOrWhiteSpace(preauthPaymentId, nameof(preauthPaymentId));

                var preauthPayment =
                    await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken) as PreauthPaymentAggregate;

                _logger.LogInformation("PaymentService.CapturePreauthPaymentAsync: Retrieved preauth payment for capture. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, Status: {Status}, PreauthStatus: {PreauthStatus}",
                    preauthPayment!.Id, preauthPayment.PolicyId, preauthPayment.PayorId, preauthPayment.Status, preauthPayment.PreauthStatus);

                IPspSettingsProvider pspSettingsProvider = GetPspSettingsProvider(preauthPayment!.PaymentProvider);
                PspSettingsAggregate? pspSettings = await pspSettingsProvider.GetPspSettingsAsync(cancellationToken);
                if (pspSettings?.UseAutoCapture ?? false)
                    throw new DomainException(
                        "Capturing failed: The auto-capture is disabled in the current PSP settings.");

                if (preauthPayment.IsFailure || preauthPayment.PreauthStatus != PreauthPaymentStatus.ToBePaid)
                {
                    _logger.LogInformation("PaymentService.CapturePreauthPaymentAsync: Preauth payment not eligible for capture. PaymentId: {PaymentId}, IsFailure: {IsFailure}, PreauthStatus: {PreauthStatus}",
                        preauthPayment.Id, preauthPayment.IsFailure, preauthPayment.PreauthStatus);
                    return preauthPayment;
                }

                var capturePayment = new CapturePaymentAggregate(preauthPayment);
                ValidatePayment(capturePayment);
                capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);
                await _paymentRepository.InsertAsync(capturePayment, cancellationToken);

                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                    capturePayment =
                        await pspService.CapturePaymentAsync(capturePayment, preauthPayment.ProviderPaymentId,
                            cancellationToken);
                    if (capturePayment.IsSuccessful) preauthPayment.SetPreauthStatus(PreauthPaymentStatus.Paid);

                    _logger.LogInformation("PaymentService.CapturePreauthPaymentAsync: Preauth payment capture completed. PaymentId: {PaymentId}, CapturePaymentId: {CapturePaymentId}, CaptureStatus: {CaptureStatus}, PreauthStatus: {PreauthStatus}",
                        preauthPayment.Id, capturePayment.Id, capturePayment.Status, preauthPayment.PreauthStatus);

                    return capturePayment;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PaymentService.CapturePreauthPaymentAsync: Error during payment capture. PaymentId: {PaymentId}, CapturePaymentId: {CapturePaymentId}",
                        preauthPayment.Id, capturePayment.Id);

                    if (!capturePayment.Status.Equals(PaymentStatus.Failed))
                    {
                        preauthPayment.SetPreauthStatus(PreauthPaymentStatus.PaymentFailed);
                        capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, ex.Message);
                    }
                }
                finally
                {
                    await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                    await _paymentRepository.UpdateAsync(capturePayment, cancellationToken);
                }

                return capturePayment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.CapturePreauthPaymentAsync: Unexpected error during preauth payment capture. PaymentId: {PaymentId}",
                    preauthPaymentId);
                throw;
            }
        }
    }

    public async Task<PreauthPaymentAggregate> CancelPreauthPaymentAsync(string preauthPaymentId,
        PaymentStatus cancellationStatus = PaymentStatus.Canceled,
        string? cancellationReason = null,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Starting preauth payment cancellation. PaymentId: {PaymentId}, CancellationStatus: {CancellationStatus}, Reason: {CancellationReason}",
                preauthPaymentId, cancellationStatus, cancellationReason ?? "No reason provided");

            try
            {
                PaymentAggregate payment = await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken);
                if (payment is not PreauthPaymentAggregate preauthPayment)
                {
                    _logger.LogError("PaymentService.CancelPreauthPaymentAsync: Cancellation failed - not a preauth payment. PaymentId: {PaymentId}, PaymentType: {PaymentType}",
                        preauthPaymentId, payment.GetType().Name);
                    throw new DomainException("Cancellation failed: Only preauthorized transactions can be canceled.");
                }

                _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Retrieved preauth payment for cancellation. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, CurrentStatus: {Status}, PreauthStatus: {PreauthStatus}",
                    preauthPayment.Id, preauthPayment.PolicyId, preauthPayment.PayorId, preauthPayment.Status, preauthPayment.PreauthStatus);

                if (preauthPayment.Status == PaymentStatus.Succeeded)
                {
                    _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Preauth payment already succeeded. PaymentId: {PaymentId}, Status: {Status}",
                        preauthPayment.Id, preauthPayment.Status);
                    throw new DomainException("Cancellation failed: Preauthorized payment has already been captured.");
                }

                // Validate the cancellation status
                ValidateCancellationStatus(cancellationStatus);

                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                    if (preauthPayment.ProviderPaymentId is null && preauthPayment.Status == PaymentStatus.Prepared)
                    {
                        _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Cancelling prepared payment without provider transaction. PaymentId: {PaymentId}",
                            preauthPayment.Id);

                        preauthPayment.SetPreauthStatus(GetPreauthStatusForCancellation(cancellationStatus));
                        preauthPayment.AddPaymentStatusHistoryItem(cancellationStatus, preauthPayment.Money, cancellationReason);
                    }
                    else if (preauthPayment.Status != PaymentStatus.Canceled
                    && preauthPayment.Status != PaymentStatus.Expired)
                    {
                        _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Cancelling payment with provider. PaymentId: {PaymentId}, ProviderPaymentId: {ProviderPaymentId}",
                            preauthPayment.Id, preauthPayment.ProviderPaymentId);

                        // For PSP cancellations, we still need to support the new status
                        await pspService.CancelPreauthPaymentAsync(preauthPayment, cancellationToken);

                        // Override the PSP-set status with the desired cancellation status if different
                        if (preauthPayment.Status != cancellationStatus)
                        {
                            preauthPayment.AddPaymentStatusHistoryItem(cancellationStatus, preauthPayment.Money, cancellationReason);
                            preauthPayment.SetPreauthStatus(GetPreauthStatusForCancellation(cancellationStatus));
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PaymentService.CancelPreauthPaymentAsync: Error during payment cancellation. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                        preauthPayment.Id, preauthPayment.PaymentProvider);

                    if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
                        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money, ex.Message);
                }
                finally
                {
                    await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                }

                _logger.LogInformation("PaymentService.CancelPreauthPaymentAsync: Preauth payment cancellation completed. PaymentId: {PaymentId}, FinalStatus: {Status}, PreauthStatus: {PreauthStatus}",
                    preauthPayment.Id, preauthPayment.Status, preauthPayment.PreauthStatus);

                return preauthPayment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.CancelPreauthPaymentAsync: Unexpected error during preauth payment cancellation. PaymentId: {PaymentId}",
                    preauthPaymentId);
                throw;
            }
        }
    }

    public async Task<PreauthPaymentAggregate> FinalizePreauthPaymentAsync(string preauthPaymentId,
        JsonElement? dynamicFields,
        bool isWebhookCall = false,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = preauthPaymentId }))
        {
            _logger.LogInformation("PaymentService.FinalizePreauthPaymentAsync: Starting preauth payment finalization. PaymentId: {PaymentId}, IsWebhookCall: {IsWebhookCall}, HasDynamicFields: {HasDynamicFields}",
                preauthPaymentId, isWebhookCall, dynamicFields.HasValue);

            try
            {
                PaymentAggregate payment = await _paymentRepository.GetByIdAsync(preauthPaymentId, cancellationToken);
                if (payment is not PreauthPaymentAggregate preauthPayment)
                {
                    _logger.LogError("PaymentService.FinalizePreauthPaymentAsync: Finalization failed - not a preauth payment. PaymentId: {PaymentId}, PaymentType: {PaymentType}",
                        preauthPaymentId, payment.GetType().Name);
                    throw new DomainException("Finalization failed: Only preauthorized transactions can be finalized.");
                }

                _logger.LogInformation("PaymentService.FinalizePreauthPaymentAsync: Retrieved preauth payment for finalization. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, Status: {Status}, PreauthStatus: {PreauthStatus}, IsUpdate: {IsUpdate}",
                    preauthPayment.Id, preauthPayment.PolicyId, preauthPayment.PayorId, preauthPayment.Status, preauthPayment.PreauthStatus, preauthPayment.IsUpdate);

                if (!isWebhookCall)
                {
                    if (preauthPayment.IsFailure || preauthPayment.IsSuccessful)
                    {
                        _logger.LogInformation("PaymentService.FinalizePreauthPaymentAsync: Payment already finalized. PaymentId: {PaymentId}, IsFailure: {IsFailure}, IsSuccessful: {IsSuccessful}",
                            preauthPayment.Id, preauthPayment.IsFailure, preauthPayment.IsSuccessful);
                        return preauthPayment;
                    }
                }

                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(preauthPayment.PaymentProvider);
                    PaymentAggregate? prevPayment = null;
                    if (preauthPayment.IsUpdate)
                    {
                        _logger.LogInformation("PaymentService.FinalizePreauthPaymentAsync: Processing payment update finalization. PaymentId: {PaymentId}, PrevPaymentId: {PrevPaymentId}",
                            preauthPayment.Id, preauthPayment.PrevPaymentId);

                        if (string.IsNullOrWhiteSpace(preauthPayment.PrevPaymentId))
                            throw new DomainException("Finalization failed: PrevPaymentId is missing.");
                        prevPayment =
                            await _paymentRepository.GetByIdAsync(preauthPayment.PrevPaymentId!, cancellationToken);
                        if (prevPayment == null)
                            throw new DomainException(
                                "Finalization failed: Previously preauthorized transaction is missing.");

                    }

                    await pspService.FinalizePaymentAsync(preauthPayment, prevPayment as PreauthPaymentAggregate,
                        dynamicFields,
                        cancellationToken);
                }
                catch (Exception exception)
                {
                    _logger.LogError(exception, "PaymentService.FinalizePreauthPaymentAsync: Error during payment finalization. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                        preauthPayment.Id, preauthPayment.PaymentProvider);

                    if (!preauthPayment.Status.Equals(PaymentStatus.Failed))
                        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money,
                            exception.Message);
                }
                finally
                {
                    //it is flow for add new card
                    if (preauthPayment.IsUpdate || preauthPayment.IsCardUpdate)
                    {
                        if (preauthPayment.Status == PaymentStatus.Succeeded)
                        {
                            AddCreditCardUpdateDomainEvent(preauthPayment);
                            //  when success payment push refund for update card flow
                            await _mediator.Send(new RefundPaymentCommand(
                                   preauthPayment.Id,
                                   preauthPayment.Money.PaymentAmount,
                                   preauthPayment.Money.PaymentDecimalPrecision
                            ), cancellationToken);
                        }
                    }
                    else
                        AddPaymentDomainEvent(preauthPayment, PaymentType.Initial);

                    await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);

                    // Create PaymentMethod record if payment is successful
                    if (preauthPayment.IsSuccessful)
                    {
                        try
                        {
                            await _mediator.Send(new CreatePaymentMethodCommand(preauthPayment), cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "PaymentService.FinalizePreauthPaymentAsync: Error creating PaymentMethod record. PaymentId: {PaymentId}. Payment finalization was successful, but PaymentMethod creation failed.",
                                preauthPayment.Id);
                            // Don't throw exception - payment was successful, PaymentMethod creation is optional
                        }
                    }
                }

                if (preauthPayment is { IsSuccessful: false, IsFailure: false })
                {
                    _logger.LogError("PaymentService.FinalizePreauthPaymentAsync: Payment finalization resulted in invalid status. PaymentId: {PaymentId}, Status: {Status}",
                        preauthPayment.Id, preauthPayment.Status);
                    throw new IllegalPaymentStatusException(preauthPayment.Status);
                }

                _logger.LogInformation("PaymentService.FinalizePreauthPaymentAsync: Preauth payment finalization completed. PaymentId: {PaymentId}, FinalStatus: {Status}, PreauthStatus: {PreauthStatus}, IsUpdate: {IsUpdate}",
                    preauthPayment.Id, preauthPayment.Status, preauthPayment.PreauthStatus, preauthPayment.IsUpdate);

                return preauthPayment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.FinalizePreauthPaymentAsync: Unexpected error during preauth payment finalization. PaymentId: {PaymentId}",
                    preauthPaymentId);
                throw;
            }
        }
    }

    public async Task<RecurringPaymentAggregate> ProcessRecurringPaymentAsync(
        decimal amount,
        int decimalPrecision,
        string currencyDesc,
        string policyId,
        string invoiceNumber,
        string payorId,
        string? renewedFromPolicyId,
        CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("PaymentService.ProcessRecurringPaymentAsync: Starting recurring payment processing. PolicyId: {PolicyId}, PayorId: {PayorId}, Amount: {Amount} {CurrencyDesc}, InvoiceNumber: {InvoiceNumber}, RenewedFromPolicyId: {RenewedFromPolicyId}",
            policyId, payorId, amount, currencyDesc, invoiceNumber, renewedFromPolicyId ?? "null");

        try
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(policyId, nameof(policyId));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(invoiceNumber, nameof(invoiceNumber));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(payorId, nameof(payorId));
            GuardClause.IsZeroOrNegative(decimalPrecision, nameof(decimalPrecision));
            GuardClause.IsZeroOrNegative(amount, nameof(amount));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(currencyDesc, nameof(currencyDesc));
            
            string resolvePolicyId = ResolvePolicyId(renewedFromPolicyId, policyId);

            var payment = await ResolveInitialPaymentAsync(policyId, payorId, renewedFromPolicyId, resolvePolicyId,
                cancellationToken);

            var recurringPayment = new RecurringPaymentAggregate(payment.PaymentProvider,
                new PaymentMoney(CurrencyHelper.GetIsoCode(currencyDesc), currencyDesc, amount,
                    decimalPrecision), resolvePolicyId, invoiceNumber, payorId, null);

            using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = recurringPayment.Id }))
            {
                recurringPayment.SetInitialBearer(payment.InitialBearer!);
                recurringPayment.SetPspSettings(payment.PspSettings);
                recurringPayment.SetPayerData(payment.PayerData!);
                recurringPayment.SetDynamicFields(payment.DynamicFields);

                PaymentAggregate? prevPayment =
                    await GetPrevPaymentByPolicyIdAndPayorId(recurringPayment.PolicyId, recurringPayment.PayorId,
                        cancellationToken);
                SetInternalReference(prevPayment, recurringPayment);

                await VerifyPreviousPaymentByInvoiceNumberAsync(recurringPayment, cancellationToken);

                ValidatePayment(recurringPayment);

                recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, recurringPayment.Money);
                await _paymentRepository.InsertAsync(recurringPayment, cancellationToken);

                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(recurringPayment.PaymentProvider);
                    recurringPayment =
                        await pspService.RecurringPaymentAsync(recurringPayment, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PaymentService.ProcessRecurringPaymentAsync: Error processing recurring payment with PSP. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                        recurringPayment.Id, recurringPayment.PaymentProvider);

                    if (!recurringPayment.Status.Equals(PaymentStatus.Failed))
                        recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, recurringPayment.Money,
                            ex.Message);
                }
                finally
                {
                    AddPaymentDomainEvent(recurringPayment, PaymentType.Recurring);
                    await _paymentRepository.UpdateAsync(recurringPayment, cancellationToken);
                }

                _logger.LogInformation("PaymentService.ProcessRecurringPaymentAsync: Recurring payment processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}, PolicyId: {PolicyId}, PayorId: {PayorId}",
                    recurringPayment.Id, recurringPayment.Status, recurringPayment.PolicyId, recurringPayment.PayorId);

                return recurringPayment;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.ProcessRecurringPaymentAsync: Unexpected error during recurring payment processing. PolicyId: {PolicyId}, PayorId: {PayorId}",
                policyId, payorId);
            throw;
        }
    }
    
    private async Task<PaymentAggregate> ResolveInitialPaymentAsync(
        string policyId, 
        string payorId, 
        string? renewedFromPolicyId, 
        string resolvePolicyId, 
        CancellationToken cancellationToken)
    {
        PaymentAggregate? payment =
            await GetPreauthPaymentByPolicyIdAndPayorId(resolvePolicyId, payorId, cancellationToken);
        if (payment is null)
        {
            _logger.LogError("PaymentService.ProcessRecurringPaymentAsync: Unable to locate initial payment for recurring payment. PolicyId: {PolicyId}, PayorId: {PayorId}, SearchPolicyId: {SearchPolicyId}",
                policyId, payorId, renewedFromPolicyId ?? policyId);
            throw new DomainException($"Unable to locate the initial payment for payorId: '{payorId}', policyId: '{policyId}', renewedFromPolicyId: '{renewedFromPolicyId}'.", DomainErrorCodes.INITIAL_PAYMENT_NOT_FOUND);
        }

        if (string.IsNullOrEmpty(payment.InitialBearer?.Token))
        {
            _logger.LogError("PaymentService.ProcessRecurringPaymentAsync: Payment token not found for payment. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                payment.Id, payment.PaymentProvider);

            throw new DomainException($"Payment token not found for payment with Id: '{payment.Id}'.", DomainErrorCodes.CARD_TOKEN_NOT_FOUND);
        }

        _logger.LogInformation("PaymentService.ProcessRecurringPaymentAsync: Found initial payment for recurring payment. InitialPaymentId: {InitialPaymentId}, PaymentProvider: {PaymentProvider}",
            payment.Id, payment.PaymentProvider);

        return payment;
    }
    
    private string ResolvePolicyId(string? renewedFromPolicyId, string fallbackPolicyId)
    {
        if (!string.IsNullOrWhiteSpace(renewedFromPolicyId))
        {
            _logger.LogDebug("Using renewedFromPolicyId: '{RenewedFromPolicyId}'", renewedFromPolicyId);
            return renewedFromPolicyId;
        }

        _logger.LogDebug("renewedFromPolicyId is null or empty. Falling back to policyId: '{PolicyId}'", fallbackPolicyId);
        return fallbackPolicyId;
    }

    private async Task VerifyPreviousPaymentByInvoiceNumberAsync(PaymentAggregate payment,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(payment.InvoiceNumber))
            return;
        IEnumerable<PaymentAggregate> paymentsByInvoice =
            await GetAllPaymentsByInvoice(payment.InvoiceNumber, cancellationToken);
        if (paymentsByInvoice.Any(p => p.IsSuccessful))
        {
            throw new DomainException($"Impossible to pay the same invoice {payment.InvoiceNumber} twice.");
        }
    }

    private static void SetInternalReference(PaymentAggregate? prevPayment, PaymentAggregate curPayment)
    {
        if (prevPayment != null)
        {
            bool needToRetryPrevPaymentIsFailure =
                prevPayment.InvoiceNumber != null && curPayment.InvoiceNumber != null &&
                prevPayment.InvoiceNumber == curPayment.InvoiceNumber && prevPayment.IsFailure;
            if (needToRetryPrevPaymentIsFailure)
                curPayment.SetAttempt(prevPayment.Attempt + 1);
        }

        curPayment.SetInternalReference();
    }

    private async Task<PaymentAggregate?> GetPreauthPaymentByPolicyIdAndPayorId(string policyId, string payorId,
        CancellationToken cancellationToken)
    {
        _logger.LogDebug("PaymentService.GetPreauthPaymentByPolicyIdAndPayorId: Searching for preauth payment by PolicyId: {PolicyId}, PayorId: {PayorId}", policyId, payorId);

        IEnumerable<PaymentAggregate> payments =
            await _paymentRepository.FindAllByAsync(p => p.PolicyId == policyId && p.PayorId == payorId,
                cancellationToken);
        PaymentAggregate? payment = payments.LastOrDefault(p => p is PreauthPaymentAggregate);

        if (payment != null)
        {
            _logger.LogDebug("PaymentService.GetPreauthPaymentByPolicyIdAndPayorId: Found preauth payment. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                payment.Id, payment.PolicyId, payment.PayorId, payment.PaymentProvider);
        }
        else
        {
            _logger.LogDebug("PaymentService.GetPreauthPaymentByPolicyIdAndPayorId: No preauth payment found for PolicyId: {PolicyId}, PayorId: {PayorId}", policyId, payorId);
        }

        return payment;
    }

    private async Task<PaymentAggregate?> GetPrevPaymentByPolicyIdAndPayorId(string policyId, string payorId,
        CancellationToken cancellationToken)
    {
        IEnumerable<PaymentAggregate> payments =
            await _paymentRepository.FindAllByAsync(p => p.PolicyId == policyId && p.PayorId == payorId,
                cancellationToken);
        PaymentAggregate? payment = payments.LastOrDefault(p => p is RecurringPaymentAggregate);
        return payment;
    }

    private async Task<IEnumerable<PaymentAggregate>> GetAllPaymentsByInvoice(string? invoiceNumber,
        CancellationToken cancellationToken)
    {
        if (string.IsNullOrWhiteSpace(invoiceNumber))
            return ArraySegment<PaymentAggregate>.Empty;
        return await _paymentRepository.FindAllByAsync(p => p.InvoiceNumber == invoiceNumber,
            cancellationToken);
    }

    public async Task<PaymentAggregate> FailPaymentAsync(string paymentId,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = paymentId }))
        {
            _logger.LogInformation("PaymentService.FailPaymentAsync: Starting payment failure processing. PaymentId: {PaymentId}", paymentId);

            try
            {
                PaymentAggregate payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);

                _logger.LogInformation("PaymentService.FailPaymentAsync: Retrieved payment for failure processing. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, CurrentStatus: {Status}, PaymentProvider: {PaymentProvider}",
                    payment.Id, payment.PolicyId, payment.PayorId, payment.Status, payment.PaymentProvider);

                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(payment.PaymentProvider);
                    await pspService.FailPaymentAsync(payment, cancellationToken);
                }
                catch (Exception exception)
                {
                    _logger.LogError(exception, "PaymentService.FailPaymentAsync: Error during payment failure processing with PSP. PaymentId: {PaymentId}, PaymentProvider: {PaymentProvider}",
                        payment.Id, payment.PaymentProvider);

                    if (!payment.Status.Equals(PaymentStatus.Failed))
                        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, exception.Message);
                }
                finally
                {
                    await _paymentRepository.UpdateAsync(payment, cancellationToken);
                }

                _logger.LogInformation("PaymentService.FailPaymentAsync: Payment failure processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}",
                    payment.Id, payment.Status);

                return payment;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.FailPaymentAsync: Unexpected error during payment failure processing. PaymentId: {PaymentId}",
                    paymentId);
                throw;
            }
        }
    }

    public async Task<RefundAggregate> RefundPaymentAsync(string paymentId, decimal amount, int decimalPrecision,
        CancellationToken cancellationToken = default)
    {
        using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = paymentId }))
        {
            _logger.LogInformation("PaymentService.RefundPaymentAsync: Starting payment refund processing. PaymentId: {PaymentId}, RefundAmount: {Amount}, DecimalPrecision: {DecimalPrecision}",
                paymentId, amount, decimalPrecision);

            try
            {
                PaymentAggregate payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);

                _logger.LogInformation("PaymentService.RefundPaymentAsync: Retrieved payment for refund processing. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentAmount: {PaymentAmount}, AlreadyRefunded: {RefundedAmount}, RemainingAmount: {RemainingAmount}",
                    payment.Id, payment.PolicyId, payment.PayorId, payment.Money.PaymentAmount, payment.CalculateRefundedAmount(),
                    payment.Money.PaymentAmount - payment.CalculateRefundedAmount());

                if (amount > payment.Money.PaymentAmount - payment.CalculateRefundedAmount())
                {
                    _logger.LogError("PaymentService.RefundPaymentAsync: Refund amount exceeds remaining refundable amount. PaymentId: {PaymentId}, RequestedRefund: {RequestedAmount}, RemainingRefundable: {RemainingAmount}",
                        payment.Id, amount, payment.Money.PaymentAmount - payment.CalculateRefundedAmount());
                    throw new DomainException("Refund Amount must be less or equal of transaction remaining amount.");
                }

                var paymentRefund =
                    new RefundAggregate(payment.Id, RefundStatus.Failed,
                        new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc, amount,
                            decimalPrecision), null, payment.ProviderPaymentId);
                paymentRefund = await _refundRepository.InsertAsync(paymentRefund, cancellationToken);
                try
                {
                    IPaymentProviderService pspService = GetPaymentProviderService(payment.PaymentProvider);
                    paymentRefund = await pspService.RefundAsync(payment, paymentRefund, cancellationToken);
                    if (paymentRefund.IsSuccessful)
                    {
                        PaymentStatusHistoryItem? oldPaymentStatusHistoryItem = payment.PaymentStatusHistoryItem;
                        PaymentStatus status =
                            payment.CalculateRefundedAmount() + paymentRefund.Money.PaymentAmount ==
                            payment.Money.PaymentAmount
                                ? PaymentStatus.Refunded
                                : PaymentStatus.PartiallyRefunded;

                        payment.AddPaymentStatusHistoryItem(status, paymentRefund.Money);

                        if (oldPaymentStatusHistoryItem?.Status is PaymentStatus.Refunded
                            or PaymentStatus.PartiallyRefunded)
                        {
                            if (oldPaymentStatusHistoryItem.RefundId != null)
                                payment.PaymentStatusHistoryItem?.SetRefundId(
                                    oldPaymentStatusHistoryItem.RefundId.Value + 1);
                        }
                        else
                            payment.PaymentStatusHistoryItem?.SetRefundId(1);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PaymentService.RefundPaymentAsync: Error during payment refund processing. PaymentId: {PaymentId}, RefundId: {RefundId}, PaymentProvider: {PaymentProvider}",
                        payment.Id, paymentRefund.Id, payment.PaymentProvider);
                    if (!paymentRefund.Status.Equals(RefundStatus.Failed)) paymentRefund.SetStatus(RefundStatus.Failed);
                }
                finally
                {
                    await _paymentRepository.UpdateAsync(payment, cancellationToken);
                    await _refundRepository.UpdateAsync(paymentRefund, cancellationToken);
                }

                _logger.LogInformation("PaymentService.RefundPaymentAsync: Payment refund processing completed. PaymentId: {PaymentId}, RefundId: {RefundId}, RefundStatus: {RefundStatus}, PaymentStatus: {PaymentStatus}",
                    payment.Id, paymentRefund.Id, paymentRefund.Status, payment.Status);

                return paymentRefund;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "PaymentService.RefundPaymentAsync: Unexpected error during payment refund processing. PaymentId: {PaymentId}",
                    paymentId);
                throw;
            }
        }
    }

    public async Task<(string responseStr, HttpStatusCode httpStatusCode)> HandleWebhookAsync(string webhookBody,
        PaymentProvider provider, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("PaymentService.HandleWebhookAsync: Starting webhook processing. PaymentProvider: {PaymentProvider}, WebhookBodyLength: {BodyLength}",
            provider, webhookBody?.Length ?? 0);

        try
        {
            (
                string paymentId,
                string providerPaymentId,
                string externalReference,
                string internalReference,
                PaymentStatus paymentStatus,
                decimal? amount,
                string webhookAcknowledgeMessage,
                bool isFinalizationRequired,
                bool skip
            ) = await HandleWebhookByPspAsync(webhookBody, provider, cancellationToken);

            if (skip)
            {
                _logger.LogInformation("PaymentService.HandleWebhookAsync: Webhook processing skipped. PaymentProvider: {PaymentProvider}", provider);
                return (string.Empty, HttpStatusCode.OK);
            }

            PaymentAggregate? payment =
                await TryToFindPaymentAsync(paymentId, externalReference, internalReference, cancellationToken);

            if (payment == null)
            {
                _logger.LogWarning("PaymentService.HandleWebhookAsync: Payment not found for webhook. PaymentProvider: {PaymentProvider}, PaymentId: {PaymentId}, ExternalRef: {ExternalRef}, InternalRef: {InternalRef}",
                    provider, paymentId ?? "null", externalReference ?? "null", internalReference ?? "null");
                return (string.Empty, HttpStatusCode.BadRequest);
            }

            _logger.LogInformation("PaymentService.HandleWebhookAsync: Found payment for webhook processing. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, CurrentStatus: {CurrentStatus}, NewStatus: {NewStatus}",
                payment.Id, payment.PolicyId, payment.PayorId, payment.Status, paymentStatus);

            using (_logger.BeginScope(new Dictionary<string, object> { ["PaymentId"] = payment.Id }))
            {
                bool statusChanged = payment.Status != paymentStatus;
                if (!string.IsNullOrWhiteSpace(providerPaymentId) &&
                    string.IsNullOrWhiteSpace(payment.ProviderPaymentId))
                {
                    payment.AssignProviderTransaction(providerPaymentId);
                    if (!statusChanged)
                        await _paymentRepository.UpdateAsync(payment, cancellationToken);
                }

                if (!statusChanged)
                {
                    _logger.LogInformation("PaymentService.HandleWebhookAsync: Webhook processing completed without status change. PaymentId: {PaymentId}, Status: {Status}",
                        payment.Id, payment.Status);
                    return (webhookAcknowledgeMessage, HttpStatusCode.OK);
                }

                if (payment is PreauthPaymentAggregate preauthPaymentAggregate &&
                    paymentStatus == PaymentStatus.Succeeded)
                    preauthPaymentAggregate.SetPreauthStatus(PreauthPaymentStatus.ToBePaid);

                payment.AddPaymentStatusHistoryItem(paymentStatus,
                    amount != null
                        ? new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc,
                            amount.Value,
                            payment.Money.PaymentDecimalPrecision)
                        : payment.Money, webhookBody: webhookBody);

                await _paymentRepository.UpdateAsync(payment, cancellationToken);

                if (isFinalizationRequired)
                {
                    _logger.LogInformation("PaymentService.HandleWebhookAsync: Finalizing payment as required by webhook. PaymentId: {PaymentId}",
                        payment.Id);
                    await FinalizePreauthPaymentAsync(payment.Id,
                        payment.DynamicFields == null
                            ? null
                            : System.Text.Json.JsonSerializer.Deserialize<JsonElement>(payment.DynamicFields),
                        true,
                        cancellationToken);
                }

                _logger.LogInformation("PaymentService.HandleWebhookAsync: Webhook processing completed. PaymentId: {PaymentId}, FinalStatus: {Status}, StatusChanged: {StatusChanged}, FinalizationRequired: {FinalizationRequired}",
                    payment.Id, payment.Status, statusChanged, isFinalizationRequired);

                return (webhookAcknowledgeMessage, HttpStatusCode.OK);
            }
        }
        catch (UnauthorizedException ex)
        {
            _logger.LogWarning(ex, "PaymentService.HandleWebhookAsync: Unauthorized webhook request. PaymentProvider: {PaymentProvider}",
                provider);
            return (string.Empty, HttpStatusCode.Unauthorized);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.HandleWebhookAsync: Unexpected error during webhook processing. PaymentProvider: {PaymentProvider}",
                provider);
            return (string.Empty, HttpStatusCode.InternalServerError);
        }
    }

    public async Task<PaymentAggregate> RegisterPaymentAsync(RegisterPaymentCommand registerPaymentCommand,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("PaymentService.RegisterPaymentAsync: Starting payment registration. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}, TransactionType: {TransactionType}, Amount: {Amount} {CurrencyCode}, Status: {Status}, InvoiceNumber: {InvoiceNumber}",
            registerPaymentCommand.PolicyId, registerPaymentCommand.PayorId, registerPaymentCommand.PaymentProvider,
            registerPaymentCommand.TransactionType, registerPaymentCommand.Amount, registerPaymentCommand.CurrencyCode, registerPaymentCommand.Status, registerPaymentCommand.InvoiceNumber ?? "null");

        try
        {
            ValidateRegisterPaymentCommand(registerPaymentCommand);

            PaymentAggregate? payment;
            if (registerPaymentCommand.PaymentProvider != PaymentProvider.ExternalFile)
            {
                _logger.LogInformation("PaymentService.RegisterPaymentAsync: Processing non-external file payment registration. PolicyId: {PolicyId}, PayorId: {PayorId}",
                    registerPaymentCommand.PolicyId, registerPaymentCommand.PayorId);

                payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
                var finalizedPayment = await FinalizePreauthPaymentAsync(payment.Id, registerPaymentCommand.DynamicFields, false, cancellationToken);

                _logger.LogInformation("PaymentService.RegisterPaymentAsync: Payment registration completed. PaymentId: {PaymentId}, PaymentType: NonExternal, FinalStatus: {Status}",
                    finalizedPayment.Id, finalizedPayment.Status);

                return finalizedPayment;
            }

            _logger.LogInformation("PaymentService.RegisterPaymentAsync: Processing external file payment registration. TransactionType: {TransactionType}",
                registerPaymentCommand.TransactionType);

            PaymentType paymentType;
            switch (registerPaymentCommand.TransactionType)
            {
                case TransactionTypePremiumCollectedInitial:
                    {
                        paymentType = PaymentType.Initial;
                        payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
                        break;
                    }
                case TransactionTypePremiumCollectedEndorsement:
                    {
                        paymentType = PaymentType.Receipt;
                        payment = await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);
                        break;
                    }
                case TransactionTypePremiumCollectedRecurring:
                    {
                        paymentType = PaymentType.Recurring;
                        payment = await CreateRecurringPaymentAsync(registerPaymentCommand, cancellationToken);
                        break;
                    }
                case TransactionTypeRefund:
                case TransactionTypeRefundPaid:
                    {
                        _logger.LogInformation("PaymentService.RegisterPaymentAsync: Processing refund transaction. PolicyId: {PolicyId}, PayorId: {PayorId}",
                            registerPaymentCommand.PolicyId, registerPaymentCommand.PayorId);

                        payment = await GetPaymentByUniqueConstraintAsync(registerPaymentCommand, cancellationToken) ??
                                  await CreateNewPreauthPaymentAsync(registerPaymentCommand, cancellationToken);

                        var refundResult = await CreateRefundAsync(payment, registerPaymentCommand, cancellationToken);

                        _logger.LogInformation("PaymentService.RegisterPaymentAsync: Payment registration completed. PaymentId: {PaymentId}, PaymentType: Refund, FinalStatus: {Status}",
                            refundResult.Id, refundResult.Status);

                        return refundResult;
                    }
                default:
                    _logger.LogError("PaymentService.RegisterPaymentAsync: Invalid transaction type. TransactionType: {TransactionType}",
                        registerPaymentCommand.TransactionType);
                    throw new DomainException(
                        $"Invalid transaction type. Should be '{TransactionTypePremiumCollectedInitial}' or '{TransactionTypePremiumCollectedRecurring}' or '{TransactionTypePremiumCollectedEndorsement}' or '{TransactionTypeRefund}'.");
            }

            SyncPaymentStatus(payment, registerPaymentCommand);
            SetPaymentMethodIfRequired(registerPaymentCommand, payment);

            if (payment.DomainEvents.Count > 0)
                payment.ClearDomainEvents();

            AddPaymentDomainEvent(payment, paymentType);

            await _paymentRepository.UpdateAsync(payment, cancellationToken);

            _logger.LogInformation("PaymentService.RegisterPaymentAsync: Payment registration completed. PaymentId: {PaymentId}, PaymentType: {PaymentType}, FinalStatus: {Status}",
                payment.Id, paymentType, payment.Status);

            return payment;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentService.RegisterPaymentAsync: Unexpected error during payment registration. PolicyId: {PolicyId}, PayorId: {PayorId}, TransactionType: {TransactionType}",
                registerPaymentCommand.PolicyId, registerPaymentCommand.PayorId, registerPaymentCommand.TransactionType);
            throw;
        }
    }

    private static void SetPaymentMethodIfRequired(RegisterPaymentCommand registerPaymentCommand,
        PaymentAggregate? payment)
    {
        if (!string.IsNullOrWhiteSpace(registerPaymentCommand.PaymentMethod) &&
            string.IsNullOrWhiteSpace(payment!.PaymentMethod))
            payment.SetPaymentMethod(registerPaymentCommand.PaymentMethod);
    }

    private void SyncPaymentStatus(PaymentAggregate payment, RegisterPaymentCommand command)
    {
        PaymentStatus updatedStatus = command.Status switch
        {
            TransactionSucceededStatus => PaymentStatus.Succeeded,
            TransactionFailedStatus => PaymentStatus.Failed,
            _ => throw new DomainException("Invalid payment status.")
        };

        if (payment.Status == updatedStatus)
            return;
        _logger.LogInformation(
            "Syncing payment status for PaymentId: {PaymentId}, OldStatus: {OldStatus}, NewStatus: {NewStatus}",
            payment.Id, payment.Status, updatedStatus);

        payment.AddPaymentStatusHistoryItem(updatedStatus, payment.Money,
            updatedStatus == PaymentStatus.Failed ? command.Error ?? "Failed by client." : null);
    }

    private async Task<PaymentAggregate> CreateRefundAsync(PaymentAggregate payment, RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        if (command.Amount > payment.Money.PaymentAmount - payment.CalculateRefundedAmount())
            throw new DomainException("Refund amount is greater than the remaining refundable amount.");

        RefundAggregate refund =
            await CreateRefundAggregateAsync(payment, command, cancellationToken);

        _logger.LogInformation("Refund status is {Status} for payment: {PaymentId}", payment.Id, refund.Status);

        SetPaymentMethodIfRequired(command, payment);

        if (!refund.IsSuccessful)
        {
            AddPaymentDomainEvent(payment, PaymentType.Refund, refund);
            await _paymentRepository.UpdateAsync(payment, cancellationToken);
            return payment;
        }

        payment.AddPaymentStatusHistoryItem(
            refund.Money.PaymentAmount == payment.Money.PaymentAmount
                ? PaymentStatus.Refunded
                : PaymentStatus.PartiallyRefunded, refund.Money);

        AddPaymentDomainEvent(payment, PaymentType.Refund, refund);

        await _paymentRepository.UpdateAsync(payment, cancellationToken);

        return payment;
    }

    private async Task<RefundAggregate> CreateRefundAggregateAsync(PaymentAggregate payment,
        RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        RefundStatus updatedStatus = command.Status switch
        {
            TransactionSucceededStatus => RefundStatus.Succeeded,
            TransactionFailedStatus => RefundStatus.Failed,
            _ => throw new DomainException("Invalid payment status.")
        };

        var refund = new RefundAggregate(
            payment.Id,
            updatedStatus,
            new PaymentMoney(payment.Money.PaymentCurrencyCode, payment.Money.PaymentCurrencyDesc, command.Amount, 2),
            command.Error,
            payment.ProviderPaymentId
        );

        return await _refundRepository.InsertAsync(refund, cancellationToken);
    }

    private async Task<RecurringPaymentAggregate> CreateRecurringPaymentAsync(
        RegisterPaymentCommand command, CancellationToken cancellationToken)
    {
        var recurringPayment = new RecurringPaymentAggregate(
            command.PaymentProvider,
            CreatePaymentMoney(command),
            command.PolicyId,
            command.InvoiceNumber,
            command.PayorId,
            command.EffectiveDate
        );

        recurringPayment.SetInternalReference();
        recurringPayment.SetDynamicFields(command.DynamicFields);

        ValidatePayment(recurringPayment);
        recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, recurringPayment.Money);

        await _paymentRepository.InsertAsync(recurringPayment, cancellationToken);
        return recurringPayment;
    }

    private static PaymentMoney CreatePaymentMoney(RegisterPaymentCommand command) =>
        new(
            CurrencyHelper.GetIsoCode(command.CurrencyCode),
            command.CurrencyCode,
            command.Amount,
            2
        );

    private async Task<PreauthPaymentAggregate> CreateNewPreauthPaymentAsync(RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        var preauthPayment = new PreauthPaymentAggregate(
            command.PaymentProvider,
            CreatePaymentMoney(command),
            command.PolicyId,
            command.InvoiceNumber,
            command.PayorId,
            command.EffectiveDate
        );

        ValidatePayment(preauthPayment);

        preauthPayment.SetInternalReference();
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Created, preauthPayment.Money);
        preauthPayment.SetDynamicFields(command.DynamicFields);

        if (command.PaymentProvider != PaymentProvider.ExternalFile)
        {
            await PopulatePspSettingsAsync(preauthPayment, cancellationToken);
        }

        await _paymentRepository.InsertAsync(preauthPayment, cancellationToken);
        _logger.LogInformation("Created new PreauthPaymentAggregate for policy: {PolicyId} and payorId: {PayorId}",
            command.PolicyId, command.PayorId);

        return preauthPayment;
    }

    private async Task<PaymentAggregate?> GetPaymentByUniqueConstraintAsync(RegisterPaymentCommand command,
        CancellationToken cancellationToken)
    {
        PaymentAggregate? payment =
            await GetPreauthPaymentByPolicyIdAndPayorId(command.PolicyId, command.PayorId, cancellationToken);
        if (payment != null && string.IsNullOrWhiteSpace(command.InvoiceNumber))
            return payment.InvoiceNumber == command.InvoiceNumber ? payment : null;

        return payment;
    }

    private static void ValidateRegisterPaymentCommand(RegisterPaymentCommand command)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.PayorId, nameof(command.PayorId));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.PolicyId, nameof(command.PolicyId));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(command.TransactionType, nameof(command.TransactionType));
        GuardClause.IsZeroOrNegative(command.Amount, nameof(command.Amount));
    }

    private async Task<PaymentAggregate?> TryToFindPaymentAsync(string paymentId,
        string externalReference, string internalReference, CancellationToken cancellationToken)
    {
        PaymentAggregate? payment = null;
        if (!string.IsNullOrWhiteSpace(paymentId))
            payment = await _paymentRepository.GetByIdAsync(paymentId, cancellationToken);
        else if (!string.IsNullOrWhiteSpace(externalReference))
            payment = (await _paymentRepository.FindAllByAsync(
                    p => p.ExternalReference != null && p.ExternalReference == externalReference,
                    cancellationToken))
                .FirstOrDefault();
        else if (!string.IsNullOrWhiteSpace(internalReference))
            payment = (await _paymentRepository.FindAllByAsync(
                    p => p.InternalReference == internalReference,
                    cancellationToken))
                .FirstOrDefault();

        return payment;
    }

    private async Task<PreauthPaymentAggregate> PreparePaymentAsync(PreauthPaymentAggregate preauthPayment,
        IPaymentProviderService pspService, CancellationToken cancellationToken)
    {
        preauthPayment = await pspService.PreparePaymentAsync(preauthPayment, cancellationToken);
        preauthPayment.SetPreauthStatus(PreauthPaymentStatus.ToBePaid);
        switch (preauthPayment.Status)
        {
            case PaymentStatus.Prepared:
            case PaymentStatus.Failed:
                await _paymentRepository.UpdateAsync(preauthPayment, cancellationToken);
                break;
        }

        return preauthPayment;
    }

    private async
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus, decimal
            ? amount, string
            webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookByPspAsync(
            string webhookBody,
            PaymentProvider provider,
            CancellationToken cancellationToken = default)
    {
        IPaymentProviderService pspService = GetPaymentProviderService(provider);
        IPspSettingsProvider pspSettingsProvider = GetPspSettingsProvider(provider);
        PspSettingsAggregate? pspSettings = await pspSettingsProvider.GetPspSettingsAsync(cancellationToken);

        return await pspService.HandleWebhookAsync(webhookBody, pspSettings, cancellationToken);
    }

    private async Task PopulatePspSettingsAsync(PaymentAggregate payment, CancellationToken cancellationToken)
    {
        PspSettingsAggregate? pspSettings = await GetPspSettingsProvider(payment.PaymentProvider)
            .GetPspSettingsAsync(cancellationToken);

        payment.SetPspSettings(JsonConvert.SerializeObject(pspSettings, Formatting.Indented));
    }

    private IPspSettingsProvider GetPspSettingsProvider(PaymentProvider type)
    {
        IPspSettingsProvider? provider = _pspSettingsProviders.SingleOrDefault(p => p.Type == type);
        if (provider != null) return provider;

        _logger.LogWarning($"Trying to use unsupported psp settings provider {type}");
        throw new NotImplementedException("Psp settings provider not supported.");
    }

    private static void ValidatePayment(PaymentAggregate payment)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(payment.Money, nameof(payment.Money));
        if (!IsUpdatePreauthPayment(payment))
            GuardClause.IsZeroOrNegative(payment.Money.PaymentAmount, nameof(payment.Money.PaymentAmount));

        GuardClause.IsZeroOrNegative(payment.Money.PaymentDecimalPrecision,
            nameof(payment.Money.PaymentDecimalPrecision));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.Money.PaymentCurrencyCode,
            nameof(payment.Money.PaymentCurrencyCode));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.Money.PaymentCurrencyDesc,
            nameof(payment.Money.PaymentCurrencyDesc));
    }

    private static bool IsUpdatePreauthPayment(PaymentAggregate payment) =>
        payment is PreauthPaymentAggregate { IsUpdate: true };

    private IPaymentProviderService GetPaymentProviderService(PaymentProvider paymentProvider)
    {
        IPaymentProviderService? paymentProviderService =
            _paymentProviderServices.FirstOrDefault(service => service.Type.Equals(paymentProvider));
        if (paymentProviderService != null) return paymentProviderService;

        _logger.LogWarning($"Trying to use unsupported payment provider {paymentProvider}");
        throw new NotImplementedException("Payment provider not supported");
    }

    private static void AddPaymentDomainEvent(PaymentAggregate payment, PaymentType paymentType,
        RefundAggregate? refund = null)
    {
        if (payment is { IsSuccessful: false, IsFailure: false }) return;

        PSPBearerPseudoCC pseudoCc = payment.InitialBearer as PSPBearerPseudoCC ?? new PSPBearerPseudoCC();
        Money money = CreateMoney(payment, refund);
        CreditCard creditCard = CreateCreditCard(pseudoCc);
        PaymentProviderInfo paymentProviderInfo = CreatePaymentProviderInfo(payment);
        PaymentBankInfo? paymentBankInfo = CreatePaymentBankInfo(payment);

        if (payment.IsSuccessful)
        {
            if (refund is { IsSuccessful: false })
            {
                var failedEvent = new PaymentFailedDomainEvent
                {
                    PolicyId = payment.PolicyId,
                    InvoiceNumber = payment.InvoiceNumber,
                    PayorId = payment.PayorId,
                    Status = refund.Status.ToString(),
                    EffectiveDate = GetEffectiveDate(payment),
                    Money = money,
                    CreditCard = creditCard,
                    PaymentProviderInfo = paymentProviderInfo,
                    FailureMessage = refund.ErrorDetails ?? "Failed by client.",
                    Type = paymentType.ToString(),
                    ReferenceId = payment.Id,
                    PaymentMethod = payment.PaymentMethod
                };

                if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

                payment.AddDomainEvent(failedEvent);
            }
            else
            {
                var succeededEvent = new PaymentSucceededDomainEvent
                {
                    PolicyId = payment.PolicyId,
                    InvoiceNumber = payment.InvoiceNumber,
                    PayorId = payment.PayorId,
                    Status = refund?.Status.ToString() ?? payment.Status.ToString() ?? string.Empty,
                    EffectiveDate = GetEffectiveDate(payment),
                    Money = money,
                    CreditCard = creditCard,
                    PaymentProviderInfo = paymentProviderInfo,
                    Type = paymentType.ToString(),
                    ReferenceId = payment.Id,
                    PaymentMethod = payment.PaymentMethod,
                    PaymentBankInfo = paymentBankInfo
                };

                if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

                payment.AddDomainEvent(succeededEvent);
            }
        }
        else if (payment.IsFailure)
        {
            var failedEvent = new PaymentFailedDomainEvent
            {
                PolicyId = payment.PolicyId,
                InvoiceNumber = payment.InvoiceNumber,
                PayorId = payment.PayorId,
                Status = payment.Status.ToString() ?? string.Empty,
                EffectiveDate = GetEffectiveDate(payment),
                Money = money,
                CreditCard = creditCard,
                PaymentProviderInfo = paymentProviderInfo,
                FailureMessage = payment.PaymentStatusHistoryItem?.Error ?? string.Empty,
                Type = paymentType.ToString(),
                ReferenceId = payment.Id,
                PaymentMethod = payment.PaymentMethod
            };

            if (payment.DomainEvents.Count > 0) payment.ClearDomainEvents();

            payment.AddDomainEvent(failedEvent);
        }
    }

    private static void AddCreditCardUpdateDomainEvent(PaymentAggregate payment)
    {
        if (payment is { IsSuccessful: false, IsFailure: false })
            return;

        PSPBearerPseudoCC pseudoCc = payment.InitialBearer as PSPBearerPseudoCC ?? new PSPBearerPseudoCC();
        CreditCard creditCard = CreateCreditCard(pseudoCc);
        IDomainEvent? domainEvent = null;
        if (payment.IsSuccessful)
            domainEvent = new CreditCardUpdateSucceededDomainEvent
            {
                PolicyId = payment.PolicyId,
                PayorId = payment.PayorId,
                EffectiveDate = GetEffectiveDate(payment),
                CreditCard = creditCard,
                PaymentMethod = payment.PaymentMethod
            };
        else if (payment.IsFailure)
            domainEvent = new CreditCardUpdateFailedDomainEvent
            {
                PolicyId = payment.PolicyId,
                PayorId = payment.PayorId,
                EffectiveDate = GetEffectiveDate(payment),
                CreditCard = creditCard,
                FailureMessage = payment.PaymentStatusHistoryItem?.Error ?? string.Empty,
                PaymentMethod = payment.PaymentMethod
            };

        if (domainEvent != null)
        {
            if (payment.DomainEvents.Count > 0)
                payment.ClearDomainEvents();
            payment.AddDomainEvent(domainEvent);
        }
    }

    private static DateTime GetEffectiveDate(PaymentAggregate payment) =>
        payment.EffectiveDate ?? payment.PaymentStatusHistoryItem?.CreatedAtDateUtc ?? DateTime.UtcNow;

    private static Money CreateMoney(PaymentAggregate payment, RefundAggregate? refund) =>
        new()
        {
            CurrencyCode = refund?.Money.PaymentCurrencyCode ?? payment.Money.PaymentCurrencyCode,
            CurrencyDesc = refund?.Money.PaymentCurrencyDesc ?? payment.Money.PaymentCurrencyDesc,
            Amount = refund?.Money.PaymentAmount ?? payment.Money.PaymentAmount,
            DecimalPrecision = refund?.Money.PaymentDecimalPrecision ?? payment.Money.PaymentDecimalPrecision
        };

    private static CreditCard CreateCreditCard(PSPBearerPseudoCC pseudoCc) =>
        new()
        {
            Holder = pseudoCc.Holder,
            CardNumber = pseudoCc.TruncatedCardPan,
            CardType = pseudoCc.CardType,
            Country = pseudoCc.Country,
            ExpiryYear = pseudoCc.ExpiryYear,
            ExpiryMonth = pseudoCc.ExpiryMonth
        };

    private static PaymentProviderInfo CreatePaymentProviderInfo(PaymentAggregate payment)
    {
        var paymentProviderInfo = new PaymentProviderInfo
        {
            ExternalPaymentReference = payment.ExternalReference,
            InternalPaymentReference = payment.Id,
            PaymentProvider = payment.PaymentProvider.ToString()
        };

        if (payment is PreauthPaymentAggregate preauthPayment)
            paymentProviderInfo.InitializationToken = preauthPayment.InitializationToken;

        return paymentProviderInfo;
    }

    private static PaymentBankInfo? CreatePaymentBankInfo(PaymentAggregate payment)
    {
        if (string.IsNullOrWhiteSpace(payment.DynamicFields))
        {
            return null;
        }

        try
        {
            return System.Text.Json.JsonSerializer.Deserialize<PaymentBankInfo>(payment.DynamicFields);
        }
        catch (System.Text.Json.JsonException)
        {
            return null;
        }
    }

    private static void ValidateCancellationStatus(PaymentStatus cancellationStatus)
    {
        // Define valid cancellation statuses
        var validCancellationStatuses = new[]
        {
            PaymentStatus.Canceled,
            PaymentStatus.Expired,
            PaymentStatus.Failed
        };

        if (!validCancellationStatuses.Contains(cancellationStatus))
        {
            throw new DomainException($"Invalid cancellation status: {cancellationStatus}. Valid statuses are: {string.Join(", ", validCancellationStatuses)}");
        }
    }

    private static PreauthPaymentStatus GetPreauthStatusForCancellation(PaymentStatus cancellationStatus)
    {
        return cancellationStatus switch
        {
            PaymentStatus.Canceled => PreauthPaymentStatus.Cancelled,
            PaymentStatus.Expired => PreauthPaymentStatus.Cancelled, // Map expired to cancelled preauth status
            PaymentStatus.Failed => PreauthPaymentStatus.CancelFailed,
            _ => PreauthPaymentStatus.CancelFailed
        };
    }

    private async Task<string?> GetTenantConfigRedirectUrlAsync(string clientKey, CancellationToken cancellationToken = default)
    {
        if (!_tenantProvider.TryGetCurrent(out TenantId? tenantId))
            return null;

        TenantSettings tenantSettings = await _authService.GetTenantSettingsAsync(tenantId.Value, cancellationToken);

        string? memberPortalOrigin = tenantSettings?.Hosts?.FirstOrDefault(h => h.Contains(clientKey));
        if (string.IsNullOrWhiteSpace(memberPortalOrigin))
            return null;

        memberPortalOrigin = EnsureUrlHasSchema(memberPortalOrigin);
        return memberPortalOrigin;
    }

    private static string? ExtractClientKeyFromDynamicFields(JsonElement? dynamicFields)
    {
        if (dynamicFields == null)
            return null;

        try
        {
            if (dynamicFields.Value.TryGetProperty("clientKey", out JsonElement clientKeyElement))
            {
                return clientKeyElement.GetString();
            }
        }
        catch (System.Text.Json.JsonException)
        {
            // Invalid JSON, return null
        }

        return null;
    }
}