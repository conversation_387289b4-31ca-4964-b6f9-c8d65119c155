using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Api.PspSettings.Errors;
using CoverGo.Payments.Application.PspSettings.Commands.CreatePspSettings;
using CoverGo.Payments.Application.PspSettings.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.PspSettings.CreatePspSettings;

[MutationType]
public class CreatePspSettingsMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(PspSettingsAlreadyExistsError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "result")]
    //[Authorize]
    public async Task<ResultDto> CreatePspSettings(
        CreatePspSettingsCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}