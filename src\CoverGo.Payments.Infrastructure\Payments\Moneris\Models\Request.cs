﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class Request
{
    [JsonProperty("txn_total")]
    public string? TxnTotal { get; set; }

    [JsonProperty("cust_info")]
    public CustomerInfo? CustomerInfo { get; set; }

    [JsonProperty("shipping")]
    public Address? Shipping { get; set; }

    [JsonProperty("billing")]
    public Address? Billing { get; set; }

    [JsonProperty("cc_total")]
    public string? CcTotal { get; set; }

    [JsonProperty("pay_by_token")]
    public string? PayByToken { get; set; }

    [JsonProperty("cc")]
    public CreditCard? Cc { get; set; }

    [JsonProperty("ticket")]
    public string? Ticket { get; set; }

    [JsonProperty("cust_id")]
    public string? CustId { get; set; }

    [JsonProperty("dynamic_descriptor")]
    public string? DynamicDescriptor { get; set; }

    [JsonProperty("order_no")]
    public string? OrderNo { get; set; }

    [JsonProperty("eci")]
    public string? Eci { get; set; }

    [JsonProperty("issuer_id")]
    public string? IssuerId { get; set; }

    [JsonProperty("mcp")]
    public Mcp? Mcp { get; set; }
}