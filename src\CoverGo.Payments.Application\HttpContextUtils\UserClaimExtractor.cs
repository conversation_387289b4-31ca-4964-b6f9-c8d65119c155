// implement IUserClaimExtractor to extract user entity ID from claims
using System.Security.Claims;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Payments.Application.HttpContextUtils
{
    public class UserClaimExtractor : IUserClaimExtractor
    {
        private readonly IHttpContextAccessor _httpContextAccessor;

        public UserClaimExtractor(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public string GetUserEntityId()
        {
            return _httpContextAccessor.HttpContext?.User.FindFirst("entityId")?.Value;
        }

        public ClaimsIdentity GetClaimsIdentity()
        {
            return _httpContextAccessor.HttpContext!.User.Identity as ClaimsIdentity;
        }
    }
}
