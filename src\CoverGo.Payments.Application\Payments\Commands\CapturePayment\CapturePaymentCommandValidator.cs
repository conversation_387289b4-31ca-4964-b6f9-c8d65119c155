﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.CapturePayment
{
    public class CapturePaymentCommandValidator : AbstractValidator<CapturePaymentCommand>
    {
        public CapturePaymentCommandValidator(
            ILogger<CapturePaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PaymentId).NotEmpty().WithMessage("No paymentId found.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
