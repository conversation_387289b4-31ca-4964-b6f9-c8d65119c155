﻿using CoverGo.Payments.Application.Encryption;
using GuardClauses;
using System.Security.Cryptography;

namespace CoverGo.Payments.Infrastructure.Encryption.Symmetric
{
    public class AesEncryptionService : IEncryptionService
    {
        private readonly Aes _aesCipher;
        public AesEncryptionService(
            string key,
            string iv,
            int keySize,
            CipherMode cipherMode,
            PaddingMode paddingMode)
        {
            GuardClause.IsNullOrWhiteSpace(key, nameof(key));
            GuardClause.IsNullOrWhiteSpace(iv, nameof(iv));

            if (!(keySize == 128 || keySize == 192 || keySize == 256))
                throw new ArgumentException("invalid key size");

            byte[] keyBytes = Convert.FromBase64String(key);
            byte[] ivBytes = Convert.FromBase64String(iv);

            if (keyBytes.Length * 8 != keySize)
                throw new Exception($"key is not of size {keySize}");
            if (ivBytes.Length != 16)
                throw new Exception("invalid iv");

            _aesCipher = Aes.Create();
            _aesCipher.KeySize = keySize;
            _aesCipher.Mode = cipherMode;
            _aesCipher.Padding = paddingMode;
            _aesCipher.Key = keyBytes;
            _aesCipher.IV = ivBytes;
        }

        public string Encrypt(string plainText)
        {
            GuardClause.IsNullOrWhiteSpace(plainText, nameof(plainText));

            // Create an encryptor to perform the stream transform.
            using ICryptoTransform encryptor = _aesCipher.CreateEncryptor(_aesCipher.Key, _aesCipher.IV);

            // Create the streams used for encryption.
            using var msEncrypt = new MemoryStream();
            using var csEncrypt = new CryptoStream(msEncrypt, encryptor, CryptoStreamMode.Write);
            using (var swEncrypt = new StreamWriter(csEncrypt))
            {
                //Write all data to the stream.
                swEncrypt.Write(plainText);
            }

            byte[] encryptedBytes = msEncrypt.ToArray();
            return Convert.ToBase64String(encryptedBytes);
        }
        public string Decrypt(string cipherText)
        {
            GuardClause.IsNullOrWhiteSpace(cipherText, nameof(cipherText));

            // Create a decryptor to perform the stream transform.
            using ICryptoTransform decryptor = _aesCipher.CreateDecryptor(_aesCipher.Key, _aesCipher.IV);

            var cipherBytes = Convert.FromBase64String(cipherText);

            // Create the streams used for decryption.
            using var msDecrypt = new MemoryStream(cipherBytes);
            using var csDecrypt = new CryptoStream(msDecrypt, decryptor, CryptoStreamMode.Read);
            using var srDecrypt = new StreamReader(csDecrypt);
            // Read the decrypted bytes from the decrypting stream
            // and place them in a string.
            string plaintext = srDecrypt.ReadToEnd();

            return plaintext;
        }
    }
}
