﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.ING.Models;

public class DebitPaymentProfileResponse
{
    [JsonProperty(IngContainer.PropsName.Transaction)]
    public Transaction Transaction { get; set; }
}

public class Transaction
{
    [JsonProperty(IngContainer.PropsName.Id)]
    public string Id { get; set; }

    [JsonProperty(IngContainer.PropsName.Type)]
    public string Type { get; set; }

    [JsonProperty(IngContainer.PropsName.Source)]
    public string Source { get; set; }

    [JsonProperty(IngContainer.PropsName.Created)]
    public long Created { get; set; }

    [JsonProperty(IngContainer.PropsName.Modified)]
    public long Modified { get; set; }

    [JsonProperty(IngContainer.PropsName.NotificationUrl)]
    public string NotificationUrl { get; set; }

    [JsonProperty(IngContainer.PropsName.Status)]
    public string Status { get; set; }

    [JsonProperty(IngContainer.PropsName.ServiceId)]
    public string ServiceId { get; set; }

    [JsonProperty(IngContainer.PropsName.Amount)]
    public decimal Amount { get; set; }

    [JsonProperty(IngContainer.PropsName.Currency)]
    public string Currency { get; set; }

    [JsonProperty(IngContainer.PropsName.Title)]
    public string Title { get; set; }

    [JsonProperty(IngContainer.PropsName.OrderId)]
    public string OrderId { get; set; }

    [JsonProperty(IngContainer.PropsName.PaymentMethod)]
    public string PaymentMethod { get; set; }

    [JsonProperty(IngContainer.PropsName.PaymentMethodCode)]
    public string PaymentMethodCode { get; set; }

    [JsonProperty(IngContainer.PropsName.Payment)]
    public Payment Payment { get; set; }

    [JsonProperty(IngContainer.PropsName.StatusCode)]
    public string StatusCode { get; set; }

    [JsonProperty(IngContainer.PropsName.StatusCodeDescription)]
    public string StatusCodeDescription { get; set; }

    [JsonProperty(IngContainer.PropsName.PaymentProfiles)]
    public PaymentProfile PaymentProfile { get; set; }
}

public class Payment
{
    [JsonProperty(IngContainer.PropsName.Id)]
    public string Id { get; set; }

    [JsonProperty(IngContainer.PropsName.Status)]
    public string Status { get; set; }
}