﻿
Microsoft Visual Studio Solution File, Format Version 12.00
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "_docker", "_docker", "{DAF07A0E-D36B-454C-9B81-37B8DB153238}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
		Dockerfile = Dockerfile
		docker-compose.ci.yml = docker-compose.ci.yml
		Mongo.Dockerfile = Mongo.Dockerfile
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Domain", "src\CoverGo.Payments.Domain\CoverGo.Payments.Domain.csproj", "{8CFFFD1F-4DFF-4E14-BA72-A389D0B5C648}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Application", "src\CoverGo.Payments.Application\CoverGo.Payments.Application.csproj", "{BBD74DD5-03ED-4CA5-AA9E-2190B4C0E7A4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Infrastructure", "src\CoverGo.Payments.Infrastructure\CoverGo.Payments.Infrastructure.csproj", "{29B26A5C-F5D5-4162-88F2-870A4BF5A928}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Api", "src\CoverGo.Payments.Api\CoverGo.Payments.Api.csproj", "{BE713318-32AE-4D67-9334-B4605CDD965D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{EE2C147D-2278-48F1-B01A-EFB4819F0227}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.IntegrationTests", "src\CoverGo.Payments.IntegrationTests\CoverGo.Payments.IntegrationTests.csproj", "{65F724DC-3B34-4800-A66F-908E2C4161B9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.UnitTests", "src\CoverGo.Payments.UnitTests\CoverGo.Payments.UnitTests.csproj", "{B65FB304-EEBC-470E-BED5-59A936FEA259}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "_helm", "_helm", "{9F32E407-F85B-4134-BCFB-AAA1856EA6BD}"
	ProjectSection(SolutionItems) = preProject
		helm\Chart.yaml = helm\Chart.yaml
		helm\values.yaml = helm\values.yaml
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{DA3B012A-7A6C-4D48-94CD-24BDF7CFD62D}"
	ProjectSection(SolutionItems) = preProject
		README.md = README.md
		Directory.Packages.props = Directory.Packages.props
		coverlet.runsettings = coverlet.runsettings
		nuget.config = nuget.config
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		integrations\moneris.html = integrations\moneris.html
		integrations\ing.html = integrations\ing.html
		dapr\components\platform-pubsub-rb.yaml = dapr\components\platform-pubsub-rb.yaml
		dapr\configuration\platform-config.yaml = dapr\configuration\platform-config.yaml
		integrations\monerisUpdate.html = integrations\monerisUpdate.html
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Tests.GatewayClient", "src\CoverGo.Payments.Tests.GatewayClient\CoverGo.Payments.Tests.GatewayClient.csproj", "{2AD0B196-7118-44C0-AAD5-4B62315A3CF8}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "CoverGo.Payments.Integration.Events", "src\CoverGo.Payments.Integration.Events\CoverGo.Payments.Integration.Events.csproj", "{1FDA0067-772A-4328-AB98-B41036C62045}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "_darp", "_darp", "{B62A5F43-858D-4B02-8B66-D50698581C7A}"
	ProjectSection(SolutionItems) = preProject
		dapr\components\platform-pubsub-rb.yaml = dapr\components\platform-pubsub-rb.yaml
		dapr\configuration\platform-config.yaml = dapr\configuration\platform-config.yaml
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CoverGo.Payments.Tests.PaymentsClient", "src\CoverGo.Payments.Tests.PaymentsClient\CoverGo.Payments.Tests.PaymentsClient.csproj", "{5793AE2A-D919-414C-879C-8F97EBDC7551}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{8CFFFD1F-4DFF-4E14-BA72-A389D0B5C648}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8CFFFD1F-4DFF-4E14-BA72-A389D0B5C648}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8CFFFD1F-4DFF-4E14-BA72-A389D0B5C648}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8CFFFD1F-4DFF-4E14-BA72-A389D0B5C648}.Release|Any CPU.Build.0 = Release|Any CPU
		{BBD74DD5-03ED-4CA5-AA9E-2190B4C0E7A4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BBD74DD5-03ED-4CA5-AA9E-2190B4C0E7A4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BBD74DD5-03ED-4CA5-AA9E-2190B4C0E7A4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BBD74DD5-03ED-4CA5-AA9E-2190B4C0E7A4}.Release|Any CPU.Build.0 = Release|Any CPU
		{29B26A5C-F5D5-4162-88F2-870A4BF5A928}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{29B26A5C-F5D5-4162-88F2-870A4BF5A928}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{29B26A5C-F5D5-4162-88F2-870A4BF5A928}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{29B26A5C-F5D5-4162-88F2-870A4BF5A928}.Release|Any CPU.Build.0 = Release|Any CPU
		{BE713318-32AE-4D67-9334-B4605CDD965D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BE713318-32AE-4D67-9334-B4605CDD965D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BE713318-32AE-4D67-9334-B4605CDD965D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BE713318-32AE-4D67-9334-B4605CDD965D}.Release|Any CPU.Build.0 = Release|Any CPU
		{65F724DC-3B34-4800-A66F-908E2C4161B9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{65F724DC-3B34-4800-A66F-908E2C4161B9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{65F724DC-3B34-4800-A66F-908E2C4161B9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{65F724DC-3B34-4800-A66F-908E2C4161B9}.Release|Any CPU.Build.0 = Release|Any CPU
		{B65FB304-EEBC-470E-BED5-59A936FEA259}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B65FB304-EEBC-470E-BED5-59A936FEA259}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B65FB304-EEBC-470E-BED5-59A936FEA259}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B65FB304-EEBC-470E-BED5-59A936FEA259}.Release|Any CPU.Build.0 = Release|Any CPU
		{2AD0B196-7118-44C0-AAD5-4B62315A3CF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{2AD0B196-7118-44C0-AAD5-4B62315A3CF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{2AD0B196-7118-44C0-AAD5-4B62315A3CF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{2AD0B196-7118-44C0-AAD5-4B62315A3CF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{1FDA0067-772A-4328-AB98-B41036C62045}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1FDA0067-772A-4328-AB98-B41036C62045}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1FDA0067-772A-4328-AB98-B41036C62045}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1FDA0067-772A-4328-AB98-B41036C62045}.Release|Any CPU.Build.0 = Release|Any CPU
		{5793AE2A-D919-414C-879C-8F97EBDC7551}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5793AE2A-D919-414C-879C-8F97EBDC7551}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5793AE2A-D919-414C-879C-8F97EBDC7551}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5793AE2A-D919-414C-879C-8F97EBDC7551}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{65F724DC-3B34-4800-A66F-908E2C4161B9} = {EE2C147D-2278-48F1-B01A-EFB4819F0227}
		{B65FB304-EEBC-470E-BED5-59A936FEA259} = {EE2C147D-2278-48F1-B01A-EFB4819F0227}
		{DAF07A0E-D36B-454C-9B81-37B8DB153238} = {DA3B012A-7A6C-4D48-94CD-24BDF7CFD62D}
		{9F32E407-F85B-4134-BCFB-AAA1856EA6BD} = {DA3B012A-7A6C-4D48-94CD-24BDF7CFD62D}
		{2AD0B196-7118-44C0-AAD5-4B62315A3CF8} = {EE2C147D-2278-48F1-B01A-EFB4819F0227}
		{B62A5F43-858D-4B02-8B66-D50698581C7A} = {DA3B012A-7A6C-4D48-94CD-24BDF7CFD62D}
		{5793AE2A-D919-414C-879C-8F97EBDC7551} = {EE2C147D-2278-48F1-B01A-EFB4819F0227}
	EndGlobalSection
EndGlobal
