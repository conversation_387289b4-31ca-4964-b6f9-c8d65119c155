﻿using CoverGo.Payments.Infrastructure.Payments.Ing.Builders;
using CoverGo.Payments.Infrastructure.Payments.Ing.Configurations;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Payments.Ing
{
    public abstract class BaseClient(IngClientSettings settings, IHttpClientFactory httpClientFactory, ILogger logger)
    {
        protected async Task<T?> ProcessAsync<T>(Uri requestUrl, HttpMethod httpMethod, CancellationToken ct,
            object? content = default(HttpContent))
            where T : class
        {
            HttpRequestMessage request = IngClientRequestBuilder.BuildRequestMessage(requestUrl, httpMethod, settings.ApiKey, content);
            var communicator = new IngApiHttpCommunicator<T>(httpClientFactory, settings, logger);
            return await communicator.SendAsync(request, ct);
        }
    }
}