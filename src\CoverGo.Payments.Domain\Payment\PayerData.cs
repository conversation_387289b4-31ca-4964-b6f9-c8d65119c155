using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;

namespace CoverGo.Payments.Domain.Payment;

public class PayerData : ValueObject
{
    private PayerData() { }

    public PayerData(string? companyName, string? externalCustomerId, string? firstName, string? lastName,
        Address? address, string? emailAddress, string? language, string? phoneNumber)
    {
        CompanyName = companyName;
        ExternalCustomerId = externalCustomerId;
        FirstName = firstName;
        LastName = lastName;
        Address = address;
        EmailAddress = emailAddress;
        Language = language;
        PhoneNumber = phoneNumber;
    }

    public string? CompanyName { get; private set; }

    public string? ExternalCustomerId { get; private set; }

    public string? FirstName { get; private set; }

    public string? LastName { get; private set; }

    public Address? Address { get; private set; }

    public string? EmailAddress { get; private set; }

    public string? Language { get; private set; }

    public string? PhoneNumber { get; private set; }

    public string CustomerName => string.IsNullOrWhiteSpace(CompanyName) ? FirstName + " " + LastName : CompanyName;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        if (CompanyName != null) yield return CompanyName;
        if (ExternalCustomerId != null) yield return ExternalCustomerId;
        if (FirstName != null) yield return FirstName;
        if (LastName != null) yield return LastName;
        if (Address != null) yield return Address;
        if (EmailAddress != null) yield return EmailAddress;
        if (Language != null) yield return Language;
        if (PhoneNumber != null) yield return PhoneNumber;
    }
}