using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;
using CoverGo.Payments.Domain.Exceptions;
using GuardClauses;

namespace CoverGo.Payments.Domain.Payment;

public class PaymentMoney : ValueObject
{
    private PaymentMoney()
    {
        
    }
    
    public PaymentMoney(string currencyCode, string currencyDesc, decimal amount, int decimalPrecision)
    {
        PaymentCurrencyCode = currencyCode;
        PaymentCurrencyDesc = currencyDesc;
        PaymentAmount = amount;
        PaymentDecimalPrecision = decimalPrecision;
    }
    
    public string PaymentCurrencyCode { get; private set; }
    
    public string PaymentCurrencyDesc { get; private set; }
    
    public decimal PaymentAmount { get; private set; }
    
    public int PaymentDecimalPrecision { get; private set; }
    
    public MCPDetails? PaymentMcDetails { get; private set; }

    public void SetMcpDetails(MCPDetails mcpDetails)
    {
        GuardClause.ArgumentIsNotNull(mcpDetails, nameof(mcpDetails));

        if (PaymentMcDetails != null) throw new DomainException("MCPDetails is already set");

        PaymentMcDetails = mcpDetails;
    }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return PaymentCurrencyCode;
        yield return PaymentCurrencyDesc;
        yield return PaymentAmount;
        yield return PaymentDecimalPrecision;
        yield return PaymentMcDetails!;
    }
}