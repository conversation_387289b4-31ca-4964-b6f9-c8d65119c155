﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.FailPayment
{
    public class FailPaymentCommandValidator : AbstractValidator<FailPaymentCommand>
    {
        public FailPaymentCommandValidator(
            ILogger<FailPaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PaymentId).NotEmpty().WithMessage("No paymentId found.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
