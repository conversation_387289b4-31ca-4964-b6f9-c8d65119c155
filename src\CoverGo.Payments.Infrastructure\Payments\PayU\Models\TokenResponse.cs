﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class TokenResponse
{
    [JsonProperty(PayUContainer.PropsName.PayuPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public long PayuPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
    public int Code { get; set; }

    [JsonProperty(PayUContainer.PropsName.Message, NullValueHandling = NullValueHandling.Ignore)]
    public string Message { get; set; }

    [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
    public string Status { get; set; }

    [JsonProperty(PayUContainer.PropsName.Token, NullValueHandling = NullValueHandling.Ignore)]
    public string Token { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.TokenStatus, NullValueHandling = NullValueHandling.Ignore)]
    public string TokenStatus { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.CardUniqueIdentifier, NullValueHandling = NullValueHandling.Ignore)]
    public string CardUniqueIdentifier { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.ExpirationDate, NullValueHandling = NullValueHandling.Ignore)]
    public string ExpirationDate { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.CardHolderName, NullValueHandling = NullValueHandling.Ignore)]
    public string CardHolderName { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.LastFourDigits, NullValueHandling = NullValueHandling.Ignore)]
    public string LastFourDigits { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.CardExpirationDate, NullValueHandling = NullValueHandling.Ignore)]
    public string CardExpirationDate { get; set; }
}