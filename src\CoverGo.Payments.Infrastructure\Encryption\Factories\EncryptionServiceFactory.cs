﻿using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Infrastructure.Encryption.Symmetric;
using System.Security.Cryptography;

namespace CoverGo.Payments.Infrastructure.Encryption.Factories
{
    public static class EncryptionServiceFactory
    {
        public static IEncryptionService CreateEncryptionService(
            string algorithm,
            string key,
            string iv,
            int keySize,
            CipherMode cipherMode,
            PaddingMode paddingMode)
        {
            return algorithm.ToUpper() switch
            {
                "AES" => new AesEncryptionService(key, iv, keySize, cipherMode, paddingMode),
                _ => throw new InvalidOperationException($"Unsupported algorithm: {algorithm}")
            };
        }
    }
}
