ARG ALPINE="-alpine"
FROM mcr.microsoft.com/dotnet/sdk:8.0$ALPINE AS build
ARG BUILDCONFIG=Release
ARG VERSION=1.0.0
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /app

COPY ./*.sln .

COPY ./src/CoverGo.Payments.Integration.Events/*.csproj ./src/CoverGo.Payments.Integration.Events/
COPY ./nuget.config .

COPY ./src ./src

ARG BUILDCONFIG="Release"
ARG FILE_VERSION="1.0.0.0"
ARG INFORMATIONAL_VERSION="1.0"
ARG APP_VERSION="1.0.0"
ENV CI_BUILD=true
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN dotnet pack  ./src/CoverGo.Payments.Integration.Events/CoverGo.Payments.Integration.Events.csproj -c $BUILDCONFIG -o nuget -p:IncludeSymbols=true -p:SymbolPackageFormat=snupkg /p:PackageVersion="$APP_VERSION"  /p:Version="$APP_VERSION" /p:FileVersion="$FILE_VERSION" /p:InformationVersion="$INFORMATIONAL_VERSION"
