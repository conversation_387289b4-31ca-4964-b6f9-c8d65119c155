{"Serilog": {"WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"outputTemplate": "[{Level:u3}][{TraceId}][{SpanId}][{StatusCode}][{Timestamp:HH:mm:ss.fff}][T{ThreadId:D2}][{Tenant}][{ApplicationName}][{EnvironmentName}][{MachineName}][T{ThreadId:D2}][{SourceContext}] {Message:l}{NewLine}{Exception}"}}]}, "serviceUrls": {"auth": "http://host.docker.internal:60000/"}, "ConnectionStrings": {"redis": "localhost:6379,abortConnect=false", "gateway": "http://host.docker.internal:60060/", "auth": "http://host.docker.internal:60000/"}, "HealthChecksUI": {"HealthChecks": [{"Name": "Payments Service Health", "Uri": "http://localhost:51504/health-api"}], "Webhooks": [], "EvaluationTimeInSeconds": 10, "MinimumSecondsBetweenFailureNotifications": 60}, "MongoDatabaseConfiguration": {"ConnectionString": "****************************************"}, "GraphQL": {"IncludeExceptionDetails": true}, "GraphQLStitching": {"Enabled": false, "SchemaName": "payments", "Redis": {"Publish": false, "ConfigurationName": "GatewayV2"}}, "CheckDI": true}