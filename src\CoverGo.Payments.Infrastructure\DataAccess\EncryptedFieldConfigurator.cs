﻿using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Domain.Encryption;
using MongoDB.Bson.Serialization;
using System.Collections;
using System.Reflection;

namespace CoverGo.Payments.Infrastructure.DataAccess
{
    public static class EncryptedFieldConfigurator
    {
        public static void RegisterClassMap<TEntity>(IEncryptionService encryptionService, Action<BsonClassMap>? configMap = null) where TEntity : class
        {
            BsonClassMap.TryRegisterClassMap<TEntity>(cm =>
            {
                if (configMap == null)
                {
                    cm.AutoMap();
                }
                else
                {
                    configMap(cm);
                }

                foreach (var memberMap in cm.DeclaredMemberMaps)
                {
                    var propertyInfo = memberMap.MemberInfo as PropertyInfo;

                    if (IsEncryptedType(propertyInfo))
                    {
                        SetMemberMap(memberMap, propertyInfo!.PropertyType, false, encryptionService);
                    }
                    else if (IsClassTypeHasInnerEncryptedProperties(propertyInfo) || IsCollectionTypeHasInnerEncryptedProperties(propertyInfo))
                    {
                        SetMemberMap(memberMap, propertyInfo!.PropertyType, true, encryptionService);
                    }
                }
            });
        }

        private static void SetMemberMap(BsonMemberMap memberMap, Type propertyType, bool isNested, IEncryptionService encryptionService)
        {
            IBsonSerializer? serializer;

            if (isNested)
            {
                serializer = CreateFieldSerializer(propertyType, encryptionService, typeof(EncryptedNestedFieldSerializer<>));
            }
            else
            {
                serializer = CreateFieldSerializer(propertyType, encryptionService, typeof(EncryptedFieldSerializer<>));
            }

            if (serializer != null)
            {
                memberMap.SetSerializer(serializer);
            }
        }

        private static IBsonSerializer? CreateFieldSerializer(Type propertyType, IEncryptionService encryptionService, Type serializertype)
        {
            var serializerType = serializertype.MakeGenericType(propertyType);
            return Activator.CreateInstance(serializerType, encryptionService) as IBsonSerializer;
        }

        private static bool IsClassTypeHasInnerEncryptedProperties(PropertyInfo? propertyInfo)
        {
            if (propertyInfo == null)
            {
                return false;
            }
            var type = propertyInfo.PropertyType;
            if (!type.IsClass || type == typeof(string) || type.IsArray)
            {
                return false;
            }

            return HasEncryptedProperty(type);
        }

        private static bool IsCollectionTypeHasInnerEncryptedProperties(PropertyInfo? propertyInfo)
        {
            if (propertyInfo == null)
            {
                return false;
            }
            var type = propertyInfo.PropertyType;
            if (!typeof(IEnumerable).IsAssignableFrom(type) || !type.IsGenericType)
            {
                return false;
            }

            var elementType = type.GetGenericArguments().FirstOrDefault();
            return elementType != null && HasEncryptedProperty(elementType);
        }

        private static bool IsEncryptedType(PropertyInfo? propertyInfo)
        {
            if (propertyInfo == null)
            {
                return false;
            }
            return propertyInfo.GetCustomAttribute<EncryptedAttribute>() != null;
        }

        private static bool HasEncryptedProperty(Type type)
        {
            foreach (var property in type.GetProperties())
            {
                if (property.GetCustomAttribute<EncryptedAttribute>() != null)
                {
                    return true;
                }

                if (property.PropertyType.IsClass && property.PropertyType != typeof(string) && !property.PropertyType.IsArray)
                {
                    if (HasEncryptedProperty(property.PropertyType))
                    {
                        return true;
                    }
                }
            }
            return false;
        }
    }
}
