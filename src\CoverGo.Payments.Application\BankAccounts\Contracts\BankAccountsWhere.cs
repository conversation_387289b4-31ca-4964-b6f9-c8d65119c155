using CoverGo.Payments.Application.Common;

namespace CoverGo.Payments.Application.BankAccounts.Contracts;

public class BankAccountsWhere
{
    public IReadOnlyCollection<BankAccountsWhere>? And { get; set; }
    public IReadOnlyCollection<BankAccountsWhere>? Or { get; set; }
    public StringWhere? Id { get; set; }
    public StringWhere? AccountNumber { get; set; }
    public BankAccountUsageWhere? UsedFor { get; set; }
    public DateTimeWhere? CreatedAt { get; set; }
    public StringWhere? PayorId { get; set; }
    public PayorTypeWhere? PayorType { get; set; }
} 