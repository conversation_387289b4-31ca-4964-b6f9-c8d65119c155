﻿using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Infrastructure.Payments.Ing;
using CoverGo.Payments.Infrastructure.Payments.ING;
using CoverGo.Payments.Infrastructure.Payments.Ing.Configurations;
using CoverGo.Payments.Infrastructure.Payments.ING.Models;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using Microsoft.AspNetCore.Http;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments.Ing.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.PspSettings;

namespace CoverGo.Payments.UnitTests;

public class IngPaymentProviderServiceTests
{
    private const string PspSettingsJson = "{\"ApiUrl\":\"https://test\",\"ApiKey\":\"test\",\"SuccessUrl\":\"https://test/success\",\"CancelUrl\":\"https://test/cancel\",\"FailUrl\":\"https://test/failure\",\"MerchantId\":\"test\",\"ServiceId\":\"test\",\"ServiceKey\":\"test\",\"RedirectUrl\":\"test/redirect\"}";
    
    private readonly Mock<ILogger<IngPaymentProviderService>> _loggerMock;
    private readonly Mock<IIngClientFactory> _ingClientFactoryMock;
    private readonly Mock<IIngClient> _ingClientMock;
    private readonly IngPaymentProviderService _sut;
    private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;

    public IngPaymentProviderServiceTests()
    {
        _loggerMock = new Mock<ILogger<IngPaymentProviderService>>();
        _ingClientFactoryMock = new Mock<IIngClientFactory>();
        _ingClientMock = new Mock<IIngClient>();
        _httpContextAccessorMock = new Mock<IHttpContextAccessor>();

        _ingClientFactoryMock.Setup(factory => factory.Create(It.IsAny<IngClientSettings>()))
            .Returns(_ingClientMock.Object);

        _sut = new IngPaymentProviderService(_loggerMock.Object, _ingClientFactoryMock.Object, _httpContextAccessorMock.Object);
    }

    [Fact]
    public async Task GIVEN_valid_payment_WHEN_GetPreProcessRedirectUrlAsync_THEN_should_return_signature()
    {
        PreauthPaymentAggregate payment = CreatePaymentAggregate();
        JsonElement dynamicFields = PrepareDynamicFields();

        RedirectUrlOutput result = await _sut.GetPreProcessRedirectUrlAsync(payment, dynamicFields, CancellationToken.None);

        result.Should().NotBeNull();
        result.Data.Should().ContainKey("signature");
        result.Data["signature"].Should().Contain("sha256");
    }
    
    [Fact]
    public async Task GIVEN_valid_payment_WHEN_GetPreProcessRedirectUrlAsync_THEN_should_assign_dynamicFields()
    {
        PreauthPaymentAggregate payment = CreatePaymentAggregate();
        JsonElement dynamicFields = PrepareDynamicFields();

        await _sut.GetPreProcessRedirectUrlAsync(payment, dynamicFields, CancellationToken.None);

        payment.DynamicFields.Should().NotBeNull();
        payment.DynamicFields.Should().Be(dynamicFields.ToString());
    }
    
    [Fact]
    public async Task GIVEN_valid_payment_WHEN_GetPreProcessRedirectUrlAsync_THEN_should_assign_payerData()
    {
        PreauthPaymentAggregate payment = CreatePaymentAggregate();
        JsonElement dynamicFields = PrepareDynamicFields();

        await _sut.GetPreProcessRedirectUrlAsync(payment, dynamicFields, CancellationToken.None);

        payment.PayerData.Should().NotBeNull();
        payment.PayerData!.FirstName.Should().Be("John");
        payment.PayerData!.LastName.Should().Be("Doe");
        payment.PayerData!.EmailAddress.Should().Be("<EMAIL>");
        payment.PayerData!.PhoneNumber.Should().Be("501501501");
    }
   
    [Fact]
    public async Task GIVEN_null_payment_WHEN_GetPreProcessRedirectUrlAsync_THEN_should_throw_ArgumentNullException()
    {
        PreauthPaymentAggregate? payment = null;
        JsonElement dynamicFields = PrepareDynamicFields();

        Func<Task> act = async () => await _sut.GetPreProcessRedirectUrlAsync(payment, dynamicFields, CancellationToken.None);

        await act.Should().ThrowAsync<ArgumentNullException>().WithMessage("*payment*");
    }

    [Fact]
    public async Task GIVEN_invalid_pspSettings_WHEN_GetPreProcessRedirectUrlAsync_THEN_should_throw_ArgumentNullException()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EURO", 1000, 2), null, null, null, null);
        const string invalidPspSettingsJson = "{\"ApiUrl\":\"https://test\",\"SuccessUrl\":\"https://test/success\",\"CancelUrl\":\"https://test/cancel\",\"FailUrl\":\"https://test/failure\",\"RedirectUrl\":\"test/redirect\"}";
        payment.SetPspSettings(invalidPspSettingsJson);
        JsonElement dynamicFields = PrepareDynamicFields();

        // Act
        Func<Task> act = async () => await _sut.GetPreProcessRedirectUrlAsync(payment, dynamicFields, CancellationToken.None);

        // Assert
        await act.Should().ThrowAsync<ArgumentNullException>();
    }
    
    [Fact]
    public async Task GIVEN_valid_payment_and_pspSettings_WHEN_FinalizePaymentAsync_THEN_should_succeed()
    {
        PreauthPaymentAggregate payment = CreatePaymentAggregate();
        payment.SetDynamicFields(PrepareDynamicFields());

        const string customerId = "CUST001";
        var paymentProfileResponse = new PaymentProfileResponse
        {
            PaymentProfiles = new[] { new PaymentProfile { Id = "profileId", FirstName = "John", LastName = "Doe" } }
        };

        _ingClientMock.Setup(client => client.GetPaymentProfileAsync(customerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentProfileResponse);

        Func<Task> act = async () => await _sut.FinalizePaymentAsync(payment, null, null, CancellationToken.None);

        await act.Should().NotThrowAsync();

        payment.PaymentStatusHistoryItems.Should().ContainSingle(item => item.Status == PaymentStatus.Succeeded);
    }
    
    [Fact]
    public async Task GIVEN_null_payment_WHEN_FinalizePaymentAsync_THEN_should_throw_ArgumentNullException()
    {
        PreauthPaymentAggregate payment = null;
        JsonElement dynamicFields = PrepareDynamicFields();

        Func<Task> act = async () => await _sut.FinalizePaymentAsync(payment, null, dynamicFields, CancellationToken.None);

        await act.Should().ThrowAsync<ArgumentNullException>().WithMessage("*payment*");
    }
    
    [Fact]
    public async Task GIVEN_invalid_pspSettings_WHEN_FinalizePaymentAsync_THEN_should_throw_ArgumentNullException()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EURO", 1000, 2), null, null, null, null);
        const string invalidPspSettingsJson = "{\"ApiUrl\":\"https://test\",\"SuccessUrl\":\"https://test/success\",\"CancelUrl\":\"https://test/cancel\",\"FailUrl\":\"https://test/failure\",\"RedirectUrl\":\"test/redirect\"}";
        payment.SetPspSettings(invalidPspSettingsJson);
        JsonElement dynamicFields = PrepareDynamicFields();

        Func<Task> act = async () => await _sut.FinalizePaymentAsync(payment, null, dynamicFields, CancellationToken.None);

        await act.Should().ThrowAsync<ArgumentNullException>();
    }
    
    [Fact]
    public async Task GIVEN_failed_profile_lookup_WHEN_FinalizePaymentAsync_THEN_should_throw_exception()
    {
        PreauthPaymentAggregate payment = CreatePaymentAggregate();
        payment.SetDynamicFields(PrepareDynamicFields());
        const string customerId = "CUST001";

        _ingClientMock.Setup(client => client.GetPaymentProfileAsync(customerId, It.IsAny<CancellationToken>()))
            .ReturnsAsync((PaymentProfileResponse)null);

        Func<Task> act = async () => await _sut.FinalizePaymentAsync(payment, null, null, CancellationToken.None);

        await act.Should().ThrowAsync<Exception>().WithMessage("Failed to retrieve payment profile.");
    }
    
    [Fact]
    public async Task GIVEN_valid_recurringPayment_WHEN_RecurringPaymentAsync_THEN_should_succeed()
    {
        RecurringPaymentAggregate recurringPayment = CreateRecurringPaymentAggregate();
        recurringPayment.SetPspSettings(PspSettingsJson);

        var pspBearer = new PSPBearerPseudoCC { Token = "profileToken" };
        recurringPayment.SetInitialBearer(pspBearer);
        
        recurringPayment.SetInternalReference();
        
        var debitResponse = new DebitPaymentProfileResponse
        {
            Transaction = new Transaction { Id = "transaction123" }
        };

        _ingClientMock.Setup(client => client.DebitPaymentProfileAsync(It.IsAny<DebitPaymentProfileRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(debitResponse);

        RecurringPaymentAggregate result = await _sut.RecurringPaymentAsync(recurringPayment, CancellationToken.None);

        result.Should().NotBeNull();
        result.Id.Should().Be(recurringPayment.Id);
        result.PaymentStatusHistoryItems.Should().ContainSingle(item => item.Status == PaymentStatus.Succeeded);

        _ingClientMock.Verify(client => client.DebitPaymentProfileAsync(It.Is<DebitPaymentProfileRequest>(req =>
            req.PaymentProfileId == pspBearer.Token &&
            req.ServiceId == "test" &&
            req.Amount == CurrencyHelper.ConvertUnitsToSubunits(recurringPayment.Money.PaymentAmount) &&
            req.Currency == CurrencyHelper.GetCurrency(recurringPayment.Money.PaymentCurrencyCode)), CancellationToken.None), Times.Once);
    }
    
    [Fact]
    public async Task GIVEN_null_token_in_pspBearer_WHEN_RecurringPaymentAsync_THEN_should_throw_exception()
    {
        RecurringPaymentAggregate recurringPayment = CreateRecurringPaymentAggregate();
        recurringPayment.SetPspSettings(PspSettingsJson);

        var pspBearer = new PSPBearerPseudoCC { Token = null };
        recurringPayment.SetInitialBearer(pspBearer);
        
        recurringPayment.SetInternalReference();

        Func<Task> act = async () => await _sut.RecurringPaymentAsync(recurringPayment, CancellationToken.None);

        await act.Should().ThrowAsync<Exception>().WithMessage("No PaymentProfileID found for recurring payment.");
    }
    
    [Fact]
    public async Task GIVEN_failed_debit_request_WHEN_RecurringPaymentAsync_THEN_should_log_error_and_throw_exception()
    {
        RecurringPaymentAggregate recurringPayment = CreateRecurringPaymentAggregate();
        recurringPayment.SetPspSettings(PspSettingsJson);

        var pspBearer = new PSPBearerPseudoCC { Token = "profileToken" };
        recurringPayment.SetInitialBearer(pspBearer);
        
        recurringPayment.SetInternalReference();

        _ingClientMock.Setup(client => client.DebitPaymentProfileAsync(It.IsAny<DebitPaymentProfileRequest>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((DebitPaymentProfileResponse)null); 

        Func<Task> act = async () => await _sut.RecurringPaymentAsync(recurringPayment, CancellationToken.None);

        await act.Should().ThrowAsync<Exception>().WithMessage("Recurring payment request failed:*");
    }

    [Fact]
    public async Task GIVEN_valid_payment_and_refund_WHEN_RefundAsync_THEN_should_process_refund_successfully()
    {
        // Arrange
        var payment = CreatePaymentAggregate();
        payment.AssignProviderTransaction(Guid.NewGuid().ToString());
        var refund = new RefundAggregate(payment.Id, RefundStatus.Created, payment.Money, null, payment.ProviderPaymentId);
        var refundResponse = new RefundResponse 
        { 
            Transaction = new Transaction 
            { 
                Id = Guid.NewGuid().ToString(),
                Status = "settled"
            }            
        };

        _ingClientMock.Setup(client => client.PostRefundAsync(It.IsAny<RefundRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(refundResponse);

        // Act
        var result = await _sut.RefundAsync(payment, refund, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(RefundStatus.Succeeded);
        _ingClientMock.Verify(client => client.PostRefundAsync(It.IsAny<RefundRequest>(), payment.ProviderPaymentId!, CancellationToken.None), Times.Once);
    }

    [Fact]
    public async Task GIVEN_valid_payment_and_fail_refund_WHEN_RefundAsync_THEN_should_handle_refund_failure()
    {
        // Arrange
        var payment = CreatePaymentAggregate();
        payment.AssignProviderTransaction(Guid.NewGuid().ToString());
        var refund = new RefundAggregate(payment.Id, RefundStatus.Created, payment.Money, null, payment.ProviderPaymentId);
        var refundResponse = new RefundResponse
        {
            Transaction = new Transaction
            {
                Id = Guid.NewGuid().ToString(),
                Status = "rejected"
            }
        };

        _ingClientMock.Setup(client => client.PostRefundAsync(It.IsAny<RefundRequest>(), It.IsAny<string>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(refundResponse);

        // Act & Assert
        await Assert.ThrowsAsync<DomainException>(() => _sut.RefundAsync(payment, refund, CancellationToken.None));
        refund.Status.Should().Be(RefundStatus.Failed);
    }

    [Fact(Skip = "Failing test will fix later")]
    public async Task Given_ValidWebhookBody_When_HandleWebhookAsync_Then_ReturnsExpectedResult()
    {
        // Arrange
        var webhookBody = "{\"Transaction\": {\"OrderId\": \"638655093964578460-f0929a71-9652-4db3-b943-c7315d5ba238\", \"Id\": \"txn_123\", \"Status\": \"settled\", \"Amount\": 10000}}";
        var pspSettings = new IngPspSettingsAggregate();
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-Imoje-Signature"] = "valid_signature";
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);

        // Act
        var result = await _sut.HandleWebhookAsync(webhookBody, pspSettings, CancellationToken.None);

        // Assert
        result.paymentId.Should().Be("f0929a71-9652-4db3-b943-c7315d5ba238");
        result.providerPaymentId.Should().Be("txn_123");
        result.paymentStatus.Should().Be(PaymentStatus.Finalizing);
        result.amount.Should().Be(100m);
        result.isFinalizationRequired.Should().BeFalse();
        result.skip.Should().BeFalse();
    }

    [Fact]
    public async Task Given_InvalidWebhookBody_When_HandleWebhookAsync_Then_ReturnsFailedStatus()
    {
        // Given
        var webhookBody = "{}";
        var pspSettings = new IngPspSettingsAggregate();
        var httpContext = new DefaultHttpContext();
        httpContext.Request.Headers["X-Imoje-Signature"] = "valid_signature";
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);

        // When
        var result = await _sut.HandleWebhookAsync(webhookBody, pspSettings, CancellationToken.None);

        // Then
        result.paymentId.Should().BeEmpty();
        result.providerPaymentId.Should().BeEmpty();
        result.paymentStatus.Should().Be(PaymentStatus.Failed);
        result.amount.Should().BeNull();
        result.isFinalizationRequired.Should().BeFalse();
        result.skip.Should().BeFalse();
    }

    [Fact]
    public async Task Given_MissingSignatureHeader_When_HandleWebhookAsync_Then_ReturnsFailedStatus()
    {
        // Given
        var webhookBody = "{\"Transaction\": {\"OrderId\": \"12345-67890\", \"Id\": \"txn_123\", \"Status\": \"settled\", \"Amount\": 10000}}";
        var pspSettings = new IngPspSettingsAggregate();
        var httpContext = new DefaultHttpContext();
        _httpContextAccessorMock.Setup(x => x.HttpContext).Returns(httpContext);

        // When
        var result = await _sut.HandleWebhookAsync(webhookBody, pspSettings, CancellationToken.None);

        // Then
        result.paymentId.Should().BeEmpty();
        result.providerPaymentId.Should().BeEmpty();
        result.paymentStatus.Should().Be(PaymentStatus.Failed);
        result.amount.Should().BeNull();
        result.isFinalizationRequired.Should().BeFalse();
        result.skip.Should().BeFalse();
    }

    private static RecurringPaymentAggregate CreateRecurringPaymentAggregate()
    {
        var recurringPayment =
            new RecurringPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EURO", 1000, 2), null, null, null, null);
        return recurringPayment;
    }
    
    private static PreauthPaymentAggregate CreatePaymentAggregate()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EURO", 1000, 2), null, null, null, null);
        
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();
        
        return payment;
    }
    
    private static JsonElement PrepareDynamicFields() =>
        JsonDocument.Parse(JsonSerializer.Serialize(
            "{\"customerFirstName\":\"John\",\"customerLastName\":\"Doe\",\"customerId\":\"CUST001\",\"customerEmail\":\"<EMAIL>\",\"customerPhone\":\"501501501\"}")).RootElement;
}