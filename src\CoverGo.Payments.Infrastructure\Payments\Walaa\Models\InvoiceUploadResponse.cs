﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Walaa.Models;

public class InvoiceUploadResponse
{
    [JsonProperty("Result")]
    public string? Result { get; set; }

    [JsonProperty("InvoiceDetails")]
    public InvoiceUploadResponseInvoiceDetail[]? InvoiceDetails { get; set; }

    [JsonProperty("Errors")]
    public Error[]? Errors { get; set; }

    public bool IsSuccess => Result == "Success";
}

public class InvoiceUploadResponseInvoiceDetail
{
    [JsonProperty("LinkExpiryDate")]
    public string? LinkExpiryDate { get; set; }

    [JsonProperty("PaymentLink")]
    public string? PaymentLink { get; set; }
}

public class Error
{
    [JsonProperty("Message")]
    public string? Message { get; set; }

    [JsonProperty("Code")]
    public string? Code { get; set; }
}
