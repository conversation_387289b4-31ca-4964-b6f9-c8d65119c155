﻿using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.PspSettings.Commands.EncryptPspSettings;
using MediatR;

namespace CoverGo.Payments.Api.PspSettings.EncryptPspSettings
{
    [MutationType]
    public class EncryptPspSettingsMutation
    {
        [Error(typeof(DomainError))]
        [UseMutationConvention(PayloadFieldName = "result")]
        //[Authorize]
        public async Task<string> EncryptPspSettings(
            EncryptPspSettingsCommand input,
            [Service] IMediator commandProcessor,
            CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
    }
}
