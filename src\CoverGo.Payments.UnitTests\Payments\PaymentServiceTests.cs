﻿using System.Linq.Expressions;
using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.Multitenancy;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Commands.RegisterPayment;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Providers;
using CoverGo.Payments.Application.PspSettings.Providers;

using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Payment.DomainEvents;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments;
using CoverGo.Proxies.Auth;
using FluentAssertions;
using MediatR;
using Microsoft.Extensions.Logging;
using MongoDB.Driver;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments;

public class PaymentServiceTests
{
    private const string PspSettingsJson =
        "{ \"ApiUrl\": \"https://apiuat.walaa.com:5000\", \"WebhookUsername\": \"username\", \"WebhookPassword\": \"password\" }";

    protected readonly Mock<IRepository<PaymentAggregate, string>> _paymentRepositoryMock;
    protected readonly Mock<IRepository<RefundAggregate, string>> _refundRepositoryMock;
    protected readonly Mock<IRepository<TokenizedPaymentInitializationAggregate, string>> _tokenizedPaymentInitializationRepositoryMock;
    protected readonly Mock<IMediator> _mediatorMock;
    protected readonly PaymentService _paymentService;

    public PaymentServiceTests()
    {
        _paymentRepositoryMock = new Mock<IRepository<PaymentAggregate, string>>();
        _refundRepositoryMock = new Mock<IRepository<RefundAggregate, string>>();

        Mock<ITenantProvider> tenantProviderMock = new();
        Mock<IAuthService> authServiceMock = new();
        _tokenizedPaymentInitializationRepositoryMock =
            new();
        Mock<ILogger<PaymentService>> loggerMock = new();
        Mock<IMongoCollection<PaymentAggregate>> mongoCollectionMock = new();

        var pspSettingsProviderMock = new Mock<IPspSettingsProvider>();
        pspSettingsProviderMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        pspSettingsProviderMock
            .Setup(p => p.GetPspSettingsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new StripePspSettingsAggregate { UseAutoCapture = false });

        var paymentProviderServiceMock = new Mock<IPaymentProviderService>();
        paymentProviderServiceMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);

        paymentProviderServiceMock
            .Setup(p => p.PreparePaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate p, CancellationToken _) => p);
        paymentProviderServiceMock
            .Setup(p => p.RecurringPaymentAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((RecurringPaymentAggregate p, CancellationToken _) => p);

        _mediatorMock = new Mock<IMediator>();
        // setup send method
        _mediatorMock
            .Setup(m => m.Send(It.IsAny<CancelPaymentCommand>(), It.IsAny<CancellationToken>()))
            .Returns(Task.FromResult(new Application.Payments.Contracts.PaymentDto()
            {
                Id = Guid.NewGuid().ToString()
            }));

        _paymentService = new PaymentService(
            loggerMock.Object,
            _tokenizedPaymentInitializationRepositoryMock.Object,
            _paymentRepositoryMock.Object,
            _refundRepositoryMock.Object,
            new[] { paymentProviderServiceMock.Object },
            new[] { pspSettingsProviderMock.Object },
            mongoCollectionMock.Object,
            authServiceMock.Object,
            tenantProviderMock.Object,
            _mediatorMock.Object
        );
    }

    [Fact]
    public async Task
        GIVEN_valid_Initial_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_THEN_should_create_new_preauth_payment()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "DIGITAL_PAYMENT",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedInitial
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);
        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();
        _paymentRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task
        GIVEN_valid_Endorsement_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_THEN_should_create_new_preauth_payment()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "DIGITAL_PAYMENT",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedEndorsement
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);
        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();
        _paymentRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task
        GIVEN_valid_WHEN_RegisterPaymentAsync_with_recurring_payment_THEN_should_create_recurring_payment()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "DIGITAL_PAYMENT",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedRecurring
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RecurringPaymentAggregate(PaymentProvider.ExternalFile,
                new PaymentMoney("978", "EUR", 500, 2),
                "policy1", "invoice1", "payor1", null));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<RecurringPaymentAggregate>();
        _paymentRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_invalid_transaction_type_WHEN_RegisterPaymentAsync_THEN_should_throw_DomainException()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            "InvalidTransactionType"
        );

        // Act & Assert
        await FluentActions.Invoking(() => _paymentService.RegisterPaymentAsync(command, CancellationToken.None))
            .Should().ThrowAsync<DomainException>()
            .WithMessage(
                "Invalid transaction type. Should be 'Premium Collected Initial' or 'Premium Collected Recurring' or 'Premium Collected Endorsement' or 'Refund'.");
    }

    [Fact]
    public async Task GIVEN_existing_payment_WHEN_RegisterPaymentAsync_with_refund_THEN_should_create_refund()
    {
        // Arrange
        var existingPayment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);
        existingPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, new PaymentMoney("978", "EUR", 1000, 2));

        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypeRefund
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingPayment });
        _refundRepositoryMock
            .Setup(repo => repo.InsertAsync(It.IsAny<RefundAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RefundAggregate(existingPayment.Id, RefundStatus.Succeeded,
                new PaymentMoney("978", "EUR", 500, 2), null, existingPayment.ProviderPaymentId));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(PaymentStatus.PartiallyRefunded);
        _refundRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<RefundAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_invalid_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_THEN_should_throw_DomainException()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            -1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedInitial
        );

        // Act & Assert
        await FluentActions.Invoking(() => _paymentService.RegisterPaymentAsync(command, CancellationToken.None))
            .Should().ThrowAsync<ArgumentException>().WithMessage("Amount is 0 or negative number");
    }

    [Fact]
    public async Task
        GIVEN_external_payment_provider_WHEN_RegisterPaymentAsync_THEN_should_not_finalize_preauth_payment()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedInitial
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());
        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();
        _paymentRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()), Times.Once);
        _paymentRepositoryMock.Verify(
            repo => repo.InsertAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()),
            Times.Never);
    }

    [Fact]
    public async Task
        GIVEN_valid_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_THEN_should_add_payment_succeeded_domain_event_with_correct_content()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedInitial
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentSucceededDomainEvent);

        PaymentSucceededDomainEvent paymentSucceededEvent = domainEvents.OfType<PaymentSucceededDomainEvent>().Single();
        paymentSucceededEvent.PolicyId.Should().Be("policy1");
        paymentSucceededEvent.InvoiceNumber.Should().Be("invoice1");
        paymentSucceededEvent.PayorId.Should().Be("payor1");
        paymentSucceededEvent.Money.Amount.Should().Be(1000m);
        paymentSucceededEvent.Money.CurrencyCode.Should().Be("978");
        paymentSucceededEvent.Money.CurrencyDesc.Should().Be("EUR");
        paymentSucceededEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        paymentSucceededEvent.Type.Should().Be(PaymentType.Initial.ToString());
        paymentSucceededEvent.Status.Should().Be(PaymentStatus.Succeeded.ToString());
    }

    [Fact]
    public async Task
        GIVEN_valid_Receipt_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_THEN_should_add_payment_succeeded_domain_event_with_correct_content()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedEndorsement
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.Ing, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentSucceededDomainEvent);

        PaymentSucceededDomainEvent paymentSucceededEvent = domainEvents.OfType<PaymentSucceededDomainEvent>().Single();
        paymentSucceededEvent.PolicyId.Should().Be("policy1");
        paymentSucceededEvent.InvoiceNumber.Should().Be("invoice1");
        paymentSucceededEvent.PayorId.Should().Be("payor1");
        paymentSucceededEvent.Money.Amount.Should().Be(1000m);
        paymentSucceededEvent.Money.CurrencyCode.Should().Be("978");
        paymentSucceededEvent.Money.CurrencyDesc.Should().Be("EUR");
        paymentSucceededEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        paymentSucceededEvent.Type.Should().Be(PaymentType.Receipt.ToString());
        paymentSucceededEvent.Status.Should().Be(PaymentStatus.Succeeded.ToString());
    }

    [Fact]
    public async Task
        GIVEN_recurring_payment_WHEN_RegisterPaymentAsync_with_recurring_payment_THEN_should_add_recurring_payment_domain_event_with_correct_content()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedRecurring
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RecurringPaymentAggregate(PaymentProvider.ExternalFile,
                new PaymentMoney("978", "EUR", 500, 2),
                "policy1", "invoice1", "payor1", null));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<RecurringPaymentAggregate>();

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentSucceededDomainEvent);

        PaymentSucceededDomainEvent paymentSucceededEvent = domainEvents.OfType<PaymentSucceededDomainEvent>().Single();
        paymentSucceededEvent.PolicyId.Should().Be("policy1");
        paymentSucceededEvent.InvoiceNumber.Should().Be("invoice1");
        paymentSucceededEvent.PayorId.Should().Be("payor1");
        paymentSucceededEvent.Money.Amount.Should().Be(500m);
        paymentSucceededEvent.Money.CurrencyCode.Should().Be("978");
        paymentSucceededEvent.Money.CurrencyDesc.Should().Be("EUR");
        paymentSucceededEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        paymentSucceededEvent.Type.Should().Be(PaymentType.Recurring.ToString());
        paymentSucceededEvent.Status.Should().Be(PaymentStatus.Succeeded.ToString());
    }

    [Fact]
    public async Task
        GIVEN_existing_payment_WHEN_RegisterPaymentAsync_with_refund_THEN_should_add_refund_domain_event_with_correct_content()
    {
        // Arrange
        var existingPayment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);
        existingPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, new PaymentMoney("978", "EUR", 1000, 2));

        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionSucceededStatus,
            "BANK_TRANSFER",
            string.Empty,
            PrepareDynamicFields(),
            PaymentService.TransactionTypeRefund
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingPayment });

        _refundRepositoryMock
            .Setup(repo => repo.InsertAsync(It.IsAny<RefundAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RefundAggregate(existingPayment.Id, RefundStatus.Succeeded,
                new PaymentMoney("978", "EUR", 500, 2), null, existingPayment.ProviderPaymentId));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(PaymentStatus.PartiallyRefunded);

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentSucceededDomainEvent);

        PaymentSucceededDomainEvent refundSucceededEvent = domainEvents.OfType<PaymentSucceededDomainEvent>().Single();
        refundSucceededEvent.PolicyId.Should().Be("policy1");
        refundSucceededEvent.InvoiceNumber.Should().Be("invoice1");
        refundSucceededEvent.PayorId.Should().Be("payor1");
        refundSucceededEvent.Money.Amount.Should().Be(500m);
        refundSucceededEvent.Money.CurrencyCode.Should().Be("978");
        refundSucceededEvent.Money.CurrencyDesc.Should().Be("EUR");
        refundSucceededEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        refundSucceededEvent.Type.Should().Be(PaymentType.Refund.ToString());
        refundSucceededEvent.Status.Should().Be(PaymentStatus.Succeeded.ToString());
    }

    [Fact]
    public async Task
        GIVEN_valid_RegisterPaymentCommand_WHEN_RegisterPaymentAsync_fails_THEN_should_add_payment_failed_domain_event_with_correct_content()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            1000m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionFailedStatus,
            "BANK_TRANSFER",
            "NOT SUFFICIENT FUNDS",
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedInitial
        );

        var payment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile, new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(payment);

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<PreauthPaymentAggregate>();

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentFailedDomainEvent);

        PaymentFailedDomainEvent paymentFailedEvent = domainEvents.OfType<PaymentFailedDomainEvent>().Single();
        paymentFailedEvent.PolicyId.Should().Be("policy1");
        paymentFailedEvent.InvoiceNumber.Should().Be("invoice1");
        paymentFailedEvent.PayorId.Should().Be("payor1");
        paymentFailedEvent.Money.Amount.Should().Be(1000m);
        paymentFailedEvent.Money.CurrencyCode.Should().Be("978");
        paymentFailedEvent.Money.CurrencyDesc.Should().Be("EUR");
        paymentFailedEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        paymentFailedEvent.FailureMessage.Should().Be("NOT SUFFICIENT FUNDS");
        paymentFailedEvent.Status.Should().Be(PaymentStatus.Failed.ToString());
    }

    [Fact]
    public async Task
        GIVEN_existing_preauth_payment_WHEN_RegisterPaymentAsync_with_recurring_payment_fails_THEN_should_add_recurring_payment_failed_domain_event_with_correct_content()
    {
        // Arrange
        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionFailedStatus,
            "BANK_TRANSFER",
            "ACCOUNT CLOSED",
            PrepareDynamicFields(),
            PaymentService.TransactionTypePremiumCollectedRecurring
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.InsertAsync(It.IsAny<RecurringPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RecurringPaymentAggregate(PaymentProvider.ExternalFile,
                new PaymentMoney("978", "EUR", 500, 2),
                "policy1", "invoice1", "payor1", null));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Should().BeOfType<RecurringPaymentAggregate>();

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentFailedDomainEvent);

        PaymentFailedDomainEvent paymentFailedEvent = domainEvents.OfType<PaymentFailedDomainEvent>().Single();
        paymentFailedEvent.PolicyId.Should().Be("policy1");
        paymentFailedEvent.InvoiceNumber.Should().Be("invoice1");
        paymentFailedEvent.PayorId.Should().Be("payor1");
        paymentFailedEvent.Money.Amount.Should().Be(500m);
        paymentFailedEvent.Money.CurrencyCode.Should().Be("978");
        paymentFailedEvent.Money.CurrencyDesc.Should().Be("EUR");
        paymentFailedEvent.FailureMessage.Should().Be("ACCOUNT CLOSED");
        paymentFailedEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        paymentFailedEvent.Status.Should().Be(PaymentStatus.Failed.ToString());
    }

    [Fact]
    public async Task
        GIVEN_existing_payment_WHEN_RegisterPaymentAsync_with_refund_fails_THEN_should_add_refund_failed_domain_event_with_correct_content()
    {
        // Arrange
        var existingPayment = new PreauthPaymentAggregate(PaymentProvider.ExternalFile,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1", "invoice1", "payor1", null);
        existingPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, new PaymentMoney("978", "EUR", 1000, 2));

        var command = new RegisterPaymentCommand(
            PaymentProvider.ExternalFile,
            500m,
            "EUR",
            DateTime.Now,
            "policy1",
            "invoice1",
            "payor1",
            PaymentService.TransactionFailedStatus,
            "BANK_TRANSFER",
            "FUNDS NOT CLEARED",
            PrepareDynamicFields(),
            PaymentService.TransactionTypeRefund
        );

        _paymentRepositoryMock.Setup(repo =>
                repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                    It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { existingPayment });

        _refundRepositoryMock
            .Setup(repo => repo.InsertAsync(It.IsAny<RefundAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new RefundAggregate(existingPayment.Id, RefundStatus.Failed,
                new PaymentMoney("978", "EUR", 500, 2), "FUNDS NOT CLEARED", existingPayment.ProviderPaymentId));

        // Act
        PaymentAggregate result = await _paymentService.RegisterPaymentAsync(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(PaymentStatus.Succeeded);

        IReadOnlyCollection<IDomainEvent> domainEvents = result.DomainEvents;
        domainEvents.Should().NotBeNullOrEmpty();
        domainEvents.Should().ContainSingle(e => e is PaymentFailedDomainEvent);

        PaymentFailedDomainEvent refundFailedEvent = domainEvents.OfType<PaymentFailedDomainEvent>().Single();
        refundFailedEvent.PolicyId.Should().Be("policy1");
        refundFailedEvent.InvoiceNumber.Should().Be("invoice1");
        refundFailedEvent.PayorId.Should().Be("payor1");
        refundFailedEvent.Money.Amount.Should().Be(500m);
        refundFailedEvent.Money.CurrencyCode.Should().Be("978");
        refundFailedEvent.Money.CurrencyDesc.Should().Be("EUR");
        refundFailedEvent.FailureMessage.Should().Be("FUNDS NOT CLEARED");
        refundFailedEvent.PaymentMethod.Should().Be("BANK_TRANSFER");
        refundFailedEvent.Status.Should().Be(PaymentStatus.Failed.ToString());
    }

    [Fact]
    public async Task GIVEN_no_previous_payment_WHEN_ProcessInitialPaymentAsync_THEN_should_set_internal_reference()
    {
        // Arrange
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        // Act
        ProcessInitialPaymentResult result =
            await _paymentService.ProcessInitialPaymentAsync(preauthPayment, null, CancellationToken.None);

        // Assert
        result.Payment.InternalReference.Should().NotBeNullOrWhiteSpace();
        result.Payment.InternalReference.Should().Be("invoice1_0");
        result.Payment.Attempt.Should().Be(0);
    }

    [Fact]
    public async Task
        GIVEN_failed_previous_payment_WHEN_ProcessInitialPaymentAsync_THEN_should_increment_attempt_and_update_internal_reference()
    {
        // Arrange
        var prevPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        prevPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, prevPayment.Money, "Failed by PSP.");
        prevPayment.SetAttempt(1);

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PaymentAggregate> { prevPayment });

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        // Act
        ProcessInitialPaymentResult result =
            await _paymentService.ProcessInitialPaymentAsync(preauthPayment, null, CancellationToken.None);

        // Assert
        result.Payment.InternalReference.Should().NotBeNullOrWhiteSpace();
        result.Payment.InternalReference.Should().Be("invoice1_2");
        result.Payment.Attempt.Should().Be(2);
    }

    [Fact]
    public async Task
        GIVEN_no_previous_recurring_payment_WHEN_ProcessRecurringPaymentAsync_THEN_should_set_internal_reference()
    {
        // Arrange
        var prevPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice0",
            "payor1",
            null
        );
        prevPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, prevPayment.Money, "For test");
        var pspBearer = new PSPBearerPseudoCC { Token = "profileToken" };
        prevPayment.SetInitialBearer(pspBearer);
        prevPayment.SetPayerData(new PayerData(
            language: null,
            emailAddress: "<EMAIL>",
            address: new Address(
                "testLine1",
                "testLine2",
                string.Empty,
                string.Empty,
                "testPostalCode",
                "testCity",
                "testState",
                "testCountry"),
            lastName: "LastName",
            firstName: "FirstName",
            externalCustomerId: string.Empty,
            companyName: string.Empty,
            phoneNumber: "**********"));
        prevPayment.SetPspSettings(PspSettingsJson);

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PaymentAggregate> { prevPayment });

        RecurringPaymentAggregate result = await _paymentService.ProcessRecurringPaymentAsync(
            500m,
            2,
            "EUR",
            "policy1",
            "invoice1",
            "payor1",
            null,
            CancellationToken.None
        );

        // Assert
        result.InternalReference.Should().NotBeNullOrWhiteSpace();
        result.InternalReference.Should().Be("invoice1_0");
        result.Attempt.Should().Be(0);
    }

    [Fact]
    public async Task GIVEN_initialization_tokens_with_matching_payments_WHEN_CancelPaymentInitializationsAsync_THEN_should_send_CancelPaymentCommand()
    {
        // Arrange
        var initializationToken1 = "token1";
        var initializationToken2 = "token2";
        var initializationTokens = new List<string> { initializationToken1, initializationToken2 };

        var tokenizedPaymentInit1 = new TokenizedPaymentInitializationAggregate(
            PaymentProvider.Stripe,
            1000m,
            "978",
            "EUR",
            2,
            null,
            null,
            "policy1",
            "invoice1",
            "payor1"
        );
        var tokenizedPaymentInit2 = new TokenizedPaymentInitializationAggregate(
            PaymentProvider.Stripe,
            2000m,
            "978",
            "EUR",
            2,
            null,
            null,
            "policy2",
            "invoice2",
            "payor2"
        );

        var preauthPayment1 = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null,
            initializationToken1
        );

        var preauthPayment2 = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 2000, 2),
            "policy2",
            "invoice2",
            "payor2",
            null,
            initializationToken2
        );

        _tokenizedPaymentInitializationRepositoryMock
            .Setup(repo => repo.FindAllAsync(
                It.Is<List<string>>(ids => ids.Contains(initializationToken1) && ids.Contains(initializationToken2)),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TokenizedPaymentInitializationAggregate> { tokenizedPaymentInit1, tokenizedPaymentInit2 });

        _tokenizedPaymentInitializationRepositoryMock
            .Setup(repo => repo.UpdateBatchAsync(It.IsAny<List<TokenizedPaymentInitializationAggregate>>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<PaymentAggregate> { preauthPayment1, preauthPayment2 });

        // Act
        await _paymentService.CancelPaymentInitializationsAsync(initializationTokens, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        _mediatorMock.Verify(
            m => m.Send(It.Is<CancelPaymentCommand>(cmd => cmd.PaymentId == preauthPayment1.Id), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should send CancelPaymentCommand for first payment");

        _mediatorMock.Verify(
            m => m.Send(It.Is<CancelPaymentCommand>(cmd => cmd.PaymentId == preauthPayment2.Id), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should send CancelPaymentCommand for second payment");

        // Verify that the initialization aggregates were marked as cancelled
        _tokenizedPaymentInitializationRepositoryMock.Verify(
            repo => repo.UpdateBatchAsync(
                It.Is<List<TokenizedPaymentInitializationAggregate>>(list =>
                    list.Count == 2 &&
                    list.All(a => a.IsCanceled == true)),
                It.IsAny<CancellationToken>()),
            Times.Once,
            "Should update initialization tokens to cancelled status");

        // Verify that the CancelPaymentCommand includes the correct status and reason
        _mediatorMock.Verify(
            m => m.Send(It.Is<CancelPaymentCommand>(cmd =>
                cmd.CancellationStatus == PaymentStatus.Canceled &&
                cmd.CancellationReason == "Payments cancelled"), It.IsAny<CancellationToken>()),
            Times.Exactly(2),
            "Should send CancelPaymentCommand with correct cancellation status and reason");
    }

    [Fact]
    public async Task GIVEN_initialization_tokens_with_no_matching_payments_WHEN_CancelPaymentInitializationsAsync_THEN_should_not_send_CancelPaymentCommand()
    {
        // Arrange
        var initializationTokens = new List<string> { "token1", "token2" };

        _tokenizedPaymentInitializationRepositoryMock
            .Setup(repo => repo.FindAllAsync(It.IsAny<List<string>>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(new List<TokenizedPaymentInitializationAggregate>());

        // Act
        await _paymentService.CancelPaymentInitializationsAsync(initializationTokens, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        _mediatorMock.Verify(
            m => m.Send(It.IsAny<CancelPaymentCommand>(), It.IsAny<CancellationToken>()),
            Times.Never,
            "Should not send any CancelPaymentCommand when no initialization tokens are found");
    }

    // CancelPreauthPaymentAsync Tests

    [Fact]
    public async Task GIVEN_valid_preauth_payment_without_provider_id_WHEN_CancelPreauthPaymentAsync_THEN_should_cancel_locally()
    {
        // Arrange
        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);

        _paymentRepositoryMock
            .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(preauthPayment);

        _paymentRepositoryMock
            .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

        // Act
        PreauthPaymentAggregate result = await _paymentService.CancelPreauthPaymentAsync(preauthPayment.Id, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(preauthPayment.Id);
        result.PreauthStatus.Should().Be(PreauthPaymentStatus.Cancelled);
        result.Status.Should().Be(PaymentStatus.Canceled);

        // Verify repository interactions
        _paymentRepositoryMock.Verify(
            repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()),
            Times.Once,
            "Should retrieve the payment from repository");

        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.Is<PreauthPaymentAggregate>(p =>
                p.Id == preauthPayment.Id &&
                p.PreauthStatus == PreauthPaymentStatus.Cancelled &&
                p.Status == PaymentStatus.Canceled), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should update the payment with cancelled status");
    }

    [Fact]
    public async Task GIVEN_valid_preauth_payment_with_provider_id_WHEN_CancelPreauthPaymentAsync_THEN_should_call_psp_service()
    {
        // Arrange
        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);
        preauthPayment.AssignProviderTransaction("stripe_payment_123");

        var paymentProviderServiceMock = new Mock<IPaymentProviderService>();
        paymentProviderServiceMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        paymentProviderServiceMock
            .Setup(p => p.CancelPreauthPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .Returns(Task.CompletedTask);

        var paymentService = CreatePaymentServiceWithCustomProviders(new[] { paymentProviderServiceMock.Object });

        _paymentRepositoryMock
            .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(preauthPayment);

        _paymentRepositoryMock
            .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

        // Act
        PreauthPaymentAggregate result = await paymentService.CancelPreauthPaymentAsync(preauthPayment.Id, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(preauthPayment.Id);

        // Verify PSP service was called
        paymentProviderServiceMock.Verify(
            p => p.CancelPreauthPaymentAsync(It.Is<PreauthPaymentAggregate>(pa => pa.Id == preauthPayment.Id), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should call PSP service to cancel the payment");

        // Verify repository interactions
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should update the payment in repository");
    }

    [Fact]
    public async Task GIVEN_non_preauth_payment_WHEN_CancelPreauthPaymentAsync_THEN_should_throw_domain_exception()
    {
        // Arrange
        var recurringPayment = new RecurringPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.GetByIdAsync(recurringPayment.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(recurringPayment);

        // Act & Assert
        var exception = await FluentActions
            .Invoking(() => _paymentService.CancelPreauthPaymentAsync(recurringPayment.Id, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None))
            .Should().ThrowAsync<DomainException>();

        exception.WithMessage("Cancellation failed: Only preauthorized transactions can be canceled.");

        // Verify repository was called but no update was made
        _paymentRepositoryMock.Verify(
            repo => repo.GetByIdAsync(recurringPayment.Id, It.IsAny<CancellationToken>()),
            Times.Once,
            "Should attempt to retrieve the payment");

        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PaymentAggregate>(), It.IsAny<CancellationToken>()),
            Times.Never,
            "Should not update any payment when validation fails");
    }

    [Fact]
    public async Task GIVEN_psp_service_throws_exception_WHEN_CancelPreauthPaymentAsync_THEN_should_mark_payment_as_failed()
    {
        // Arrange
        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);
        preauthPayment.AssignProviderTransaction("stripe_payment_123");

        var expectedErrorMessage = "PSP service cancellation failed";
        var paymentProviderServiceMock = new Mock<IPaymentProviderService>();
        paymentProviderServiceMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        paymentProviderServiceMock
            .Setup(p => p.CancelPreauthPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception(expectedErrorMessage));

        var paymentService = CreatePaymentServiceWithCustomProviders(new[] { paymentProviderServiceMock.Object });

        _paymentRepositoryMock
            .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(preauthPayment);

        _paymentRepositoryMock
            .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

        // Act
        PreauthPaymentAggregate result = await paymentService.CancelPreauthPaymentAsync(preauthPayment.Id, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Id.Should().Be(preauthPayment.Id);
        result.Status.Should().Be(PaymentStatus.Failed);

        // Verify the payment was marked as failed with the correct error message
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.Is<PreauthPaymentAggregate>(p =>
                p.Id == preauthPayment.Id &&
                p.Status == PaymentStatus.Failed &&
                p.PaymentStatusHistoryItem!.Error == expectedErrorMessage), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should update payment with failed status and error message");
    }

    [Fact]
    public async Task GIVEN_payment_already_failed_WHEN_CancelPreauthPaymentAsync_with_exception_THEN_should_not_change_status()
    {
        // Arrange
        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, preauthPayment.Money, "Already failed");
        preauthPayment.AssignProviderTransaction("stripe_payment_123");

        var paymentProviderServiceMock = new Mock<IPaymentProviderService>();
        paymentProviderServiceMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        paymentProviderServiceMock
            .Setup(p => p.CancelPreauthPaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ThrowsAsync(new Exception("PSP error"));

        var paymentService = CreatePaymentServiceWithCustomProviders(new[] { paymentProviderServiceMock.Object });

        _paymentRepositoryMock
            .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(preauthPayment);

        _paymentRepositoryMock
            .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

        // Act
        PreauthPaymentAggregate result = await paymentService.CancelPreauthPaymentAsync(preauthPayment.Id, PaymentStatus.Canceled, "Payments cancelled", CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(PaymentStatus.Failed);
        result.PaymentStatusHistoryItem?.Error.Should().Be("Already failed", "Should maintain the original error message");

        // Verify repository update was called (in finally block)
        _paymentRepositoryMock.Verify(
            repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()),
            Times.Once,
            "Should still update the payment in finally block");
    }

    private PaymentService CreatePaymentServiceWithCustomProviders(IEnumerable<IPaymentProviderService> paymentProviders)
    {
        var pspSettingsProviderMock = new Mock<IPspSettingsProvider>();
        pspSettingsProviderMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        pspSettingsProviderMock
            .Setup(p => p.GetPspSettingsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new StripePspSettingsAggregate { UseAutoCapture = false });

        return new PaymentService(
            Mock.Of<ILogger<PaymentService>>(),
            _tokenizedPaymentInitializationRepositoryMock.Object,
            _paymentRepositoryMock.Object,
            _refundRepositoryMock.Object,
            paymentProviders,
            new[] { pspSettingsProviderMock.Object },
            Mock.Of<IMongoCollection<PaymentAggregate>>(),
            Mock.Of<IAuthService>(),
            Mock.Of<ITenantProvider>(),
            _mediatorMock.Object
        );
    }

    private static JsonElement PrepareDynamicFields()
    {
        const string json = "{\"customerFirstName\":\"John\",\"customerLastName\":\"Doe\",\"customerId\":\"CUST001\",\"customerEmail\":\"<EMAIL>\",\"customerPhone\":\"501501501\"}";
        return JsonDocument.Parse(json).RootElement;
    }

    private static JsonElement PrepareDynamicFieldsWithClientKey(string clientKey, Dictionary<string, string>? additionalFields = null)
    {
        var fields = new Dictionary<string, string>
        {
            ["clientKey"] = clientKey,
            ["customerFirstName"] = "John",
            ["customerLastName"] = "Doe",
            ["customerId"] = "CUST001",
            ["customerEmail"] = "<EMAIL>",
            ["customerPhone"] = "501501501"
        };

        if (additionalFields != null)
        {
            foreach (var field in additionalFields)
            {
                fields[field.Key] = field.Value;
            }
        }

        var json = System.Text.Json.JsonSerializer.Serialize(fields);
        return JsonDocument.Parse(json).RootElement;
    }

    private static JsonElement PrepareDynamicFieldsWithoutClientKey()
    {
        const string json = "{\"customerFirstName\":\"Jane\",\"customerLastName\":\"Smith\",\"customerId\":\"CUST002\",\"customerEmail\":\"<EMAIL>\",\"customerPhone\":\"502502502\"}";
        return JsonDocument.Parse(json).RootElement;
    }

    // SetPostCheckoutRedirectUrl Tests

    [Fact]
    public async Task GIVEN_dynamic_fields_with_clientKey_WHEN_ProcessInitialPaymentAsync_THEN_should_use_client_specific_redirect_url()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var clientKey = "testClient";

        var dynamicFieldsWithClientKey = PrepareDynamicFieldsWithClientKey(clientKey);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", $"https://{clientKey}.example.com", "https://another.example.com" }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should have the postCheckoutRedirectUrl added
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be($"https://{clientKey}.example.com");

        // Original fields should be preserved
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be(clientKey);
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
        resultDynamicFields.TryGetProperty("customerEmail", out JsonElement emailElement).Should().BeTrue();
        emailElement.GetString().Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GIVEN_dynamic_fields_without_clientKey_WHEN_ProcessInitialPaymentAsync_THEN_should_return_original_dynamic_fields()
    {
        // Arrange
        var dynamicFieldsWithoutClientKey = PrepareDynamicFieldsWithoutClientKey();

        var tenantProviderMock = new Mock<ITenantProvider>();
        var authServiceMock = new Mock<IAuthService>();

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithoutClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should not have postCheckoutRedirectUrl since no clientKey was provided
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Original customer fields should be preserved
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("Jane");
        resultDynamicFields.TryGetProperty("customerEmail", out JsonElement emailElement).Should().BeTrue();
        emailElement.GetString().Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GIVEN_no_tenant_context_WHEN_ProcessInitialPaymentAsync_THEN_should_return_original_dynamic_fields()
    {
        // Arrange
        var clientKey = "testClient";
        var dynamicFieldsWithClientKey = PrepareDynamicFieldsWithClientKey(clientKey);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = null; return false; });

        var authServiceMock = new Mock<IAuthService>();

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should not have postCheckoutRedirectUrl since no tenant context
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Original fields should be preserved
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be(clientKey);
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
        resultDynamicFields.TryGetProperty("customerEmail", out JsonElement emailElement).Should().BeTrue();
        emailElement.GetString().Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GIVEN_clientKey_with_no_matching_host_WHEN_ProcessInitialPaymentAsync_THEN_should_return_original_dynamic_fields()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var clientKey = "nonExistentClient";

        var dynamicFieldsWithClientKey = PrepareDynamicFieldsWithClientKey(clientKey);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", "https://client1.example.com", "https://client2.example.com" }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should not have postCheckoutRedirectUrl since no matching host
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Original fields should be preserved
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be(clientKey);
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
        resultDynamicFields.TryGetProperty("customerEmail", out JsonElement emailElement).Should().BeTrue();
        emailElement.GetString().Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GIVEN_null_dynamic_fields_with_clientKey_from_tenant_WHEN_ProcessInitialPaymentAsync_THEN_should_create_new_dynamic_fields_with_redirect_url()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var clientKey = "testClient";

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", $"https://{clientKey}.example.com" }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, null, CancellationToken.None);

        // Assert
        // Since there's no clientKey in the dynamic fields, it should return the original null/empty fields
        // The current implementation only uses clientKey from existing dynamic fields, not from tenant settings
        if (result.Payment.DynamicFields != null)
        {
            var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields).RootElement;
            // Should not have postCheckoutRedirectUrl since no clientKey was provided in dynamic fields
            resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();
        }
    }

    [Fact]
    public async Task GIVEN_dynamic_fields_with_empty_clientKey_WHEN_ProcessInitialPaymentAsync_THEN_should_return_original_dynamic_fields()
    {
        // Arrange
        var dynamicFieldsWithEmptyClientKey = PrepareDynamicFieldsWithClientKey(""); // Empty clientKey

        var tenantProviderMock = new Mock<ITenantProvider>();
        var authServiceMock = new Mock<IAuthService>();

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithEmptyClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should not have postCheckoutRedirectUrl since clientKey is empty
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Original fields should be preserved
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("");
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
        resultDynamicFields.TryGetProperty("customerEmail", out JsonElement emailElement).Should().BeTrue();
        emailElement.GetString().Should().Be("<EMAIL>");
    }

    [Fact]
    public async Task GIVEN_dynamic_fields_with_existing_postCheckoutRedirectUrl_WHEN_ProcessInitialPaymentAsync_THEN_should_override_with_client_specific_url()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var clientKey = "testClient";
        var existingRedirectUrl = "https://existing.example.com";

        var dynamicFieldsWithExistingUrl = JsonDocument.Parse($"{{\"clientKey\":\"{clientKey}\",\"postCheckoutRedirectUrl\":\"{existingRedirectUrl}\",\"otherField\":\"value\"}}").RootElement;

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", $"https://{clientKey}.example.com" }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithExistingUrl, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be($"https://{clientKey}.example.com");
        redirectUrlElement.GetString().Should().NotBe(existingRedirectUrl);

        // Other fields should be preserved
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be(clientKey);
        resultDynamicFields.TryGetProperty("otherField", out JsonElement otherFieldElement).Should().BeTrue();
        otherFieldElement.GetString().Should().Be("value");
    }

    [Fact]
    public async Task GIVEN_host_without_https_schema_WHEN_ProcessInitialPaymentAsync_THEN_should_add_https_schema()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var clientKey = "testClient";

        var dynamicFieldsWithClientKey = JsonDocument.Parse($"{{\"clientKey\":\"{clientKey}\"}}").RootElement;

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "default.example.com", $"{clientKey}.example.com" } // Without https://
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        var preauthPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );

        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(Enumerable.Empty<PaymentAggregate>());

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(preauthPayment, dynamicFieldsWithClientKey, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be($"https://{clientKey}.example.com");
        redirectUrlElement.GetString().Should().StartWith("https://");
    }

    // ProcessInitialPaymentAsync Update Tests

    [Fact]
    public async Task GIVEN_previous_payment_with_dynamic_fields_WHEN_ProcessInitialPaymentAsync_with_IsUpdate_true_THEN_should_use_previous_dynamic_fields_and_add_member_clientKey()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var memberPortalUrl = "https://member.example.com";

        var previousDynamicFields = JsonDocument.Parse("{\"customerFirstName\":\"John\",\"customerLastName\":\"Doe\",\"customerId\":\"CUST001\"}").RootElement;

        var previousPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 500, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        previousPayment.SetDynamicFields(System.Text.Json.JsonSerializer.Serialize(previousDynamicFields));

        var currentPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice2",
            "payor1",
            null
        );
        // Mark as update payment
        var isUpdateProperty = typeof(PreauthPaymentAggregate).GetProperty("IsUpdate");
        isUpdateProperty?.SetValue(currentPayment, true);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", memberPortalUrl, "https://another.example.com" }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        // Setup repository to return previous payment
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { previousPayment });

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(currentPayment, null, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should have clientKey = "member" added
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("member");

        // Should have postCheckoutRedirectUrl added
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be(memberPortalUrl);

        // Should preserve original fields from previous payment
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
        resultDynamicFields.TryGetProperty("customerLastName", out JsonElement lastNameElement).Should().BeTrue();
        lastNameElement.GetString().Should().Be("Doe");
        resultDynamicFields.TryGetProperty("customerId", out JsonElement customerIdElement).Should().BeTrue();
        customerIdElement.GetString().Should().Be("CUST001");

        // Should have previous payment ID set
        result.Payment.Should().BeOfType<PreauthPaymentAggregate>();
        var preauthResult = (PreauthPaymentAggregate)result.Payment;
        preauthResult.PrevPaymentId.Should().Be(previousPayment.Id);
    }

    [Fact]
    public async Task GIVEN_previous_payment_with_existing_clientKey_WHEN_ProcessInitialPaymentAsync_with_IsUpdate_true_THEN_should_override_clientKey_with_member()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var memberPortalUrl = "https://member.example.com";

        var previousDynamicFields = JsonDocument.Parse("{\"clientKey\":\"existingClient\",\"customerFirstName\":\"John\",\"postCheckoutRedirectUrl\":\"https://old.example.com\"}").RootElement;

        var previousPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 500, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        previousPayment.SetDynamicFields(System.Text.Json.JsonSerializer.Serialize(previousDynamicFields));

        var currentPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice2",
            "payor1",
            null
        );
        // Mark as update payment
        var isUpdateProperty = typeof(PreauthPaymentAggregate).GetProperty("IsUpdate");
        isUpdateProperty?.SetValue(currentPayment, true);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", memberPortalUrl }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        // Setup repository to return previous payment
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { previousPayment });

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(currentPayment, null, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should have clientKey overridden to "member"
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("member");
        clientKeyElement.GetString().Should().NotBe("existingClient");

        // Should have postCheckoutRedirectUrl updated
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be(memberPortalUrl);
        redirectUrlElement.GetString().Should().NotBe("https://old.example.com");

        // Should preserve other original fields
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
    }

    [Fact]
    public async Task GIVEN_previous_payment_without_dynamic_fields_WHEN_ProcessInitialPaymentAsync_with_IsUpdate_true_THEN_should_create_new_dynamic_fields_with_member_clientKey()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");
        var memberPortalUrl = "https://member.example.com";

        var previousPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 500, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        // Previous payment has no dynamic fields

        var currentPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice2",
            "payor1",
            null
        );
        // Mark as update payment
        var isUpdateProperty = typeof(PreauthPaymentAggregate).GetProperty("IsUpdate");
        isUpdateProperty?.SetValue(currentPayment, true);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", memberPortalUrl }
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        // Setup repository to return previous payment
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { previousPayment });

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(currentPayment, null, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should have clientKey = "member" added
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("member");

        // Should have postCheckoutRedirectUrl added
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out JsonElement redirectUrlElement).Should().BeTrue();
        redirectUrlElement.GetString().Should().Be(memberPortalUrl);
    }

    [Fact]
    public async Task GIVEN_no_tenant_context_WHEN_ProcessInitialPaymentAsync_with_IsUpdate_true_THEN_should_use_previous_dynamic_fields_without_modifications()
    {
        // Arrange
        var previousDynamicFields = JsonDocument.Parse("{\"clientKey\":\"existingClient\",\"customerFirstName\":\"John\"}").RootElement;

        var previousPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 500, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        previousPayment.SetDynamicFields(System.Text.Json.JsonSerializer.Serialize(previousDynamicFields));

        var currentPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice2",
            "payor1",
            null
        );
        // Mark as update payment
        var isUpdateProperty = typeof(PreauthPaymentAggregate).GetProperty("IsUpdate");
        isUpdateProperty?.SetValue(currentPayment, true);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = null; return false; });

        var authServiceMock = new Mock<IAuthService>();

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        // Setup repository to return previous payment
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { previousPayment });

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(currentPayment, null, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should preserve original clientKey without modification
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("existingClient");

        // Should not have postCheckoutRedirectUrl added since no tenant context
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Should preserve other fields
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
    }

    [Fact]
    public async Task GIVEN_no_member_host_in_tenant_settings_WHEN_ProcessInitialPaymentAsync_with_IsUpdate_true_THEN_should_use_previous_dynamic_fields_without_modifications()
    {
        // Arrange
        var tenantId = new TenantId("test-tenant-id");

        var previousDynamicFields = JsonDocument.Parse("{\"clientKey\":\"existingClient\",\"customerFirstName\":\"John\"}").RootElement;

        var previousPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 500, 2),
            "policy1",
            "invoice1",
            "payor1",
            null
        );
        previousPayment.SetDynamicFields(System.Text.Json.JsonSerializer.Serialize(previousDynamicFields));

        var currentPayment = new PreauthPaymentAggregate(
            PaymentProvider.Stripe,
            new PaymentMoney("978", "EUR", 1000, 2),
            "policy1",
            "invoice2",
            "payor1",
            null
        );
        // Mark as update payment
        var isUpdateProperty = typeof(PreauthPaymentAggregate).GetProperty("IsUpdate");
        isUpdateProperty?.SetValue(currentPayment, true);

        var tenantProviderMock = new Mock<ITenantProvider>();
        tenantProviderMock.Setup(x => x.TryGetCurrent(out It.Ref<TenantId?>.IsAny))
            .Returns((out TenantId? id) => { id = tenantId; return true; });

        var tenantSettings = new TenantSettings
        {
            Hosts = new List<string> { "https://default.example.com", "https://client1.example.com" } // No member host
        };

        var authServiceMock = new Mock<IAuthService>();
        authServiceMock.Setup(x => x.GetTenantSettingsAsync(tenantId.Value, It.IsAny<CancellationToken>()))
            .ReturnsAsync(tenantSettings);

        var paymentService = CreatePaymentServiceWithMocks(tenantProviderMock.Object, authServiceMock.Object);

        // Setup repository to return previous payment
        _paymentRepositoryMock
            .Setup(repo => repo.FindAllByAsync(It.IsAny<Expression<Func<PaymentAggregate, bool>>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(new[] { previousPayment });

        // Act
        ProcessInitialPaymentResult result = await paymentService.ProcessInitialPaymentAsync(currentPayment, null, CancellationToken.None);

        // Assert
        result.Payment.DynamicFields.Should().NotBeNull();
        var resultDynamicFields = JsonDocument.Parse(result.Payment.DynamicFields!).RootElement;

        // Should preserve original clientKey without modification
        resultDynamicFields.TryGetProperty("clientKey", out JsonElement clientKeyElement).Should().BeTrue();
        clientKeyElement.GetString().Should().Be("existingClient");

        // Should not have postCheckoutRedirectUrl added since no member host
        resultDynamicFields.TryGetProperty("postCheckoutRedirectUrl", out _).Should().BeFalse();

        // Should preserve other fields
        resultDynamicFields.TryGetProperty("customerFirstName", out JsonElement firstNameElement).Should().BeTrue();
        firstNameElement.GetString().Should().Be("John");
    }

    private PaymentService CreatePaymentServiceWithMocks(ITenantProvider tenantProvider, IAuthService authService)
    {
        Mock<ILogger<PaymentService>> loggerMock = new();
        Mock<IMongoCollection<PaymentAggregate>> mongoCollectionMock = new();

        var pspSettingsProviderMock = new Mock<IPspSettingsProvider>();
        pspSettingsProviderMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        pspSettingsProviderMock
            .Setup(p => p.GetPspSettingsAsync(It.IsAny<CancellationToken>()))
            .ReturnsAsync(new StripePspSettingsAggregate { UseAutoCapture = false });

        var paymentProviderServiceMock = new Mock<IPaymentProviderService>();
        paymentProviderServiceMock.Setup(p => p.Type).Returns(PaymentProvider.Stripe);
        paymentProviderServiceMock
            .Setup(p => p.PreparePaymentAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync((PreauthPaymentAggregate p, CancellationToken _) => p);

        return new PaymentService(
            loggerMock.Object,
            _tokenizedPaymentInitializationRepositoryMock.Object,
            _paymentRepositoryMock.Object,
            _refundRepositoryMock.Object,
            new[] { paymentProviderServiceMock.Object },
            new[] { pspSettingsProviderMock.Object },
            mongoCollectionMock.Object,
            authService,
            tenantProvider,
            _mediatorMock.Object
        );
    }
}