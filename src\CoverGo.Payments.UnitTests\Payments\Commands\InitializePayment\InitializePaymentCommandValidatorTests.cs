using CoverGo.Payments.Application.Payments.Commands.InitializePayment;
using CoverGo.Payments.Domain.Payment;
using FluentAssertions;
using FluentValidation.Results;
using Microsoft.Extensions.Logging;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments.Commands.InitializePayment;

public class InitializePaymentCommandValidatorTests
{
    [Fact]
    public async Task GIVEN_initialize_payment_command_without_initialization_token_and_amount_WHEN_validating_THEN_expected_validation_error_returned()
    {
        string expectedErrorMessage = "'Amount' must not be empty.";

        InitializePaymentCommand command = CreateTestInitializePaymentCommandWithoutInitializationToken(amount: null);

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.Errors
            .Should()
            .ContainSingle()
            .Which
            .Should()
            .Match<ValidationFailure>(vf => vf.ErrorMessage == expectedErrorMessage);
    }

    [Fact]
    public async Task GIVEN_initialize_payment_command_without_initialization_token_and_currency_code_WHEN_validating_THEN_expected_validation_error_returned()
    {
        string expectedErrorMessage = "No currency code found.";

        InitializePaymentCommand command = CreateTestInitializePaymentCommandWithoutInitializationToken(currencyCode: null);

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.Errors
            .Should()
            .ContainSingle()
            .Which
            .Should()
            .Match<ValidationFailure>(vf => vf.ErrorMessage == expectedErrorMessage);
    }

    [Fact]
    public async Task GIVEN_initialize_payment_command_without_initialization_token_and_currency_description_WHEN_validating_THEN_expected_validation_error_returned()
    {
        string expectedErrorMessage = "No currency description found.";

        InitializePaymentCommand command = CreateTestInitializePaymentCommandWithoutInitializationToken(currencyDesc: null);

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.Errors
            .Should()
            .ContainSingle()
            .Which
            .Should()
            .Match<ValidationFailure>(vf => vf.ErrorMessage == expectedErrorMessage);
    }

    [Fact]
    public async Task GIVEN_initialize_payment_command_without_initialization_token_and_decimal_precision_WHEN_validating_THEN_expected_validation_error_returned()
    {
        string expectedErrorMessage = "'Decimal Precision' must not be empty.";

        InitializePaymentCommand command = CreateTestInitializePaymentCommandWithoutInitializationToken(decimalPrecision: null);

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.Errors
            .Should()
            .ContainSingle()
            .Which
            .Should()
            .Match<ValidationFailure>(vf => vf.ErrorMessage == expectedErrorMessage);
    }

    [Fact]
    public async Task GIVEN_initialize_payment_command_without_initialization_token_and_payment_provider_WHEN_validating_THEN_expected_validation_error_returned()
    {
        string expectedErrorMessage = "No payment provider found.";

        InitializePaymentCommand command = CreateTestInitializePaymentCommandWithoutInitializationToken(paymentProvider: null);

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.Errors
            .Should()
            .ContainSingle()
            .Which
            .Should()
            .Match<ValidationFailure>(vf => vf.ErrorMessage == expectedErrorMessage);
    }

    [Fact]
    public async Task GIVEN_initialize_payment_command_with_initialization_token_WHEN_validating_THEN_no_validation_errors_returned()
    {
        InitializePaymentCommand command = new(
            Amount: null,
            CurrencyCode: null,
            CurrencyDesc: null,
            DecimalPrecision: null,
            PaymentProvider: null,
            DynamicFields: null,
            PolicyId: null,
            InvoiceNumber: null,
            PayorId: null,
            InitializationToken: Guid.NewGuid().ToString()
        );

        InitializePaymentCommandValidator validator = new(Mock.Of<ILogger<InitializePaymentCommandValidator>>());

        ValidationResult validationResult = await validator.ValidateAsync(command, CancellationToken.None);

        validationResult.IsValid.Should().BeTrue();
        validationResult.Errors.Should().BeEmpty();
    }

    private static InitializePaymentCommand CreateTestInitializePaymentCommandWithoutInitializationToken(
        decimal? amount = 1818.58m,
        string? currencyCode = "682",
        string? currencyDesc = "SAR",
        int? decimalPrecision = 2,
        string? policyId = null,
        string? invoiceNumber = null,
        string? payorId = null,
        PaymentProvider? paymentProvider = PaymentProvider.Walaa
    ) => new InitializePaymentCommand(
        Amount: amount,
        CurrencyCode: currencyCode,
        CurrencyDesc: currencyDesc,
        DecimalPrecision: decimalPrecision,
        PaymentProvider: paymentProvider,
        DynamicFields: null,
        InitializationToken: null,
        PolicyId: policyId ?? Guid.NewGuid().ToString(),
        InvoiceNumber: invoiceNumber ?? Guid.NewGuid().ToString(),
        PayorId: payorId ?? Guid.NewGuid().ToString()
    );
}
