﻿using CoverGo.Multitenancy;
using Microsoft.Extensions.DependencyInjection;
using CoverGo.BuildingBlocks.DataAccess.Mongo;
using Microsoft.Extensions.Configuration;
using CoverGo.Payments.Infrastructure;
using CoverGo.Payments.Infrastructure.DataAccess;
using CoverGo.Payments.IntegrationTests.Encryption.TestData;
using CoverGo.BuildingBlocks.Application.Core.Ports;

namespace CoverGo.Payments.IntegrationTests.Support
{
    public class TestApp
    {
        public ServiceProvider ConfigureServices(Action<ServiceCollection>? configure = null)
        {
            ServiceCollection services = new ServiceCollection();
            var manager = new ConfigurationManager();

            string? env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development.Tests";
            manager
                .AddJsonFile("testsettings.json")
                .AddJsonFile($"testsettings.{env}.json");

            string? dbConnectionString = manager
                .GetSection("MongoDatabaseConfiguration")
                .GetValue<string>("ConnectionString") ?? throw new InvalidOperationException("Database connection string is not configured.");

            services.AddMongoDb(options =>
            {
                options.DatabaseName = "TestDatabase";
                options.ConnectionString = dbConnectionString;
            });
            services.AddLogging();
            services.AddSingleton(new TenantId("CoverGo"));

            RegisterClassMaps(services);
            RegisterRepositories(services);

            services.AddScoped<IUserContextProvider, TestUserContextProvider>();
            services.AddMediatR(delegate (MediatRServiceConfiguration configuration)
            {
                configuration.RegisterServicesFromAssembly(typeof(TestApp).Assembly);
            });

            configure?.Invoke(services);
            ServiceProvider serviceProvider = services.BuildServiceProvider();
            return serviceProvider;
        }

        private void RegisterClassMaps(IServiceCollection services)
        {
            var encryptionService = InfrastructureServiceExtensions.RegisterEncryption(services);
            EncryptedFieldConfigurator.RegisterClassMap<SimpleAggregate>(encryptionService);
            EncryptedFieldConfigurator.RegisterClassMap<Class1Aggregate>(encryptionService);
            EncryptedFieldConfigurator.RegisterClassMap<Class2Aggregate>(encryptionService);
            EncryptedFieldConfigurator.RegisterClassMap<List1Aggregate>(encryptionService);
            EncryptedFieldConfigurator.RegisterClassMap<List2Aggregate>(encryptionService);
        }

        private void RegisterRepositories(IServiceCollection services)
        {
            services.AddMongoRepository<SimpleAggregate, string>();
            services.AddMongoRepository<Class1Aggregate, string>();
            services.AddMongoRepository<Class2Aggregate, string>();
            services.AddMongoRepository<List1Aggregate, string>();
            services.AddMongoRepository<List2Aggregate, string>();
        }
    }
}
