﻿using GuardClauses;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Builders
{
    public static class PayUClientUrlBuilder
    {
        public static Uri BuildGetPaymentUrl(string baseUrl, string apiVersion, string merchantPaymentReference)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));
            
            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/payments/status/", merchantPaymentReference));
        }

        public static Uri BuildPostPaymentUrl(string baseUrl, string apiVersion)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));

            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/payments/authorize/"));
        }
        
        public static Uri BuildCapturePaymentUrl(string baseUrl, string apiVersion)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));

            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/payments/capture/"));
        }
        
        public static Uri BuildRefundPaymentUrl(string baseUrl, string apiVersion)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));

            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/payments/refund/"));
        }
        
        public static Uri BuildTokenPaymentUrl(string baseUrl, string apiVersion)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));

            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/token/"));
        }

        public static Uri BuildPostSessionUrl(string baseUrl, string apiVersion)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(baseUrl));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(baseUrl, nameof(apiVersion));

            return new Uri(string.Concat(baseUrl, "/api/", apiVersion, "/payments/sessions/"));
        }
    }
}