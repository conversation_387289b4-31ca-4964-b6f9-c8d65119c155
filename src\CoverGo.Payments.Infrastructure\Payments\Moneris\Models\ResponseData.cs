﻿using System.Text.Json;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class ResponseData
{
    [JsonProperty("success")]
    public string? Success { get; set; }
    
    [JsonProperty("ticket")]
    public string? Ticket { get; set; }
    
    [JsonProperty("token")]
    public string? Token { get; set; }
    
    [JsonProperty("error")]
    public Dictionary<string, object>? Error { get; set; }
    
    [JsonProperty("request")]
    public Request? Request { get; set; }

    [JsonProperty("receipt")]
    public Receipt? Receipt { get; set; }

    public bool IsSuccess => Success == "true";
}