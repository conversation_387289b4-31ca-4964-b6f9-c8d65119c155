services:
  covergo-mongo:
    build:
      dockerfile: ./Mongo.Dockerfile
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: local_dev
      MONGO_INITDB_DATABASE: payments
    command: --replSet covergo-mongo-set --keyFile mongo.key --bind_ip_all --port 27017
    healthcheck:
      test: echo "try { var statusResult = rs.status(); if (statusResult.ok != 1) { throw new Error('Not Ok'); } } catch (err) { var initiateResult = rs.initiate({_id:'covergo-mongo-set',members:[{_id:0,host:'covergo-mongo'}]}); if (initiateResult.ok != 1) { quit(1); } }" | mongosh --port 27017 -u root -p local_dev --authenticationDatabase admin --quiet
      interval: 5s
      timeout: 15s
      start_period: 5s
      retries: 10

  covergo-payments-tests:
    image: ghcr.io/covergo/payments-test:latest
    restart: "no"
    build:
      dockerfile: ./Dockerfile
      target: tests
      args:
        GH_ACCOUNT: ${GH_ACCOUNT}
        GH_TOKEN: ${GH_TOKEN}
      context: .
    environment:
      - ASPNETCORE_ENVIRONMENT=Staging.CI
      - MongoDatabaseConfiguration__ConnectionString=********************************************?replicaSet=covergo-mongo-set
    depends_on:
      covergo-mongo:
        condition: service_healthy
