using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.TokenizePaymentInitialization;

[MutationType]
public class TokenizePaymentInitializationMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "tokenizedPaymentInitialization")]
    //[Authorize]
    public async Task<TokenizedPaymentInitializationDto> TokenizePaymentInitialization(
        TokenizePaymentInitializationCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken
    ) => await commandProcessor.Send(input, cancellationToken);
}
