<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CoverGoInternalsVersion>3.75.0</CoverGoInternalsVersion>
  </PropertyGroup>
  <!-- CoverGo -->
  <ItemGroup>
    <PackageVersion Include="AutoFixture" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.AutoMoq" Version="4.18.1" />
    <PackageVersion Include="AutoFixture.Xunit2" Version="4.18.1" />
    <PackageVersion Include="CoverGo.Applications.HealthCheck" Version="3.75.0" />
    <PackageVersion Include="CoverGo.Applications.Startup" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Api.Graphql" Version="3.2.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Application.Core" Version="10.1.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Auth" Version="2.0.1-rc.3" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Bootstrapper" Version="2.4.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess" Version="9.0.2" />
    <PackageVersion Include="CoverGo.BuildingBlocks.DataAccess.Mongo" Version="9.0.3" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Domain.Core" Version="10.1.0" />
    <PackageVersion Include="covergo.buildingblocks.MessageBus.contracts" Version="5.0.2" />
    <PackageVersion Include="CoverGo.BuildingBlocks.MessageBus.Dapr" Version="5.0.2" />
    <PackageVersion Include="CoverGo.BuildingBlocks.MessageBus.Outbox.Mongo" Version="5.0.2" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Scheduler" Version="4.1.0" />
    <PackageVersion Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" Version="4.4.0" />
    <PackageVersion Include="CoverGo.Cases.Client.Rest" Version="2.37.0" />
    <PackageVersion Include="CoverGo.Gateway.Client" Version="2.289.0" />
    <PackageVersion Include="CoverGo.Policies.Client" Version="2.299.1" />
    <PackageVersion Include="CoverGo.Premium.Integration.Events" Version="1.22.0-rc.12" />
    <PackageVersion Include="CoverGo.Products.Client" Version="2.275.0-rc.1" />
    <PackageVersion Include="CoverGo.Templates.Client.Rest" Version="2.19.0" />
    <PackageVersion Include="CoverGo.Multitenancy.AspNetCore" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Policies.Integration.Events" Version="2.314.0-rc.40" />
    <PackageVersion Include="CoverGo.Proxies.Auth" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.Reference.Client" Version="1.12.0" />
    <PackageVersion Include="CoverGo.Users.Client" Version="2.49.0" />
    <PackageVersion Include="CoverGo.SettableValues" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="CoverGo.FeatureManagement" Version="$(CoverGoInternalsVersion)" />
    <PackageVersion Include="IdentityModel" Version="7.0.0" />
    <PackageVersion Include="Polly" Version="8.5.0" />
    <PackageVersion Include="WireMock.Net" Version="1.6.1" />
    <PackageVersion Include="Microsoft.Extensions.Caching.StackExchangeRedis" Version="8.0.10" />
  </ItemGroup>
  <!-- .NET -->
  <ItemGroup>
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.7" />
    <PackageVersion Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="8.0.2" />
    <PackageVersion Include="Microsoft.SourceLink.GitHub" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.Testing" Version="8.8.0" />
  </ItemGroup>
  <!-- HotChocolate -->
  <ItemGroup>
    <PackageVersion Include="HotChocolate.AspNetCore" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.AspNetCore.CommandLine" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Execution.Abstractions" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Types.Analyzers" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Stitching.Redis" Version="13.9.14" />
    <PackageVersion Include="HotChocolate.Data.MongoDb" Version="13.9.14" />
  </ItemGroup>
  <!-- 3rd party -->
  <ItemGroup>
    <PackageVersion Include="AutoMapper" Version="13.0.1" />
    <PackageVersion Include="GuardClauses" Version="1.2.4" />
    <PackageVersion Include="MongoDB.Driver" Version="2.22.0" />
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Scrutor" Version="4.2.2" />
    <PackageVersion Include="StrawberryShake.Server" Version="13.9.7" />
    <PackageVersion Include="Stripe.net" Version="44.13.0" />
    <PackageVersion Include="Swashbuckle.AspNetCore" Version="6.7.1" />
  </ItemGroup>
  <!-- Tests -->
  <ItemGroup>
    <PackageVersion Include="FluentAssertions" Version="6.12.0" />
    <PackageVersion Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.7" />
    <PackageVersion Include="Microsoft.NET.Test.Sdk" Version="17.10.0" />
    <PackageVersion Include="Moq" Version="4.20.70" />
    <PackageVersion Include="Snapshooter.Xunit" Version="0.14.1" />
    <PackageVersion Include="xunit" Version="2.8.0" />
    <PackageVersion Include="xunit.runner.visualstudio" Version="2.8.0" />
    <PackageVersion Include="coverlet.collector" Version="6.0.2" />
    <PackageVersion Include="JunitXml.TestLogger" Version="3.1.12" />
  </ItemGroup>
</Project>
