﻿using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Infrastructure.Exceptions;

public class IllegalPaymentStatusException : Exception
{
    public IllegalPaymentStatusException(PaymentStatus? status)
        :base($"Invalid payment status: {status}.")
    {
    }

    public IllegalPaymentStatusException(PreauthPaymentStatus status)
        : base($"Invalid payment preauth status: {status}.")
    {
    }
}