﻿using System.Text;
using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Ing.Builders
{
    public static class IngClientRequestBuilder
    {
        #region Constants

        private const string ContentTypeJson = "application/json";
        private const string AuthorizationHeaderKey = "Authorization";
        private const string HostHeaderKey = "Host";
        #endregion


        #region Public Methods

        public static HttpRequestMessage BuildRequestMessage(Uri url, HttpMethod method, string apiKey, object? content, IDictionary<string, string>? additionalHeaders = null)
        {
            GuardClauses.GuardClause.ArgumentIsNotNull(url, nameof(url));
            GuardClauses.GuardClause.ArgumentIsNotNull(method, nameof(method));
            GuardClauses.GuardClause.ArgumentIsNotNull(apiKey, nameof(apiKey));

            HttpRequestMessage message = InitializeHttpRequestMessage(url, method);

            ConfigureAuthorizationHeader(message, apiKey);
            
            SetContentIfApplicable(message, content);

            if (additionalHeaders == null) return message;
            
            foreach (KeyValuePair<string, string> header in additionalHeaders)
                message.Headers.Add(header.Key, header.Value);

            return message;
        }
        
        public static async Task<HttpRequestMessage> CloneRequestMessage(HttpRequestMessage original)
        {
            HttpContent? content = null;
            if (original.Content != null)
            {
                if (original.Content is StringContent stringContent)
                {
                    string contentString = await stringContent.ReadAsStringAsync();
                    content = new StringContent(contentString, Encoding.UTF8, ContentTypeJson);
                }
                else
                {
                    Stream stream = await original.Content.ReadAsStreamAsync();
                    stream.Seek(0, SeekOrigin.Begin);
                    content = new StreamContent(stream);
                }
            }

            var cloned = new HttpRequestMessage(original.Method, original.RequestUri)
            {
                Content = content,
                Version = original.Version
            };

            foreach (KeyValuePair<string, IEnumerable<string>> header in original.Headers)
            {
                cloned.Headers.TryAddWithoutValidation(header.Key, header.Value);
            }

            return cloned;
        }

       
        #endregion

        #region Private Helper Methods

        private static HttpRequestMessage InitializeHttpRequestMessage(Uri url, HttpMethod method) =>
            new(method, url) { Headers = { { HostHeaderKey, url.Host } } };

        private static void ConfigureAuthorizationHeader(HttpRequestMessage message, string apiKey) => message.Headers.Add(AuthorizationHeaderKey, $"Bearer {apiKey}");
        
        private static void SetContentIfApplicable(HttpRequestMessage message, object? content)
        {
            if (content == null || message.Method == HttpMethod.Get || message.Method == HttpMethod.Delete) return;
            
            message.Content = new StringContent(JsonConvert.SerializeObject(content), Encoding.UTF8, ContentTypeJson);
        }

        #endregion
    }
}