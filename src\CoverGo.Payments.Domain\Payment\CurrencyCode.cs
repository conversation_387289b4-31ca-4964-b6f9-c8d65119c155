using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;
using GuardClauses;

namespace CoverGo.Payments.Domain.Payment;

public class CurrencyCode : ValueObject
{
    public CurrencyCode(string value)
    {
        GuardClause.ArgumentIsNotNull(value, nameof(value));
        Value = value;
    }

    public string Value { get; }

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return Value;
    }
}