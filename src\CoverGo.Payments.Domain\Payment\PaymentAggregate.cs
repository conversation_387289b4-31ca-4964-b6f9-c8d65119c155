﻿using System.Text.Json;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Encryption;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using GuardClauses;

namespace CoverGo.Payments.Domain.Payment;

public class PaymentAggregate : AggregateRootBase<string>
{
    protected PaymentAggregate()
        : base(Guid.NewGuid().ToString())
    {
    }

    public PaymentAggregate(PaymentProvider paymentProvider, PaymentMoney money, string policyId, string? invoiceNumber,
        string payorId, DateTime? effectiveDate)
        : base(Guid.NewGuid().ToString())
    {
        PaymentProvider = paymentProvider;
        Money = money;
        PolicyId = policyId;
        InvoiceNumber = invoiceNumber;
        PayorId = payorId;
        EffectiveDate = effectiveDate;

        AddPaymentStatusHistoryItem(PaymentStatus.Created, money);
    }

    public string PolicyId { get; protected set; }

    public string? InvoiceNumber { get; protected set; }

    public string? PaymentMethod { get; protected set; }

    public string PayorId { get; protected set; }

    public PayerData? PayerData { get; protected set; }

    public string? ProviderPaymentId { get; protected set; }

    public PSPBearer? InitialBearer { get; protected set; }

    public PaymentProvider PaymentProvider { get; protected init; }

    public List<PaymentStatusHistoryItem> PaymentStatusHistoryItems { get; protected init; } = [];

    public PaymentStatusHistoryItem? PaymentStatusHistoryItem
        => PaymentStatusHistoryItems.Count != 0
            ? PaymentStatusHistoryItems.OrderBy(pshi => pshi.CreatedAtDateUtc).Last()
            : null;

    public PaymentStatus? Status { get; private set; }

    private void UpdateStatus(PaymentStatus newStatus)
        => Status = newStatus;

    public PaymentType? Type { get; private set; }

    private void UpdateType(PaymentStatus newStatus) =>
        Type = newStatus is PaymentStatus.Refunded or PaymentStatus.PartiallyRefunded
            ? PaymentType.Refund
            : PaymentType.Receipt;

    public PaymentMoney Money { get; protected init; }

    public string InternalReference { get; protected set; }

    public string? ExternalReference { get; protected set; }

    [Encrypted] public string PspSettings { get; protected set; }

    public string? DynamicFields { get; protected set; }

    public DateTime? EffectiveDate { get; private set; }

    private void UpdateEffectiveDate()
    {
        if (EffectiveDate == null || PaymentProvider != PaymentProvider.ExternalFile) 
            EffectiveDate = PaymentStatusHistoryItem?.CreatedAtDateUtc;
    }
    
    public int Attempt { get; protected set; }

    #region Methods

    public void SetPaymentMethod(string paymentMethod)
    {
        GuardClause.ArgumentIsNotNull(paymentMethod, nameof(paymentMethod));

        if (PaymentMethod != null) throw new DomainException("PaymentMethod != null");

        PaymentMethod = paymentMethod;
    }

    public void SetAttempt(int attempt) => Attempt = attempt;

    public void SetDynamicFields(string? dynamicFields)
    {
        if (DynamicFields != null) throw new DomainException("DynamicFields != null");

        DynamicFields = dynamicFields;
    }

    public void SetPayerData(PayerData payerData)
    {
        GuardClause.ArgumentIsNotNull(payerData, nameof(payerData));

        PayerData = payerData;
    }

    public void SetInitialBearer(PSPBearer pspBearer)
    {
        GuardClause.ArgumentIsNotNull(pspBearer, nameof(pspBearer));

        InitialBearer = pspBearer;
    }

    public void UpdateInitialBearer(PSPBearer pspBearer)
    {
        GuardClause.ArgumentIsNotNull(pspBearer, nameof(pspBearer));

        InitialBearer = pspBearer;
    }

    public void AddPaymentStatusHistoryItem(PaymentStatus paymentStatus, PaymentMoney paymentMoney,
        string? error = default,
        string? webhookBody = default)
    {
        if (paymentStatus == PaymentStatus.Failed)
            GuardClause.ArgumentIsNotNull(error, nameof(error));

        PaymentStatusHistoryItems.Add(new PaymentStatusHistoryItem(paymentStatus, paymentMoney, error, webhookBody));

        UpdateStatus(paymentStatus);

        UpdateType(paymentStatus);

        UpdateEffectiveDate();
    }

    public void SetInternalReference() => InternalReference = string.IsNullOrWhiteSpace(InvoiceNumber)
        ? $"{PreciseClock.UtcNow.Ticks}-{Id}"
        : $"{InvoiceNumber}_{Attempt}";

    public void SetExternalReference(string externalReference)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(externalReference, nameof(externalReference));

        ExternalReference = externalReference;
    }

    public void AssignProviderTransaction(string? providerTransactionId)
        => ProviderPaymentId = providerTransactionId;

    public void SetPspSettings(string pspSettings)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(pspSettings, nameof(pspSettings));

        if (PspSettings != null) throw new DomainException("PspSettings != null");

        PspSettings = pspSettings;
    }

    public void SetDynamicFields(JsonElement? dynamicFields) => DynamicFields = dynamicFields?.ToString();

    public string GetDescription()
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(InternalReference, nameof(InternalReference));
        return $"Payment #{InternalReference} for the service.";
    }

    public decimal CalculateRefundedAmount() =>
        Math.Min(Money.PaymentAmount, PaymentStatusHistoryItems
            .Where(s => s.Status is PaymentStatus.Refunded or PaymentStatus.PartiallyRefunded)
            .Sum(s => s.Money.PaymentAmount));

    private static bool IsSuccessStatus(PaymentStatus status)
        => status is PaymentStatus.PreliminarySucceeded
            or PaymentStatus.Succeeded
            or PaymentStatus.Pending
            or PaymentStatus.PartiallyPaid
            or PaymentStatus.OverPaid
            or PaymentStatus.Scheduled
            or PaymentStatus.Refunded
            or PaymentStatus.PartiallyRefunded
            or PaymentStatus.SuccessfullyImported;

    public bool IsSuccessful => IsSuccessStatus(Status ?? PaymentStatus.Created);

    public bool IsFailure => Status is PaymentStatus.Failed or PaymentStatus.Canceled;

    #endregion Methods
}