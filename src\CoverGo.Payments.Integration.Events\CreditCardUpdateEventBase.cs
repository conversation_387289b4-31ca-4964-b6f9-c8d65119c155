﻿using CoverGo.BuildingBlocks.MessageBus.Contracts;

namespace CoverGo.Payments.Integration.Events;

public abstract record CreditCardUpdateEventBase : IntegrationEvent
{
    public required string PolicyId { get; init; }

    public required string PayorId { get; init; }

    public required DateTime EffectiveDate { get; init; }

    public CreditCard? CreditCard { get; init; }
    
    public string? PaymentMethod { get; init; }
}