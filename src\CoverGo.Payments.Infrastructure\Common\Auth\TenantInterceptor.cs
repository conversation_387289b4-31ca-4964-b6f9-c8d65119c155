﻿using CoverGo.BuildingBlocks.MessageBus.Abstractions.Interceptors;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using CoverGo.Multitenancy;

namespace CoverGo.Payments.Infrastructure.Common.Auth;

internal class TenantInterceptor(
    ITenantProvider tenantProvider) : IMessageInterceptor
{
    public async ValueTask<T> Intercept<T>(T message, CancellationToken cancellationToken = default) where T : Message
    {
        message.TenantId = tenantProvider.TryGetCurrent(out TenantId? tenantId) ? tenantId.Value : null;
        return await ValueTask.FromResult(message);
    }
}