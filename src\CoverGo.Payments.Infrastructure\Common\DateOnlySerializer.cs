using MongoDB.Bson.Serialization;

namespace CoverGo.Payments.Infrastructure.Common;

/// <summary>
///     By default mongo serializes DateOnly as BSON Object, not BSON Date.
///     We can include this to BB.
/// </summary>
public class DateOnlySerializer : IBsonSerializer<DateOnly>
{
    private readonly IBsonSerializer<DateTime> _dateTimeSerializer;

    private DateOnlySerializer()
    {
        _dateTimeSerializer = BsonSerializer.SerializerRegistry.GetSerializer<DateTime>();
    }

    public static DateOnlySerializer Instance { get; } = new();

    public Type ValueType => typeof(DateOnly);

    public DateOnly Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        DateTime dateTime = _dateTimeSerializer.Deserialize(context, args);
        return DateOnly.FromDateTime(dateTime);
    }

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, DateOnly value)
    {
        var dateTime = value.ToDateTime(TimeOnly.MinValue, DateTimeKind.Utc);
        _dateTimeSerializer.Serialize(context, args, dateTime);
    }

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object value)
    {
        if (value is not DateOnly dateOnly) throw new ArgumentException("Invalid value type", nameof(value));

        Serialize(context, args, dateOnly);
    }

    object IBsonSerializer.Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args) =>
        Deserialize(context, args);
}