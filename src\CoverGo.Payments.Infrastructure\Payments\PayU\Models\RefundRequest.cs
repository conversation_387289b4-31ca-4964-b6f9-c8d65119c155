﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class RefundRequest
{
    [JsonProperty(PayUContainer.PropsName.PayuPaymentReference)]
    public string PayuPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.OriginalAmount)]
    public decimal OriginalAmount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Currency)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount)]
    public decimal Amount { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantRefundReference)]
    public string MerchantRefundReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Loyalty, NullValueHandling = NullValueHandling.Ignore)]
    public List<Loyalty> Loyalty { get; set; }

    [JsonProperty(PayUContainer.PropsName.InstallmentsAmount, NullValueHandling = NullValueHandling.Ignore)]
    public decimal? InstallmentsAmount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Products, NullValueHandling = NullValueHandling.Ignore)]
    public List<Product> Products { get; set; }

    [JsonProperty(PayUContainer.PropsName.UseFastRefund, NullValueHandling = NullValueHandling.Ignore)]
    public string UseFastRefund { get; set; }

    [JsonProperty(PayUContainer.PropsName.MarketplaceV1, NullValueHandling = NullValueHandling.Ignore)]
    public List<MarketplaceV1> MarketplaceV1 { get; set; }

    [JsonProperty(PayUContainer.PropsName.AdditionalDetails, NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, string> AdditionalDetails { get; set; }
}

public class Loyalty
{
    [JsonProperty(PayUContainer.PropsName.Amount)]
    public decimal Amount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Type)]
    public string Type { get; set; }
}

public class MarketplaceV1
{
    [JsonProperty(PayUContainer.PropsName.Merchant)]
    public string Merchant { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount)]
    public decimal Amount { get; set; }
}