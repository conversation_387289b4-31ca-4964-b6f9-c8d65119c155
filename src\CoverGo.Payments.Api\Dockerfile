﻿FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 80
EXPOSE 443

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
ARG GH_ACCOUNT
ARG GH_TOKEN
WORKDIR /src
COPY nuget.config .
COPY Directory.Packages.props .
COPY Directory.Build.props .
COPY Directory.Build.targets .
COPY ["src/CoverGo.Payments.Api/CoverGo.Payments.Api.csproj", "src/CoverGo.Payments.Api/"]
COPY ["src/CoverGo.Payments.Infrastructure/CoverGo.Payments.Infrastructure.csproj", "src/CoverGo.Payments.Infrastructure/"]
COPY ["src/CoverGo.Payments.Application/CoverGo.Payments.Application.csproj", "src/CoverGo.Payments.Application/"]
COPY ["src/CoverGo.Payments.Domain/CoverGo.Payments.Domain.csproj", "src/CoverGo.Payments.Domain/"]
COPY ["src/CoverGo.Payments.Integration.Events/CoverGo.Payments.Integration.Events.csproj", "src/CoverGo.Payments.Integration.Events/"]
RUN dotnet nuget update source github --username ${GH_ACCOUNT} --password ${GH_TOKEN} --store-password-in-clear-text
RUN dotnet restore "src/CoverGo.Payments.Api/CoverGo.Payments.Api.csproj"
COPY . .
WORKDIR "/src/src/CoverGo.Payments.Api"
RUN dotnet build "CoverGo.Payments.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "CoverGo.Payments.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "CoverGo.Payments.Api.dll"]
