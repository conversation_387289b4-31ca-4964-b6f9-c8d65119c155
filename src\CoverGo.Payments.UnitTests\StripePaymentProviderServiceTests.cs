using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Payments.Stripe;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Stripe;
using Stripe.Checkout;
using Address = CoverGo.Payments.Domain.Payment.Address;
using PaymentMoney = CoverGo.Payments.Domain.Payment.PaymentMoney;

namespace CoverGo.Payments.UnitTests;

public class StripePaymentProviderServiceTests
{
    private const string PspSettingsJson =
        "{\"ApiKey\":\"sk_test_key\",\"SuccessUrl\":\"http://localhost/\",\"WebhookSecret\":\"whsec_test\"}";

    private readonly Mock<IStripeClientFactory> _stripeClientFactoryMock;
    private readonly StripePaymentProviderService _service;

    public StripePaymentProviderServiceTests()
    {
        _stripeClientFactoryMock = new Mock<IStripeClientFactory>();
        Mock<ILogger<StripePaymentProviderService>> loggerMock = new();
        Mock<IHttpContextAccessor> httpContextAccessorMock = new();
        _service = new StripePaymentProviderService(_stripeClientFactoryMock.Object, loggerMock.Object,
            httpContextAccessorMock.Object);
    }

    [Fact]
    public async Task
        GIVEN_ValidPayment_WHEN_GetPreProcessRedirectUrlAsync_THEN_RequestsStripeToCreateCheckoutSession_AND_ReturnsRedirectUrlOutput()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var session = new Session { Id = "session_id", Url = "https://example.com" };

        mockStripeClient.Setup(client => client.RequestAsync<Session>(HttpMethod.Post, $"/v1/checkout/sessions",
                It.Is<SessionCreateOptions>(x => x.Mode == "payment"), It.IsAny<RequestOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        // Act
        RedirectUrlOutput result = await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields: null);

        // Assert
        result.Should().NotBeNull();
        result.RedirectUrl.Should().Be(new Uri("https://example.com"));
        result.Data.Should().ContainKey("sessionId").WhoseValue.Should().Be("session_id");
    }

    [Fact]
    public async Task
        GIVEN_ValidUpdatePayment_WHEN_GetPreProcessRedirectUrlAsync_THEN_RequestsStripeToCreateCheckoutSession_AND_ReturnsRedirectUrlOutput()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 0, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null, isUpdate: true);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var session = new Session { Id = "session_id", Url = "https://example.com" };

        mockStripeClient.Setup(client => client.RequestAsync<Session>(HttpMethod.Post, $"/v1/checkout/sessions",
                It.Is<SessionCreateOptions>(x => x.Mode == "setup"), It.IsAny<RequestOptions>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(session);

        // Act
        RedirectUrlOutput result = await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields: null);

        // Assert
        result.Should().NotBeNull();
        result.RedirectUrl.Should().Be(new Uri("https://example.com"));
        result.Data.Should().ContainKey("sessionId").WhoseValue.Should().Be("session_id");
    }

    [Fact]
    public async Task GIVEN_InvalidPayment_WHEN_CreateCheckoutSessionAsync_THEN_ThrowsException()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe,
            new PaymentMoney("840", "USD", 1000, 2), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(),
            Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        mockStripeClient.Setup(client => client.RequestAsync<Session>(HttpMethod.Post, $"/v1/checkout/sessions",
                It.IsAny<SessionCreateOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .Throws<StripeException>();

        // Act
        Func<Task> act = async () => await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields: null);

        // Assert
        await act.Should().ThrowAsync<StripeException>();
    }

    [Fact]
    public async Task
        GIVEN_ValidCapturePayment_WHEN_CapturePaymentAsync_THEN_RequestsStripeToCapturePaymentIntent_AND_UpdatesPaymentStatus()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string paymentIntentId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(paymentIntentId);

        var capturePayment = new CapturePaymentAggregate(payment);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var paymentIntent = new PaymentIntent { Id = paymentIntentId, Status = "succeeded" };

        mockStripeClient.Setup(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/capture",
                It.IsAny<PaymentIntentCaptureOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentIntent);

        // Act
        CapturePaymentAggregate result =
            await _service.CapturePaymentAsync(capturePayment, paymentIntentId, CancellationToken.None);

        // Assert
        mockStripeClient.Verify(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/capture",
                It.IsAny<PaymentIntentCaptureOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()),
            Times.Once);

        result.Should().NotBeNull();
        result.Money.Should().Be(payment.Money);
        result.Status.Should().Be(PaymentStatus.Succeeded);
    }

    [Fact]
    public async Task
        GIVEN_ValidPayment_WHEN_CancelPaymentAsync_THEN_RequestsStripeToCancelPaymentIntent_AND_UpdatesPaymentStatus()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string paymentIntentId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(paymentIntentId);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var paymentIntent = new PaymentIntent { Id = paymentIntentId, Status = "succeeded" };

        mockStripeClient.Setup(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/cancel",
                It.IsAny<PaymentIntentCancelOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentIntent);

        // Act
        await _service.CancelPreauthPaymentAsync(payment, CancellationToken.None);

        // Assert
        mockStripeClient.Verify(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/cancel",
                It.IsAny<PaymentIntentCancelOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()),
            Times.Once);

        payment.Status.Should().Be(PaymentStatus.Canceled);
    }

    [Fact]
    public async Task
        GIVEN_ValidPayment_WHEN_FailPaymentAsync_THEN_RequestsStripeToCancelPaymentIntent_AND_UpdatesPaymentStatus()
    {
        // Arrange
        var payment = new PaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string paymentIntentId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(paymentIntentId);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var paymentIntent = new PaymentIntent { Id = paymentIntentId, Status = "succeeded" };

        mockStripeClient.Setup(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/cancel",
                It.IsAny<PaymentIntentCancelOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentIntent);

        // Act
        await _service.FailPaymentAsync(payment, CancellationToken.None);

        // Assert
        mockStripeClient.Verify(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post,
                $"/v1/payment_intents/{paymentIntentId}/cancel",
                It.IsAny<PaymentIntentCancelOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()),
            Times.Once);

        payment.Status.Should().Be(PaymentStatus.Failed);
    }

    [Fact]
    public async Task
        GIVEN_ValidPayment_AND_ValidRefund_WHEN_RefundAsync_THEN_RequestsStripeToCreateRefund_AND_UpdatesPaymentStatus()
    {
        // Arrange
        var payment = new PaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string paymentIntentId = Guid.NewGuid().ToString();
        string chargeId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(paymentIntentId);
        payment.AssignProviderTransaction(chargeId);

        var paymentRefund = new RefundAggregate(payment.Id, RefundStatus.Failed,
            new PaymentMoney("840", "USD", 500, 2), null, payment.ProviderPaymentId);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var refund = new Refund { Id = paymentIntentId, Status = "succeeded" };

        mockStripeClient.Setup(client => client.RequestAsync<Refund>(HttpMethod.Post, $"/v1/refunds",
                It.Is<RefundCreateOptions>(o =>
                    o.PaymentIntent == paymentRefund.ProviderPaymentId && o.Amount ==
                    CurrencyHelper.ConvertUnitsToSubunits(paymentRefund.Money.PaymentAmount)),
                It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(refund);

        // Act
        await _service.RefundAsync(payment, paymentRefund, CancellationToken.None);

        // Assert
        mockStripeClient.Verify(client => client.RequestAsync<Refund>(HttpMethod.Post, $"/v1/refunds",
            It.Is<RefundCreateOptions>(o =>
                o.PaymentIntent == paymentRefund.ProviderPaymentId && o.Amount ==
                CurrencyHelper.ConvertUnitsToSubunits(paymentRefund.Money.PaymentAmount)), It.IsAny<RequestOptions>(),
            It.IsAny<CancellationToken>()), Times.Once);

        paymentRefund.Status.Should().Be(RefundStatus.Succeeded);
    }

    [Fact]
    public async Task GIVEN_ValidPayment_WHEN_FinalizePaymentAsync_THEN_UpdatesPayerData_AND_UpdatesPspBearerToken()
    {
        // Arrange
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string paymentIntentId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(paymentIntentId);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var paymentIntent = new PaymentIntent
        {
            Id = paymentIntentId,
            Status = "succeeded",
            CustomerId = Guid.NewGuid().ToString(),
            PaymentMethodId = Guid.NewGuid().ToString(),
            LatestChargeId = Guid.NewGuid().ToString()
        };
        mockStripeClient.Setup(client => client.RequestAsync<PaymentIntent>(HttpMethod.Get,
                $"/v1/payment_intents/{paymentIntentId}",
                It.IsAny<PaymentIntentGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentIntent);

        var customer = new Customer
        {
            Id = paymentIntent.CustomerId,
            Email = "<EMAIL>",
            Name = "FirstName LastName",
            Phone = "**********",
            Address = new Stripe.Address
            {
                Line1 = "testLine1",
                Line2 = "testLine2",
                City = "testCity",
                State = "testState",
                PostalCode = "testPostalCode",
                Country = "testCountry"
            }
        };
        mockStripeClient.Setup(client => client.RequestAsync<Customer>(HttpMethod.Get,
                $"/v1/customers/{paymentIntent.CustomerId}",
                It.IsAny<CustomerGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customer);

        var paymentMethod = new PaymentMethod { Id = Guid.NewGuid().ToString() };
        mockStripeClient.Setup(client => client.RequestAsync<PaymentMethod>(HttpMethod.Get,
                $"/v1/payment_methods/{paymentIntent.PaymentMethodId}",
                It.IsAny<PaymentMethodGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentMethod);

        // Act
        await _service.FinalizePaymentAsync(payment, null, null, CancellationToken.None);

        // Assert
        payment.InitialBearer.Should().BeEquivalentTo(new PSPBearer { Token = paymentMethod.Id });
        payment.PayerData.Should().BeEquivalentTo(new PayerData(
            language: null,
            emailAddress: "<EMAIL>",
            address: new Address(
                "testLine1",
                "testLine2",
                string.Empty,
                string.Empty,
                "testPostalCode",
                "testCity",
                "testState",
                "testCountry"),
            lastName: "LastName",
            firstName: "FirstName",
            externalCustomerId: customer.Id,
            companyName: string.Empty,
            phoneNumber: "**********"));
    }

    [Fact]
    public async Task
        GIVEN_ValidUpdatePayment_WHEN_FinalizePaymentAsync_THEN_UpdatesPayerData_AND_UpdatesPspBearerToken()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null, isUpdate: true);
        payment.SetPspSettings(PspSettingsJson);
        payment.SetInternalReference();

        string setupIntentId = Guid.NewGuid().ToString();
        payment.AssignProviderTransaction(setupIntentId);

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var setupIntent = new SetupIntent
        {
            Id = setupIntentId,
            Status = "succeeded",
            CustomerId = Guid.NewGuid().ToString(),
            PaymentMethodId = Guid.NewGuid().ToString(),
        };
        mockStripeClient.Setup(client => client.RequestAsync<SetupIntent>(HttpMethod.Get,
                $"/v1/setup_intents/{setupIntentId}",
                It.IsAny<SetupIntentGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(setupIntent);

        var customer = new Customer
        {
            Id = setupIntent.CustomerId,
            Email = "<EMAIL>",
            Name = "FirstName LastName",
            Phone = "**********",
            Address = new Stripe.Address
            {
                Line1 = "testLine1",
                Line2 = "testLine2",
                City = "testCity",
                State = "testState",
                PostalCode = "testPostalCode",
                Country = "testCountry"
            }
        };
        mockStripeClient.Setup(client => client.RequestAsync<Customer>(HttpMethod.Get,
                $"/v1/customers/{setupIntent.CustomerId}",
                It.IsAny<CustomerGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(customer);

        var paymentMethod = new PaymentMethod { Id = Guid.NewGuid().ToString() };
        mockStripeClient.Setup(client => client.RequestAsync<PaymentMethod>(HttpMethod.Get,
                $"/v1/payment_methods/{setupIntent.PaymentMethodId}",
                It.IsAny<PaymentMethodGetOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentMethod);

        await _service.FinalizePaymentAsync(payment, null, null, CancellationToken.None);

        payment.InitialBearer.Should().BeEquivalentTo(new PSPBearer { Token = paymentMethod.Id });
        payment.PayerData.Should().BeEquivalentTo(new PayerData(
            language: null,
            emailAddress: "<EMAIL>",
            address: new Address(
                "testLine1",
                "testLine2",
                string.Empty,
                string.Empty,
                "testPostalCode",
                "testCity",
                "testState",
                "testCountry"),
            lastName: "LastName",
            firstName: "FirstName",
            externalCustomerId: customer.Id,
            companyName: string.Empty,
            phoneNumber: "**********"));
    }

    [Fact]
    public async Task
        GIVEN_ValidRecurringPayment_WHEN_RecurringPaymentAsync_THEN_UpdatesPayerData_AND_UpdatesPspBearerToken()
    {
        // Arrange
        var recurringPayment =
            new RecurringPaymentAggregate(PaymentProvider.Stripe, new PaymentMoney("840", "USD", 1000, 2),
                Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        recurringPayment.SetPspSettings(PspSettingsJson);
        recurringPayment.SetInitialBearer(new PSPBearer { Token = "test" });
        recurringPayment.SetPayerData(new PayerData(
            language: null,
            emailAddress: "<EMAIL>",
            address: new Address(
                "testLine1",
                "testLine2",
                string.Empty,
                string.Empty,
                "testPostalCode",
                "testCity",
                "testState",
                "testCountry"),
            lastName: "LastName",
            firstName: "FirstName",
            externalCustomerId: Guid.NewGuid().ToString(),
            companyName: string.Empty,
            phoneNumber: "**********"));

        var mockStripeClient = new Mock<IStripeClient>();
        _stripeClientFactoryMock.Setup(x => x.CreateClient(It.IsAny<string>())).Returns(mockStripeClient.Object);

        var paymentIntent = new PaymentIntent
        {
            Id = Guid.NewGuid().ToString(),
            Status = "succeeded",
            CustomerId = Guid.NewGuid().ToString(),
            PaymentMethodId = Guid.NewGuid().ToString(),
            LatestChargeId = Guid.NewGuid().ToString()
        };
        mockStripeClient.Setup(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post, $"/v1/payment_intents",
                It.IsAny<PaymentIntentCreateOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(paymentIntent);

        // Act
        await _service.RecurringPaymentAsync(recurringPayment, CancellationToken.None);

        mockStripeClient.Verify(client => client.RequestAsync<PaymentIntent>(HttpMethod.Post, $"/v1/payment_intents",
                It.IsAny<PaymentIntentCreateOptions>(), It.IsAny<RequestOptions>(), It.IsAny<CancellationToken>()),
            Times.Once);

        // Assert
        recurringPayment.ProviderPaymentId.Should().BeEquivalentTo(paymentIntent.Id);
        recurringPayment.Status.Should().Be(PaymentStatus.Succeeded);
    }
}