using CoverGo.BuildingBlocks.Application.Core.Ports;
using Microsoft.AspNetCore.Http;

namespace CoverGo.Payments.Infrastructure.Common;

public class UserContextProvider : IUserContextProvider
{
    private readonly IHttpContextAccessor _httpContextAccessor;

    public UserContextProvider(IHttpContextAccessor httpContextAccessor)
    {
        _httpContextAccessor = httpContextAccessor;
    }

    public string GetUserId()
    {
        var httpContext = _httpContextAccessor.HttpContext;
        if (httpContext?.User?.Identity?.IsAuthenticated != true)
        {
            return "undefined";
        }
        
        var userIdClaim = httpContext.User.FindFirst("sub") ?? 
                         httpContext.User.FindFirst("user_id") ?? 
                         httpContext.User.FindFirst("nameidentifier");

        return userIdClaim?.Value ?? "undefined";
    }
}