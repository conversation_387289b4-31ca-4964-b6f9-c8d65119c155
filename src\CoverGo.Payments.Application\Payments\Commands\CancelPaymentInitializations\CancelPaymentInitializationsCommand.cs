﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;

public record CancelPaymentInitializationsCommand(
    List<string> InitializationTokens,
    PaymentStatus? CancellationStatus = null,
    string? CancellationReason = null
) : ICommand<PaymentInitializationsCancellationResultDto>;
