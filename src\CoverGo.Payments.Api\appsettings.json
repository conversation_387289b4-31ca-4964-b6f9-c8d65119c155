{"Serilog": {"Using": ["Serilog.Sinks.Console"], "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning"}}, "Enrich": ["FromLogContext", "WithThreadId", "WithMachineName", "WithSpan"], "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>", "Args": {"formatter": "Serilog.Formatting.Compact.CompactJsonFormatter, Serilog.Formatting.Compact"}}]}, "AllowedHosts": "*", "serviceUrls": {"auth": "http://covergo-auth:8080"}, "ConnectionStrings": {"redis": "covergo-redis-master:6379,abortConnect=false", "gateway": "http://covergo-gateway:8080/", "auth": "http://covergo-auth:8080"}, "ObservabilityConfiguration": {"CollectorUrl": "http://grafana-agent-traces.monitoring:4317", "ServiceName": "payments-service", "Timeout": 1000}, "MongoDatabaseConfiguration": {"DatabaseName": "payments", "UseTransactions": true}, "GraphQL": {"IncludeExceptionDetails": false}, "GraphQLStitching": {"Enabled": true, "SchemaName": "payments", "Redis": {"Publish": true, "ConfigurationName": "GatewayV2"}}, "PubSubConfiguration": {"Environment": "", "SiloModel": {"Tenants": []}}, "FeatureManagement": {"ExternalFileInvoiceNumberValidationEnabled": {"EnabledFor": [{"Name": "Tenants", "Parameters": {"Tenants": ["gms", "gms_dev", "gms_prod", "covergo"]}}]}, "GraphQLReadinessCheck": false}, "ReadinessOptions": {"GraphQlV2Endpoint": "http://covergo-api-gateway:8080/graphql/"}, "CheckDI": false}