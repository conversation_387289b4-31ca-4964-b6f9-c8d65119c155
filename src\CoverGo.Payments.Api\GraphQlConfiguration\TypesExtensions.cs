using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Domain.BankAccount;

namespace CoverGo.Payments.Api.GraphQlConfiguration;

public sealed class PaymentAggregateType : ObjectType<PaymentAggregate>
{
    protected override void Configure(IObjectTypeDescriptor<PaymentAggregate> descriptor)
    {
        descriptor.Field(x => x.EntityAuditInfo).Ignore();
        descriptor.Field(x => x.DomainEvents).Ignore();
        descriptor.Field(x => x.DynamicFields).Ignore();
        descriptor.Field(x => x.PspSettings).Ignore();
        descriptor.Field(x => x.PaymentStatusHistoryItem).Ignore();
        descriptor.Field(x => x.PaymentStatusHistoryItems).Ignore();
        descriptor.Field(x => x.Attempt).Ignore();
        descriptor
            .Field(f => f.InitialBearer)
            .Type<PspBearerUnionType>()
            .Resolve((ctx, _) =>
            {
                PSPBearer? initialBearer = ctx.Parent<PaymentAggregate>().InitialBearer;
                if (initialBearer is PSPBearerPseudoCC pseudoCc)
                    return pseudoCc;

                return initialBearer;
            });
        descriptor
            .Field(f => f.PaymentMethod)
            .Resolve((ctx, _) =>
            {
                string? paymentMethod = ctx.Parent<PaymentAggregate>().PaymentMethod;
                return paymentMethod ?? "DIGITAL_PAYMENT";
            });
        descriptor
            .Field("reason")
            .Type<StringType>()
            .Resolve((ctx, _) => ctx.Parent<PaymentAggregate>().PaymentStatusHistoryItem?.Error);
        descriptor
            .Field("transactionType")
            .Type<StringType>()
            .Resolve((ctx, _) => ctx.Parent<PaymentAggregate>().Type);
    }
}

public sealed class PspSettingsAggregateType : ObjectType<PspSettingsAggregate>
{
    protected override void Configure(IObjectTypeDescriptor<PspSettingsAggregate> descriptor)
    {
        descriptor.Field(x => x.EntityAuditInfo).Ignore();
        descriptor.Field(x => x.DomainEvents).Ignore();
    }
}

public sealed class RefundAggregateType : ObjectType<RefundAggregate>
{
    protected override void Configure(IObjectTypeDescriptor<RefundAggregate> descriptor)
    {
        descriptor.Field(x => x.EntityAuditInfo).Ignore();
        descriptor.Field(x => x.DomainEvents).Ignore();
    }
}

public sealed class BankAccountAggregateType : ObjectType<BankAccount>
{
    protected override void Configure(IObjectTypeDescriptor<BankAccount> descriptor)
    {
        descriptor.Field(x => x.EntityAuditInfo).Ignore();
        descriptor.Field(x => x.DomainEvents).Ignore();
    }
}

public sealed class PspBearerUnionType : UnionType
{
    protected override void Configure(IUnionTypeDescriptor descriptor)
    {
        descriptor.Type<PspBearerType>();
        descriptor.Type<PspBearerPseudoCcType>();
    }
}

public sealed class PspBearerType : ObjectType<PSPBearer>
{
    protected override void Configure(IObjectTypeDescriptor<PSPBearer> descriptor)
    {
        descriptor.Field("typeName")
            .Resolve(_ => "PSPBearer")
            .Type<StringType>();

        descriptor.Field(x => x.Token).Type<StringType>().Ignore();
    }
}

public sealed class PspBearerPseudoCcType : ObjectType<PSPBearerPseudoCC>
{
    protected override void Configure(IObjectTypeDescriptor<PSPBearerPseudoCC> descriptor)
    {
        descriptor.Field(x => x.Token).Type<StringType>().Ignore();
        descriptor.Field(x => x.PseudoCardPan).Type<StringType>();
        descriptor.Field(x => x.CardType).Type<StringType>();
        descriptor.Field(x => x.Country).Type<StringType>();
        descriptor.Field(x => x.ExpiryMonth).Type<IntType>();
        descriptor.Field(x => x.ExpiryYear).Type<IntType>();
        descriptor.Field(x => x.Holder).Type<StringType>();
        descriptor.Field(x => x.TruncatedCardPan).Type<StringType>();
        descriptor.Field(x => x.OrderId).Type<StringType>().Ignore();
        descriptor.Field(x => x.IssuerId).Type<StringType>().Ignore();
    }
}