﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;

public class CancelPaymentInitializationsCommandHandler(IPaymentService paymentService, ILogger<CancelPaymentInitializationsCommandHandler> logger)
    : ICommandHandler<CancelPaymentInitializationsCommand, PaymentInitializationsCancellationResultDto>
{
    public async Task<PaymentInitializationsCancellationResultDto> Handle(CancelPaymentInitializationsCommand command, CancellationToken cancellationToken)
    {
        logger.LogInformation("CancelPaymentInitializationsCommandHandler.Handle: Starting payment initializations cancellation. TokensCount: {TokensCount}, CancellationStatus: {CancellationStatus}, Reason: {CancellationReason}",
            command.InitializationTokens.Count,
            command.CancellationStatus?.ToString() ?? "Canceled",
            command.CancellationReason ?? "No reason provided");

        try
        {
            await paymentService.CancelPaymentInitializationsAsync(
                command.InitializationTokens, 
                command.CancellationStatus, 
                command.CancellationReason, 
                cancellationToken);

            logger.LogInformation("CancelPaymentInitializationsCommandHandler.Handle: Payment initializations cancellation completed. TokensCount: {TokensCount}",
                command.InitializationTokens.Count);

            return new PaymentInitializationsCancellationResultDto { IsSuccessful = true };
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "CancelPaymentInitializationsCommandHandler.Handle: Unexpected error during payment initializations cancellation. TokensCount: {TokensCount}",
                command.InitializationTokens.Count);
            throw;
        }
    }
}
