﻿using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.IntegrationTests.Encryption.TestData;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Payments.IntegrationTests.Encryption
{
    public class MongoDbEncryptedFieldTests : IClassFixture<ServiceProviderFixture>
    {
        private readonly ServiceProvider _serviceProvider;
        public MongoDbEncryptedFieldTests(ServiceProviderFixture fixture)
        {
            _serviceProvider = fixture.ServiceProvider;
        }

        [Fact]
        public async Task Given_SimpleAggregate_When_InsertAsync_Then_AggregateIsEncrypted()
        {
            // Arrange
            var repository = _serviceProvider.GetRequiredService<IRepository<SimpleAggregate, string>>();
            var now = DateTime.UtcNow;
            var testEntity = new SimpleAggregate(Guid.NewGuid().ToString())
            {
                SensitiveString = "Sensitive String",
                SensitiveDateTime = now,
                SensitiveInt = 123,
                SensitiveDecimal = 123.45m,
                SensitiveBool = true,
                SensitiveFloat = 123.45f,
                SensitiveLong = 1234567890L,
                NormalString = "Normal String"
            };

            // Act
            await repository.InsertAsync(testEntity, CancellationToken.None);

            // Assert
            var retrievedEntity = await repository.FindByIdAsync(testEntity.Id, CancellationToken.None);
            retrievedEntity.Should().NotBeNull();
            retrievedEntity!.SensitiveString.Should().Be("Sensitive String");
            retrievedEntity.SensitiveDateTime.Should().Be(now);
            retrievedEntity.SensitiveInt.Should().Be(123);
            retrievedEntity.SensitiveDecimal.Should().Be(123.45m);
            retrievedEntity.SensitiveBool.Should().BeTrue();
            retrievedEntity.SensitiveFloat.Should().Be(123.45f);
            retrievedEntity.SensitiveLong.Should().Be(1234567890L);
            retrievedEntity.NormalString.Should().Be("Normal String");
        }

        [Fact]
        public async Task Given_ClassAggregateWithInnerClassMarkedEncrypted_When_InsertAsync_Then_AggregateIsEncrypted()
        {
            // Arrange
            var repository = _serviceProvider.GetRequiredService<IRepository<Class1Aggregate, string>>();
            var testEntity = new Class1Aggregate(Guid.NewGuid().ToString())
            {
                SensitiveClass = new Class1
                {
                    NormalString = "Sensitive String",
                    NormalInt = 123
                },
                NormalClass = new Class1
                {
                    NormalString = "Normal String",
                    NormalInt = 456
                },
            };

            // Act
            await repository.InsertAsync(testEntity, CancellationToken.None);

            // Assert
            var retrievedEntity = await repository.FindByIdAsync(testEntity.Id, CancellationToken.None);
            retrievedEntity.Should().NotBeNull();
            retrievedEntity!.SensitiveClass!.NormalString.Should().Be("Sensitive String");
            retrievedEntity.SensitiveClass.NormalInt.Should().Be(123);
            retrievedEntity.NormalClass!.NormalString.Should().Be("Normal String");
            retrievedEntity.NormalClass.NormalInt.Should().Be(456);
        }

        [Fact]
        public async Task Given_ClassAggregateWithInnerPropertiesMarkedEncrypted_When_InsertAsync_Then_AggregateIsEncrypted()
        {
            // Arrange
            var repository = _serviceProvider.GetRequiredService<IRepository<Class2Aggregate, string>>();
            var now = DateTime.UtcNow;
            var testEntity = new Class2Aggregate(Guid.NewGuid().ToString())
            {
                SensitiveClass = new Class2
                {
                    SensitiveString = "Sensitive String",
                    SensitiveDateTime = now,
                    NormalInt = 123
                }
            };

            // Act
            await repository.InsertAsync(testEntity, CancellationToken.None);

            // Assert
            var retrievedEntity = await repository.FindByIdAsync(testEntity.Id, CancellationToken.None);
            retrievedEntity.Should().NotBeNull();
            retrievedEntity!.SensitiveClass!.SensitiveString.Should().Be("Sensitive String");
            retrievedEntity.SensitiveClass.SensitiveDateTime.Should().Be(now);
            retrievedEntity.SensitiveClass.NormalInt.Should().Be(123);
        }

        [Fact]
        public async Task Given_ListAggregateWithInnerListMarkedEncrypted_When_InsertAsync_Then_AggregateIsEncrypted()
        {
            // Arrange
            var repository = _serviceProvider.GetRequiredService<IRepository<List1Aggregate, string>>();
            var testEntity = new List1Aggregate(Guid.NewGuid().ToString())
            {
                SensitiveListString = new List<string> { "Sensitive String 1", "Sensitive String 2" },
                SensitiveListObject = new List<Class1>
                {
                    new Class1 { NormalString = "String 1", NormalInt = 123 },
                    new Class1 { NormalString = "String 2", NormalInt = 456 }
                },
                NormalListObject = new List<Class1>
                {
                    new Class1 { NormalString = "String 3", NormalInt = 789 },
                    new Class1 { NormalString = "String 4", NormalInt = 101112 }
                }
            };

            // Act
            await repository.InsertAsync(testEntity, CancellationToken.None);

            // Assert
            var retrievedEntity = await repository.FindByIdAsync(testEntity.Id, CancellationToken.None);
            retrievedEntity.Should().NotBeNull();
            retrievedEntity!.SensitiveListString![0].Should().Be("Sensitive String 1");
            retrievedEntity.SensitiveListString[1].Should().Be("Sensitive String 2");
            retrievedEntity.SensitiveListObject![0].NormalString.Should().Be("String 1");
            retrievedEntity.SensitiveListObject[0].NormalInt.Should().Be(123);
            retrievedEntity.SensitiveListObject[1].NormalString.Should().Be("String 2");
            retrievedEntity.SensitiveListObject[1].NormalInt.Should().Be(456);
        }

        [Fact]
        public async Task Given_ListAggregateWithInnerPropertiesMarkedEncrypted_When_InsertAsync_Then_AggregateIsEncrypted()
        {
            // Arrange
            var repository = _serviceProvider.GetRequiredService<IRepository<List2Aggregate, string>>();
            var now = DateTime.UtcNow;
            var testEntity = new List2Aggregate(Guid.NewGuid().ToString())
            {
                SensitiveListObject = new List<Class2>
                {
                    new Class2 { SensitiveString = "Sensitive String 1", SensitiveDateTime = now, NormalInt = 123 },
                    new Class2 { SensitiveString = "Sensitive String 2", SensitiveDateTime = now, NormalInt = 456 }
                },
            };

            // Act
            await repository.InsertAsync(testEntity, CancellationToken.None);

            // Assert
            var retrievedEntity = await repository.FindByIdAsync(testEntity.Id, CancellationToken.None);
            retrievedEntity.Should().NotBeNull();
            retrievedEntity!.SensitiveListObject![0].SensitiveString.Should().Be("Sensitive String 1");
            retrievedEntity.SensitiveListObject[0].SensitiveDateTime.Should().Be(now);
            retrievedEntity.SensitiveListObject[0].NormalInt.Should().Be(123);
            retrievedEntity.SensitiveListObject[1].SensitiveString.Should().Be("Sensitive String 2");
            retrievedEntity.SensitiveListObject[1].SensitiveDateTime.Should().Be(now);
            retrievedEntity.SensitiveListObject[1].NormalInt.Should().Be(456);
        }
    }
}
