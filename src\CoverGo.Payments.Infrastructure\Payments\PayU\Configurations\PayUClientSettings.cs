﻿namespace CoverGo.Payments.Infrastructure.Payments.PayU.Configurations
{
    public class PayUClientSettings
    {
        public PayUClientSettings(string url, string apiVersion, string merchantCode, string secretKey,
            string factoryClientName)
        {
            AssignMandatoryProperties(url, apiVersion, merchantCode, secretKey);
            FactoryClientName = factoryClientName;
        }

        public string Url { get; private set; }
        public string ApiVersion { get; private set; }
        public string MerchantCode { get; private set; }
        public string SecretKey { get; private set; }
        public string FactoryClientName { get; }

        private void AssignMandatoryProperties(string url, string apiVersion, string merchantCode, string secretKey)
        {
            Valid(url, apiVersion, merchantCode, secretKey);

            Url = url;
            ApiVersion = apiVersion;
            MerchantCode = merchantCode;
            SecretKey = secretKey;
        }

        private static void Valid(string url, string apiVersion, string merchantCode, string secretKey)
        {
            if (string.IsNullOrEmpty(url) ||
                string.IsNullOrEmpty(apiVersion) ||
                string.IsNullOrEmpty(merchantCode) ||
                string.IsNullOrEmpty(secretKey))
                throw new ArgumentException("Some of settings properties are null or empty");
        }
    }
}