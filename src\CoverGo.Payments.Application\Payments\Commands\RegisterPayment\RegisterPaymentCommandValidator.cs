﻿using CoverGo.FeatureManagement;
using CoverGo.Multitenancy;
using CoverGo.Payments.Domain.Payment;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.RegisterPayment
{
    public class RegisterPaymentCommandValidator : AbstractValidator<RegisterPaymentCommand>
    {
        public RegisterPaymentCommandValidator(
            ILogger<RegisterPaymentCommandValidator> logger, ITenantProvider tenantProvider,
            IMultiTenantFeatureManager featureManager)
        {
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);

            if (tenantProvider.TryGetCurrent(out TenantId? tenantId))
                RuleFor(pc => pc.InvoiceNumber)
                    .NotEmpty()
                    .WithMessage("No invoice number found.")
                    .WhenAsync(async (_, _) =>
                        await featureManager.IsEnabled("ExternalFileInvoiceNumberValidationEnabled", tenantId.Value));

            When(pc => pc.PaymentProvider == PaymentProvider.ExternalFile, () =>
            {
                RuleFor(pc => pc.PaymentMethod).NotEmpty().WithMessage("No payment method found.");
            });
            
            RuleFor(pc => pc.CurrencyCode).NotEmpty().WithMessage("No currency code found.");
            RuleFor(pc => pc.Amount).NotNull().GreaterThan(decimal.Zero)
                .WithMessage("Amount should be greater than 0.");
            RuleFor(pc => pc.PayorId).NotEmpty().WithMessage("No payor Id found.");
            RuleFor(pc => pc.PolicyId).NotEmpty().WithMessage("No policy Id found.");
        }
    }
}