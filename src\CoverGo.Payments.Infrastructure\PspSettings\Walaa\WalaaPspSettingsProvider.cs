﻿using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;

namespace CoverGo.Payments.Infrastructure.PspSettings.Walaa;

public class WalaaPspSettingsProvider(IRepository<PspSettingsAggregate, string> pspSettingsRepository) : BasePspSettingsProvider(pspSettingsRepository)
{
    public override PaymentProvider Type => PaymentProvider.Walaa;
}
