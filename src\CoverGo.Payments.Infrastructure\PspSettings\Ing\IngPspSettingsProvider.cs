﻿using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;

namespace CoverGo.Payments.Infrastructure.PspSettings.Ing;

public class IngPspSettingsProvider(IRepository<PspSettingsAggregate, string> pspSettingsRepository) : BasePspSettingsProvider(pspSettingsRepository)
{
    public override PaymentProvider Type => PaymentProvider.Ing;
}