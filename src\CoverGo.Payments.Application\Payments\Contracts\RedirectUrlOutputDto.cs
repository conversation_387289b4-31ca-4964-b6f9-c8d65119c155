﻿using System.Collections.ObjectModel;
using GuardClauses;

namespace CoverGo.Payments.Application.Payments.Contracts;

public class RedirectUrlOutputDto
{
    protected RedirectUrlOutputDto()
    {
            
    }

    protected RedirectUrlOutputDto(Uri? redirectUrl)
    {
        RedirectUrl = redirectUrl;
    }

    protected RedirectUrlOutputDto(Uri? redirectUrl, IDictionary<string, string> data)
        : this(redirectUrl)
    {
        GuardClause.ArgumentIsNotNull(data, nameof(data));

        Data = new ReadOnlyDictionary<string, string>(data);
    }

    public Uri? RedirectUrl { get; }
        
    public IReadOnlyDictionary<string, string>? Data { get; }
}