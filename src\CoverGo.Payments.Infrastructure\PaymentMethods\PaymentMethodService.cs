using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.PaymentMethods.Services;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PaymentMethod;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.PaymentMethods;

public class PaymentMethodService : IPaymentMethodService
{
    private readonly IRepository<PayorPaymentMethodAggregate, string> _paymentMethodRepository;
    private readonly ILogger<PaymentMethodService> _logger;

    public PaymentMethodService(
        IRepository<PayorPaymentMethodAggregate, string> paymentMethodRepository,
        ILogger<PaymentMethodService> logger)
    {
        _paymentMethodRepository = paymentMethodRepository;
        _logger = logger;
    }

    public async Task CreatePayorPaymentMethodAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
    {
        try
        {
            var cardData = CreateCardDataFromInitialBearer(payment.InitialBearer);

            var paymentMethod = new PayorPaymentMethodAggregate(
                payment.PaymentProvider,
                payment.PayorId,
                payment.PayerData,
                cardData);

            await _paymentMethodRepository.InsertAsync(paymentMethod, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "PaymentMethodService.CreatePayorPaymentMethodAsync: Error creating PayorPaymentMethod. PaymentId: {PaymentId}, PayorId: {PayorId}",
                payment.Id, payment.PayorId);
            throw;
        }
    }

    private static CardData? CreateCardDataFromInitialBearer(PSPBearer initialBearer)
    {
        if (initialBearer is PSPBearerPseudoCC pseudoCC)
        {
            return new CardData(
                pseudoCardPan: pseudoCC.PseudoCardPan,
                truncatedCardPan: pseudoCC.TruncatedCardPan,
                expiryMonth: pseudoCC.ExpiryMonth,
                expiryYear: pseudoCC.ExpiryYear,
                cardType: pseudoCC.CardType,
                country: pseudoCC.Country,
                holder: pseudoCC.Holder);
        }

        return null;
    }
}
