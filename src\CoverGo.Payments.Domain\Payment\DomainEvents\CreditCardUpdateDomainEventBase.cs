﻿using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.Payments.Integration.Events;

namespace CoverGo.Payments.Domain.Payment.DomainEvents;

public abstract record CreditCardUpdateDomainEventBase : IDomainEvent
{
    public required string PolicyId { get; init; }

    public required string PayorId { get; init; }

    public required DateTime EffectiveDate { get; init; }

    public CreditCard? CreditCard { get; init; }
    
    public string? PaymentMethod { get; init; }
}