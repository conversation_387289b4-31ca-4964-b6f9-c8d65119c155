﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.CapturePayment
{
    public class CapturePaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<CapturePaymentCommandHandler> logger)
        : ICommandHandler<CapturePaymentCommand, PaymentDto>
    {
        /// <param name="capturePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(CapturePaymentCommand capturePaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("CapturePaymentCommandHandler.Handle: Starting payment capture. PaymentId: {PaymentId}",
                capturePaymentCommand.PaymentId);

            try
            {
                PaymentAggregate payment = await paymentService.CapturePreauthPaymentAsync(capturePaymentCommand.PaymentId, cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("CapturePaymentCommandHandler.Handle: Payment capture completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "CapturePaymentCommandHandler.Handle: Unexpected error during payment capture. PaymentId: {PaymentId}",
                    capturePaymentCommand.PaymentId);
                throw;
            }
        }
    }
}
