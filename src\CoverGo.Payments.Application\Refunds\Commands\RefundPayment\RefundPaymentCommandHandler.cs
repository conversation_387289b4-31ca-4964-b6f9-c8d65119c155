﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Application.Refunds.Contracts;
using CoverGo.Payments.Domain.Refund;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Refunds.Commands.RefundPayment
{
    public class RefundPaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<RefundPaymentCommandHandler> logger)
        : ICommandHandler<RefundPaymentCommand, PaymentRefundDto>
    {
        /// <param name="refundPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentRefundDto> Handle(RefundPaymentCommand refundPaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("RefundPaymentCommandHandler.Handle: Starting payment refund. PaymentId: {PaymentId}, RefundAmount: {Amount}, DecimalPrecision: {DecimalPrecision}",
                refundPaymentCommand.PaymentId,
                refundPaymentCommand.Amount,
                refundPaymentCommand.DecimalPrecision);

            try
            {
                RefundAggregate refundPayment = await paymentService.RefundPaymentAsync(
                    refundPaymentCommand.PaymentId, 
                    refundPaymentCommand.Amount, 
                    refundPaymentCommand.DecimalPrecision,
                    cancellationToken);

                var result = mapper.Map<PaymentRefundDto>(refundPayment);

                logger.LogInformation("RefundPaymentCommandHandler.Handle: Payment refund completed. PaymentId: {PaymentId}, RefundId: {RefundId}, FinalStatus: {Status}",
                    refundPayment.PaymentId,
                    refundPayment.Id,
                    refundPayment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "RefundPaymentCommandHandler.Handle: Unexpected error during payment refund. PaymentId: {PaymentId}",
                    refundPaymentCommand.PaymentId);
                throw;
            }
        }
    }
}
