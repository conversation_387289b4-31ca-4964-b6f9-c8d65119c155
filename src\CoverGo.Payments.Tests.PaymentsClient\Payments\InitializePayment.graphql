mutation InitializePayment($input: InitializePaymentInput!) {
    initializePayment(input: $input) {
        initialPaymentResult {
            payment {
                id
                money {
                    paymentCurrencyCode
                    paymentCurrencyDesc
                    paymentDecimalPrecision
                    paymentAmount
                }
                paymentProvider
                internalReference
                externalReference
                paymentStatus
                auditInfo {
                    createdAt
                    createdBy
                    lastModifiedAt
                    lastModifiedBy
                    deletedAt
                    deletedBy
                }
                refundedAmount
            }
            redirectUrl
            data {
                key
                value
            }
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                    code
                }
            }
            ... on DomainError {
                message
                code
            }
        }
    }
}