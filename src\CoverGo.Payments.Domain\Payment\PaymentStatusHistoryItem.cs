﻿using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using GuardClauses;

namespace CoverGo.Payments.Domain.Payment
{
    public class PaymentStatusHistoryItem : ValueObject
    {
        private PaymentStatusHistoryItem()
        {
            
        }
        
        public PaymentStatusHistoryItem(
            PaymentStatus status,
            PaymentMoney money,
            string? error,
            string? webhookBody)
        {
            CreatedAtDateUtc = PreciseClock.UtcNow;
            Money = money;
            WebhookBody = webhookBody;
            Error = error;
            Status = status;

        }
        
        public DateTime CreatedAtDateUtc { get; private set; }
        
        public PaymentMoney Money { get; private set; }
        
        public string? WebhookBody { get; private set; }
        
        public string? Error { get; private set; }
        
        public int? RefundId { get; private set; }

        public void SetRefundId(int refundId)
        {
            GuardClause.IsZeroOrNegative(refundId, nameof(refundId));
            if (RefundId != null) throw new DomainException("RefundId != null");

            RefundId = refundId;
        }

        public PaymentStatus Status { get; private set; }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return CreatedAtDateUtc;
            yield return Money;
            yield return WebhookBody;
            yield return Status;
            yield return Error;
            yield return RefundId;
        }
    }
}
