﻿<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>net7.0;net8.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <PackageId>CoverGo.Payments.Integration.Events</PackageId>
        <Company>CoverGo</Company>
        <PackageDescription>BuildingBlocks Dapr based integration events for Payments</PackageDescription>
        <RepositoryUrl>https://github.com/CoverGo/payments</RepositoryUrl>
        <Version>1.0.0</Version>
        <Authors>CoverGo</Authors>
        <PackageRequireLicenseAcceptance>false</PackageRequireLicenseAcceptance>
        <Description>BuildingBlocks Dapr based integration events for Payments</Description>
        <PackageProjectUrl>https://github.com/CoverGo/payments</PackageProjectUrl>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Contracts"/>
    </ItemGroup>

</Project>
