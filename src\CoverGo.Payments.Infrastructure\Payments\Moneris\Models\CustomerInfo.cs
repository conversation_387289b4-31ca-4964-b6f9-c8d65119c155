﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class CustomerInfo
{
    [JsonProperty("first_name")]
    public string? FirstName { get; set; }

    [JsonProperty("last_name")]
    public string? LastName { get; set; }

    [JsonProperty("phone")]
    public string? Phone { get; set; }

    [JsonProperty("email")]
    public string? Email { get; set; }
}