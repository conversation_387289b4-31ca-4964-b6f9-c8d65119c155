using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.CapturePayment;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.CapturePayment;

[MutationType]
public class CapturePaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "payment")]
    //[Authorize]
    public async Task<PaymentDto> CapturePayment(
        CapturePaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}