using CoverGo.Payments.Domain.Payment;
using MongoDB.Bson.Serialization;

namespace CoverGo.Payments.Infrastructure.Common;

public class CurrencyCodeStringSerializer(Func<string, CurrencyCode> factory) : IBsonSerializer<CurrencyCode>
{
    public Type ValueType => typeof(CurrencyCode);

    public CurrencyCode Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
    {
        string? value = context.Reader.ReadString();

        return factory.Invoke(value);
    }

    object IBsonSerializer.Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args) =>
        Deserialize(context, args);

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, CurrencyCode value) =>
        context.Writer.WriteString(value.Value);

    public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object value)
    {
        if (value is not CurrencyCode currencyCode) throw new ArgumentException("Invalid value type", nameof(value));

        Serialize(context, args, currencyCode);
    }
}