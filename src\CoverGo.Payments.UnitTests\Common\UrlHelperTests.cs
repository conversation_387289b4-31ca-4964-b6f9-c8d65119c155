﻿using CoverGo.Payments.Infrastructure.Common;

namespace CoverGo.Payments.UnitTests.Common
{
    public class UrlHelperTests
    {
        [Theory]
        [InlineData("https://example.com/path?query=1", "https://neworigin.com", "https://neworigin.com/path?query=1")]
        [InlineData("http://oldexample.com/api", "http://newexample.com", "http://newexample.com/api")]
        [InlineData("https://example.com:5000/path", "https://neworigin.com", "https://neworigin.com/path")]
        [InlineData("https://example.com:5000/path", "https://neworigin.com:4000", "https://neworigin.com:4000/path")]
        public void ReplaceOrigin_ShouldReplaceOrigin_WhenValidInputs(string originalUrl, string newOrigin, string expectedUrl)
        {
            // Act
            var result = UrlHelper.ReplaceOrigin(originalUrl, newOrigin);

            // Assert
            Assert.Equal(expectedUrl, result);
        }

        [Fact]
        public void ReplaceOrigin_ShouldThrowArgumentException_WhenUrlIsNull()
        {
            // Arrange
            string? url = null;
            string newOrigin = "https://example.com";

            // Act & Assert
            Assert.Throws<ArgumentException>(() => UrlHelper.ReplaceOrigin(url, newOrigin));
        }

        [Fact]
        public void ReplaceOrigin_ShouldReturnOriginalUrl_WhenNewOriginIsNullOrEmpty()
        {
            // Arrange
            string url = "https://example.com/path";
            string newOrigin = string.Empty;

            // Act
            var result = UrlHelper.ReplaceOrigin(url, newOrigin);

            // Assert
            Assert.Equal(url, result);
        }

        [Fact]
        public void ReplaceOrigin_ShouldThrowUriFormatException_WhenUrlIsInvalid()
        {
            // Arrange
            string invalidUrl = "not-a-valid-url";
            string newOrigin = "https://neworigin.com";

            // Act & Assert
            Assert.Throws<UriFormatException>(() => UrlHelper.ReplaceOrigin(invalidUrl, newOrigin));
        }

        [Fact]
        public void ReplaceOrigin_ShouldThrowUriFormatException_WhenNewOriginIsInvalid()
        {
            // Arrange
            string url = "https://example.com";
            string invalidOrigin = "invalid-origin";

            // Act & Assert
            Assert.Throws<UriFormatException>(() => UrlHelper.ReplaceOrigin(url, invalidOrigin));
        }
    }
}
