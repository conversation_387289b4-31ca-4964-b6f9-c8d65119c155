﻿using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.RegisterPayment
{
    public record RegisterPaymentCommand(
        PaymentProvider PaymentProvider,
        decimal Amount,
        string CurrencyCode,
        DateTime EffectiveDate,
        string PolicyId,
        string? InvoiceNumber,
        string PayorId,
        string? Status,
        string? PaymentMethod,
        string? Error,
        JsonElement? DynamicFields,
        string TransactionType) : ICommand<PaymentDto>;
}
