{"serviceUrls": {"auth": "http://covergo-auth:8080/"}, "ConnectionStrings": {"auth": "http://covergo-auth:8080/", "gateway": "http://covergo-gateway:8080/", "redis": "redis:6379,abortConnect=false"}, "MongoDatabaseConfiguration": {"ConnectionString": "**************************************?replicaSet=covergo-mongo-set"}, "GraphQL": {"IncludeExceptionDetails": true}, "GraphQLStitching": {"Enabled": true, "SchemaName": "payments", "Redis": {"Publish": true, "ConfigurationName": "GatewayV2"}}, "CheckDI": true, "UseInMemoryBus": true}