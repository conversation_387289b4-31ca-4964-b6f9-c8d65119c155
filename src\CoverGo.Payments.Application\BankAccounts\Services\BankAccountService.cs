using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Domain.BankAccount;

namespace CoverGo.Payments.Application.BankAccounts.Services;

public class BankAccountService : IBankAccountService
{
    private readonly IRepository<BankAccount, string> _bankAccountRepository;

    public BankAccountService(IRepository<BankAccount, string> bankAccountRepository)
    {
        _bankAccountRepository = bankAccountRepository;
    }

    public async Task<bool> IsDuplicateBankAccountAsync(
        string accountHolderName,
        string? accountNumber,
        string bankName,
        string? iban,
        string? excludeId = null,
        CancellationToken cancellationToken = default)
    {
        var existingBankAccounts = await _bankAccountRepository.FindAllByAsync(
            x => x.AccountHolderName == accountHolderName &&
                 x.BankName == bankName &&
                 (excludeId == null || x.Id != excludeId), // Exclude current record if provided
            cancellationToken);

        if (!existingBankAccounts.Any())
            return false;

        // Additional filtering for optional fields
        var duplicates = existingBankAccounts.Where(x =>
            (string.IsNullOrEmpty(accountNumber) || 
             string.IsNullOrEmpty(x.AccountNumber) ||
             x.AccountNumber.ToLower() == accountNumber.ToLower()) &&
            (string.IsNullOrEmpty(iban) || 
             string.IsNullOrEmpty(x.Iban) ||
             x.Iban.ToLower() == iban.ToLower()));

        return duplicates.Any();
    }
} 