﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.ING.Models;

public class DebitPaymentProfileRequest
{
    [JsonProperty(IngContainer.PropsName.ServiceId)]
    public required string ServiceId { get; set; }
    
    [JsonProperty(IngContainer.PropsName.PaymentProfileId)]
    public required string PaymentProfileId { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Amount)]
    public required decimal Amount { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Currency)]
    public required string Currency { get; set; }
    
    [JsonProperty(IngContainer.PropsName.OrderId)]
    public required string OrderId { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Title, NullValueHandling = NullValueHandling.Ignore)]
    public string? Title { get; set; }
}