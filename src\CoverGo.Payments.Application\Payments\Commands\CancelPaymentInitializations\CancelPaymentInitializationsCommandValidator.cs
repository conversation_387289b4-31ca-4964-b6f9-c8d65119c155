﻿using FluentValidation;
using Microsoft.Extensions.Logging;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;

public class CancelPaymentInitializationsCommandValidator : AbstractValidator<CancelPaymentInitializationsCommand>
{
    public CancelPaymentInitializationsCommandValidator(ILogger<CancelPaymentInitializationsCommandValidator> logger)
    {
        RuleFor(c => c.InitializationTokens).NotEmpty().WithMessage("Initialization tokens must not be empty.");
        RuleForEach(c => c.InitializationTokens).NotEmpty().WithMessage("Payment initialization(s) not found.");

        RuleFor(c => c.CancellationStatus)
            .Must(BeValidCancellationStatus)
            .When(c => c.CancellationStatus.HasValue)
            .WithMessage("Invalid cancellation status. Valid statuses are: Canceled, Expired, Failed.");

        logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
    }

    private static bool BeValidCancellationStatus(PaymentStatus? status)
    {
        if (!status.HasValue) return true;
        
        var validStatuses = new[] { PaymentStatus.Canceled, PaymentStatus.Expired, PaymentStatus.Failed };
        return validStatuses.Contains(status.Value);
    }
}
