using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.InitializeUpdatePayment;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.InitializeUpdatePayment;

[MutationType]
public class InitializeUpdatePaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "initializeUpdatePaymentResult")]
    //[Authorize]
    public async Task<ProcessInitialPaymentResultDto> InitializeUpdatePayment(
        InitializeUpdatePaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}