﻿<Project Sdk="Microsoft.NET.Sdk">

    <ItemGroup>
        <PackageReference Include="CoverGo.BuildingBlocks.Bootstrapper"/>
        <PackageReference Include="CoverGo.BuildingBlocks.DataAccess.Mongo"/>
        <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Dapr" />
        <PackageReference Include="CoverGo.BuildingBlocks.MessageBus.Outbox.Mongo" />
        <PackageReference Include="CoverGo.Gateway.Client"/>
        <PackageReference Include="covergo.proxies.auth" />
        <PackageReference Include="CoverGo.Reference.Client"/>
        <PackageReference Include="IdentityModel"/>
        <PackageReference Include="Polly" />
        <PackageReference Include="Scrutor"/>
        <PackageReference Include="StrawberryShake.Server"/>
        <PackageReference Include="Stripe.net" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\CoverGo.Payments.Application\CoverGo.Payments.Application.csproj"/>
        <ProjectReference Include="..\CoverGo.Payments.Integration.Events\CoverGo.Payments.Integration.Events.csproj" />
    </ItemGroup>

    <ItemGroup>
        <InternalsVisibleTo Include="CoverGo.Payments.Tests.Integration"/>
        <InternalsVisibleTo Include="CoverGo.Payments.Tests.Unit"/>
    </ItemGroup>

    <ItemGroup>
      <Reference Include="eSELECTplus_dotNet_API">
        <HintPath>Libs\eSELECTplus_dotNet_API.dll</HintPath>
      </Reference>
    </ItemGroup>

</Project>
