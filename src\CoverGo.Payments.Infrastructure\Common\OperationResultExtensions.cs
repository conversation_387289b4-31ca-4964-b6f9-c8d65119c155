﻿using StrawberryShake;

namespace CoverGo.Payments.Infrastructure.Common;

public static class OperationResultExtensions
{
    public static void ThrowOnError<TResultData>(this IOperationResult<TResultData> operationResult,
        Func<IReadOnlyList<string>?>? getOperationErrors = null) where TResultData : class
    {
        if (operationResult == null) throw new ArgumentNullException(nameof(operationResult));

        if (operationResult.Errors.Any())
        {
            IClientError error = operationResult.Errors.First();
            throw new InternalApiResponseException(error.Message, error.Exception);
        }

        if (operationResult.Data is null) throw new InternalApiResponseException("Missing operation result data");

        if (getOperationErrors is not null)
        {
            IReadOnlyList<string>? errors = getOperationErrors();
            if (errors?.Any() ?? false)
            {
                string errorMessage = string.Join(Environment.NewLine, errors);
                throw new InternalApiResponseException(errorMessage);
            }
        }
    }
}