﻿namespace CoverGo.Payments.Infrastructure.Payments.ING;

public static class IngContainer
{
    internal static class PropsName
    {
        public const string CustomerFirstName = "customerFirstName";
        public const string CustomerLastName = "customerLastName";
        public const string CustomerEmail = "customerEmail";
        public const string CustomerPhone = "customerPhone";
        public const string CustomerId = "customerId";
        public const string ServiceId = "serviceId";
        public const string PaymentProfileId = "paymentProfileId";
        public const string Amount = "amount";
        public const string Currency = "currency";
        public const string OrderId = "orderId";
        public const string Title = "title";
        public const string PaymentProfiles = "paymentProfiles";
        public const string Id = "id";
        public const string FirstName = "firstName";
        public const string LastName = "lastName";
        public const string MaskedNumber = "maskedNumber";
        public const string Month = "month";
        public const string Year = "year";
        public const string Organization = "organization";
        public const string IsActive = "isActive";
        public const string Profile = "profile";
        public const string Type = "type";
        public const string Source = "source";
        public const string Created = "created";
        public const string Modified = "modified";
        public const string NotificationUrl = "notificationUrl";
        public const string Status = "status";
        public const string PaymentMethod = "paymentMethod";
        public const string PaymentMethodCode = "paymentMethodCode";
        public const string Payment = "payment";
        public const string StatusCode = "statusCode";
        public const string StatusCodeDescription = "statusCodeDescription";
        public const string MerchantMid = "merchantMid";
        public const string MerchantCustomerId = "merchantCustomerId";
        public const string Transaction = "transaction";
    }
}