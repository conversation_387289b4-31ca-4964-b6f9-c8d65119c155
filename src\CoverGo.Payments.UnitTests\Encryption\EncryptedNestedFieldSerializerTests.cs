﻿using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Infrastructure.DataAccess;
using MongoDB.Bson.IO;
using MongoDB.Bson.Serialization;
using MongoDB.Bson;
using Moq;
using FluentAssertions;
using System.Text.Json;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.UnitTests.Encryption
{
    public class EncryptedNestedFieldSerializerTests
    {
        private readonly Mock<IEncryptionService> _encryptionServiceMock;
        private readonly EncryptedNestedFieldSerializer<TestObject> _serializer;

        public EncryptedNestedFieldSerializerTests()
        {
            _encryptionServiceMock = new Mock<IEncryptionService>();
            _serializer = new EncryptedNestedFieldSerializer<TestObject>(_encryptionServiceMock.Object);
        }

        [Fact]
        public void Given_NullValue_When_Serializing_Then_WritesNull()
        {
            // Arrange
            var document = new BsonDocument();
            using var writer = new BsonDocumentWriter(document);
            var context = BsonSerializationContext.CreateRoot(writer);

            writer.WriteStartDocument();
            writer.WriteName("Field");

            // Act
            _serializer.Serialize(context, default, null!);

            writer.WriteEndDocument();

            // Assert
            document["Field"].Should().Be(BsonNull.Value);
        }

        [Fact]
        public void Given_ClassWithEncryptedProperty_When_Serializing_Then_EncryptsAndWritesString()
        {
            // Arrange
            var document = new BsonDocument();
            using var writer = new BsonDocumentWriter(document);
            var context = BsonSerializationContext.CreateRoot(writer);

            var testObject = new TestObject { SensitiveProperty = "sensitive_value" };
            var serializedJson = JsonSerializer.Serialize(testObject.SensitiveProperty);
            var encryptedString = "encrypted_string";

            _encryptionServiceMock.Setup(s => s.Encrypt(serializedJson)).Returns(encryptedString);

            // Act
            _serializer.Serialize(context, default, testObject);

            // Assert
            var fieldValue = document["SensitiveProperty"];
            _encryptionServiceMock.Verify(s => s.Encrypt(serializedJson), Times.Once);
            fieldValue.Should().BeOfType<BsonString>();
            fieldValue.AsString.Should().Be(encryptedString);
        }

        [Fact]
        public void Given_ClassWithUnencryptedProperty_When_Serializing_Then_SerializesNormally()
        {
            // Arrange
            var document = new BsonDocument();
            using var writer = new BsonDocumentWriter(document);
            var context = BsonSerializationContext.CreateRoot(writer);

            var testObject = new TestObject { NormalProperty = "normal_value" };

            writer.WriteStartDocument();
            writer.WriteName("Field");

            // Act
            _serializer.Serialize(context, default, testObject);

            writer.WriteEndDocument();

            // Assert
            var fieldValue = document["Field"]["NormalProperty"];
            fieldValue.Should().BeOfType<BsonString>();
            fieldValue.AsString.Should().Be("normal_value");
        }

        [Fact]
        public void Given_EncryptedString_When_Deserializing_Then_DecryptsAndSetsProperty()
        {
            // Arrange
            var encryptedString = "encrypted_value";
            var decryptedJson = "\"sensitive_value\"";
            _encryptionServiceMock.Setup(s => s.Decrypt(encryptedString)).Returns(decryptedJson);

            var document = new BsonDocument
                {
                    { "Field", new BsonDocument { { "SensitiveProperty", encryptedString } } }
                };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            reader.ReadStartDocument();
            reader.ReadName("Field");

            // Act
            var result = _serializer.Deserialize(context, default);

            // Assert
            result.Should().NotBeNull();
            result.SensitiveProperty.Should().Be("sensitive_value");
            _encryptionServiceMock.Verify(s => s.Decrypt(encryptedString), Times.Once);
        }

        [Fact]
        public void Given_UnencryptedString_When_Deserializing_Then_SetsPropertyDirectly()
        {
            // Arrange
            var document = new BsonDocument
                {
                    { "Field", new BsonDocument { { "NormalProperty", "normal_value" } } }
                };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            reader.ReadStartDocument();
            reader.ReadName("Field");

            // Act
            var result = _serializer.Deserialize(context, default);

            // Assert
            result.Should().NotBeNull();
            result.NormalProperty.Should().Be("normal_value");
        }

        [Fact]
        public void Given_EncryptedCollection_When_Deserializing_Then_DecryptsAndSetsCollection()
        {
            // Arrange
            var encryptedString = "encrypted_value";
            var decryptedJson = "[\"item1\", \"item2\"]";
            _encryptionServiceMock.Setup(s => s.Decrypt(encryptedString)).Returns(decryptedJson);

            var document = new BsonDocument
                {
                    { "Field", new BsonDocument { { "CollectionProperty", encryptedString } } }
                };
            using var reader = new BsonDocumentReader(document);
            var context = BsonDeserializationContext.CreateRoot(reader);

            reader.ReadStartDocument();
            reader.ReadName("Field");

            // Act
            var result = _serializer.Deserialize(context, default);

            // Assert
            result.Should().NotBeNull();
            result.CollectionProperty.Should().ContainInOrder("item1", "item2");
            _encryptionServiceMock.Verify(s => s.Decrypt(encryptedString), Times.Once);
        }

        private class TestObject
        {
            [Encrypted]
            public string? SensitiveProperty { get; set; }

            public string? NormalProperty { get; set; }

            [Encrypted]
            public List<string>? CollectionProperty { get; set; }
        }
    }
}
