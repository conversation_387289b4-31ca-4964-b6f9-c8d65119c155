using CoverGo.Payments.Tests.GatewayClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Payments.IntegrationTests.Support;

public static class TestClientsCreator
{
    public static AuthTokenProvider CreateAuthTokenProvider(IConfiguration configuration)
    {
        var serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(configuration);

        serviceCollection.AddTestGatewayClient()
            .ConfigureHttpClient((sp, client) =>
            {
                client.BaseAddress = GetServiceUri(sp.GetRequiredService<IConfiguration>(), ServiceName.Gateway);
            });

        var serviceProvider = serviceCollection.BuildServiceProvider();
        ITestGatewayClient gatewayClient = serviceProvider.GetRequiredService<ITestGatewayClient>();
        return new AuthTokenProvider(gatewayClient);
    }

    public static ITestGatewayClient CreateTestGatewayClient(IConfiguration configuration, AuthTokenProvider authTokenProvider)
    {
        IServiceCollection serviceCollection = GetDefaultServiceCollection(configuration, authTokenProvider);
        serviceCollection.AddTestGatewayClient()
            .ConfigureHttpClient((sp, client) =>
            {
                ConfigureStrawberryShakeClient(client, sp, ServiceName.Gateway);
            });

        var serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ITestGatewayClient>();
    }

    private static void ConfigureStrawberryShakeClient(HttpClient client, IServiceProvider sp, string serviceName)
    {
        client.BaseAddress = GetServiceUri(sp.GetRequiredService<IConfiguration>(), serviceName);
        AuthTokenProvider tokenProvider = sp.GetRequiredService<AuthTokenProvider>();
        string token = tokenProvider.GetToken().GetAwaiter().GetResult();
        string header = $"Bearer {token}";
        client.DefaultRequestHeaders.TryAddWithoutValidation("Authorization", header);
    }

    private static IServiceCollection GetDefaultServiceCollection(IConfiguration configuration, AuthTokenProvider authTokenProvider) =>
        new ServiceCollection()
            .AddSingleton(configuration)
            .AddSingleton(authTokenProvider);

    public static Uri GetServiceUri(IConfiguration configuration, string serviceName)
    {
        string? path = configuration.GetConnectionString(serviceName);
        string url = $"{path}graphql";
        return new Uri(url);
    }
}