using CoverGo.Payments.Application.BankAccounts.Services;
using CoverGo.Payments.Domain.BankAccount;
using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;

public class AddBankAccountCommandValidator : AbstractValidator<AddBankAccountCommand>
{
    public AddBankAccountCommandValidator(
        ILogger<AddBankAccountCommandValidator> logger,
        IBankAccountService bankAccountService)
    {
        RuleFor(x => x.Country)
            .NotEmpty()
            .WithMessage("Country is required");

        RuleFor(x => x.Currency)
            .NotEmpty()
            .WithMessage("Currency is required");

        RuleFor(x => x.AccountHolderName)
            .NotEmpty()
            .WithMessage("Account Holder Name is required");

        RuleFor(x => x.BankName)
            .NotEmpty()
            .WithMessage("Bank Name is required");

        RuleFor(x => x.Bic)
            .NotEmpty()
            .WithMessage("BIC is required");

        RuleFor(x => x.UsedFor)
            .NotEmpty()
            .WithMessage("Used For is required")
            .Must(usedFor => usedFor != null && usedFor.Any())
            .WithMessage("At least one usage type must be specified");

        // Duplication check - async validation
        RuleFor(x => x)
            .MustAsync(async (command, cancellationToken) =>
            {
                var isDuplicate = await bankAccountService.IsDuplicateBankAccountAsync(
                    command.AccountHolderName,
                    command.AccountNumber,
                    command.BankName,
                    command.Iban,
                    excludeId: null, // No exclusion for new bank accounts
                    cancellationToken);
                
                if (isDuplicate)
                {
                    throw new BankAccountAlreadyExistsException();
                }
                
                return true; // Validation passes
            });

        logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
    }
} 