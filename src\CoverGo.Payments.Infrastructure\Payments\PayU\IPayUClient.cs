﻿using CoverGo.Payments.Infrastructure.Payments.PayU.Models;

namespace CoverGo.Payments.Infrastructure.Payments.PayU
{
    public interface IPayUClient
    {
        Task<PaymentResponse?> PostPaymentAsync(PaymentRequest payment, CancellationToken ct);
        Task<PaymentResponse?> GetPaymentAsync(string merchantPaymentReference, CancellationToken ct);
        Task<CaptureResponse?> CapturePaymentAsync(CaptureRequest captureRequest, CancellationToken ct);
        Task<RefundResponse?> PostRefundAsync(RefundRequest refundRequest, CancellationToken ct);
        Task<TokenResponse?> PostTokenAsync(TokenRequest tokenRequest, CancellationToken ct);
        Task<SessionResponse?> PostSessionAsync(SessionRequest payment, CancellationToken ct);
    }
}