﻿using GuardClauses;

namespace CoverGo.Payments.Application.Payments.Contracts;

public class ProcessInitialPaymentResultDto : RedirectUrlOutputDto
{
    public PaymentDto Payment { get; set; }

    public ProcessInitialPaymentResultDto(PaymentDto payment)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
            
        Payment = payment;
    }
        
    public ProcessInitialPaymentResultDto(PaymentDto payment, Uri? redirectUrl)
        : base(redirectUrl)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
            
        Payment = payment;
    }
        
    public ProcessInitialPaymentResultDto(PaymentDto payment, Uri? redirectUrl, IDictionary<string, string>? data)
        : base(redirectUrl, data)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
            
        Payment = payment;
    }
}