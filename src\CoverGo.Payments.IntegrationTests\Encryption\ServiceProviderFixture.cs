﻿using CoverGo.Payments.IntegrationTests.Support;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Driver;

namespace CoverGo.Payments.IntegrationTests.Encryption
{
    public class ServiceProviderFixture : IDisposable
    {
        public ServiceProviderFixture() 
        {
            ServiceProvider = new TestApp().ConfigureServices();
        }

        public ServiceProvider ServiceProvider { get; private set; }

        public void Dispose()
        {
            var client = ServiceProvider.GetRequiredService<IMongoClient>();
            client.DropDatabase("testdatabase-covergo");
        }
    }
}
