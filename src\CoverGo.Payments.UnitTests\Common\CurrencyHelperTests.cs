﻿using CoverGo.Payments.Domain.Helpers;
using FluentAssertions;

namespace CoverGo.Payments.UnitTests.Common;

public class CurrencyHelperTests
{
    [Theory]
    [InlineData("USD", "840")]
    [InlineData("EUR", "978")]
    [InlineData("JPY", "392")]
    public void GetIsoCode_Should_ReturnCorrectIsoCode(string currency, string expectedIsoCode)
    {
        // Act
        string isoCode = CurrencyHelper.GetIsoCode(currency);

        // Assert
        isoCode.Should().Be(expectedIsoCode);
    }

    [Theory]
    [InlineData("840", "USD")]
    [InlineData("978", "EUR")]
    [InlineData("392", "JPY")]
    public void GetCurrency_Should_ReturnCorrectCurrency(string isoCode, string expectedCurrency)
    {
        // Act
        string currency = CurrencyHelper.GetCurrency(isoCode);

        // Assert
        currency.Should().Be(expectedCurrency);
    }

    [Fact]
    public void GetIsoCode_Should_ThrowArgumentException_ForUnsupportedCurrency()
    {
        // Act
        Action act = () => CurrencyHelper.GetIsoCode("XYZ");

        // Assert
        act.Should().Throw<ArgumentException>().WithMessage("Unsupported currency: XYZ");
    }

    [Fact]
    public void GetCurrency_Should_ThrowArgumentException_ForUnsupportedIsoCode()
    {
        // Act
        Action act = () => CurrencyHelper.GetCurrency("999");

        // Assert
        act.Should().Throw<ArgumentException>().WithMessage("Unsupported ISO code: 999");
    }

    [Theory]
    [InlineData(12345L, 123.45)]
    [InlineData(100L, 1.00)]
    [InlineData(0L, 0.00)]
    public void ConvertSubunitsToUnits_Should_ReturnCorrectUnits(long subunits, decimal expectedUnits)
    {
        // Act
        decimal? units = CurrencyHelper.ConvertSubunitsToUnits(subunits);

        // Assert
        units.Should().Be(expectedUnits);
    }

    [Theory]
    [InlineData(123.45, 12345L)]
    [InlineData(1.00, 100L)]
    [InlineData(0.00, 0L)]
    public void ConvertUnitsToSubunits_Should_ReturnCorrectSubunits(decimal units, long expectedSubunits)
    {
        // Act
        long subunits = CurrencyHelper.ConvertUnitsToSubunits(units);

        // Assert
        subunits.Should().Be(expectedSubunits);
    }
}