using CoverGo.Payments.Application.Common;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Queries.MemberPaymentsQuery;
using CoverGo.Payments.Application.Payments.Queries.PaymentQuery;
using CoverGo.Payments.Application.Payments.Queries.PaymentsQuery;
using CoverGo.Payments.Domain.Payment;
using HotChocolate.Authorization;
using MediatR;
using CoverGo.BuildingBlocks.Application.Core.Ports;
using CoverGo.Payments.Application.HttpContextUtils;


namespace CoverGo.Payments.Api.Payments;

[QueryType]
public class PaymentQueries
{
    [Authorize]
    public Task<PaymentAggregate> Payment(
        [Service] IMediator mediator,
        Id id,
        CancellationToken cancellationToken = default) =>
        mediator.Send(new PaymentQuery(id), cancellationToken);

    [Authorize]
    public Task<PagedResult<PaymentAggregate>> Payments(
        int? skip,
        int? take,
        PaymentsWhere? where,
        [Service] IMediator mediator,
        CancellationToken cancellationToken = default)
        => mediator.Send(new PaymentsQuery
        {
            Skip = skip,
            Take = take,
            Where = where
        }, cancellationToken);

    [Authorize]
    public Task<PagedResult<PaymentAggregate>> MemberPayments(
    int? skip,
    int? take,
    MemberPaymentsWhere? where,
    [Service] IMediator mediator,
    [Service] IUserClaimExtractor userClaimExtractor,
    [Service] ILogger<PaymentQueries> logger,
    CancellationToken cancellationToken = default)
    {
        // log the user ID for debugging purposes
        var userId = userClaimExtractor.GetUserEntityId();
        logger.LogInformation($"MemberPayments query for User ID: {userId}");
        var patchedWhere = where ?? new MemberPaymentsWhere();
        patchedWhere.PayorId ??= new StringWhere();
        patchedWhere.PayorId.Eq = userId;
        return mediator.Send(new MemberPaymentsQuery
        {
            Skip = skip,
            Take = take,
            Where = patchedWhere,
        }, cancellationToken);
    }
}