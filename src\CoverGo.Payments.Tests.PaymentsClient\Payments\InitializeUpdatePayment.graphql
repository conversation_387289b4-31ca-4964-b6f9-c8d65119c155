mutation InitializeUpdatePayment {
    initializeUpdatePayment(input: {
        currencyDesc: "USD"
        policyId: "policy123"
        payorId: "payor123"
        paymentProvider: ING
    }) {
        initializeUpdatePaymentResult {
            payment {
                id
                paymentStatus
                money {
                    paymentCurrencyCode
                    paymentAmount
                }
                internalReference
                externalReference
                auditInfo {
                    createdAt
                    createdBy
                }
            }
            redirectUrl
            data {
                key
                value
            }
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                }
            }
            ... on DomainError {
                message
                code
            }
        }
    }
}