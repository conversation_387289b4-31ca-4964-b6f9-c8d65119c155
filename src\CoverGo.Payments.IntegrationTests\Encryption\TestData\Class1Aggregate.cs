﻿using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.IntegrationTests.Encryption.TestData
{
    public class Class1Aggregate : AggregateRootBase<string>
    {
        public Class1Aggregate(string id) : base(id)
        {
        }

        [Encrypted]
        public Class1? SensitiveClass { get; set; }
        public Class1? NormalClass { get; set; }
        public string? NormalString { get; set; }
    }

    public class Class1
    {
        public string NormalString { get; set; } = string.Empty;
        public int NormalInt { get; set; }
    }
}
