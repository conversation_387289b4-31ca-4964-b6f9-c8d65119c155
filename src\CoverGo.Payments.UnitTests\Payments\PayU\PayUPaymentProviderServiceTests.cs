﻿using System.Net;
using System.Text.Json;
using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Infrastructure.Payments.PayU;
using FluentAssertions;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace CoverGo.Payments.UnitTests.Payments.PayU;

public class PayUPaymentProviderServiceTests
{
    private const string ValidPaymentProviderSettingsJson =
        "{\"MerchantCode\":\"test_merchant_code\",\"SecretKey\":\"test_secret_key\",\"Environment\":\"qa\",\"RedirectUrl\":\"https://medihelp-admin.dev.asia.covergo.cloud/checkout/finalize\"}";

    private const string ValidDynamicFieldsJson =
        "{\"FirstName\":\"amina test\",\"LastName\":\"test\",\"Email\":\"<EMAIL>\",\"Phone\":\"123456789\",\"CountryCode\":\"PL\",\"Name\":\"test product\",\"Sku\":\"00000229\",\"UnitPrice\":\"250.00\",\"Quantity\":\"1\"}";

    private const string ValidOneTimeTokenDynamicFieldsJson =
        "{\"SessionId\":\"7f2c90b3-e3e6-4344-8043-9dd15c8d1c5d\",\"Token\":\"0OUA9nsXtc2Yhzhfmji4quWNolTg1+55tPhY1jehfo0JhWFGAbyCfeAEua0ypvXHqPjLYhqkdsD3e+5f4jBYRDWNz9MQyKCtBMVfAXQo+2M=\"}";

    private readonly string _authorizationResponseJson = GetAuthorizationResponse();
    private readonly string _finalizationResponseJson = GetFinalizationResponse();
    private readonly string _tokenResponseJson = GetTokenResponse();
    private readonly string _sessionResponseJson = GetSessionResponse();
    private readonly string _oneTimeTokenAuthorizationResponseJson = GetOneTimeTokenAuthorizationResponse();

    private readonly PayUPaymentProviderService _service;
    private readonly Mock<HttpClient> _httpClient;

    public PayUPaymentProviderServiceTests()
    {
        var httpClientFactoryMock = new Mock<IHttpClientFactory>();
        var dateTimeProviderMock = new Mock<IDateTimeProvider>();
        var loggerMock = new Mock<ILogger<PayUPaymentProviderService>>();
        var httpMessageHandlerMock = new Mock<HttpMessageHandler>(MockBehavior.Loose);

        _httpClient = new Mock<HttpClient>(httpMessageHandlerMock.Object);
        httpClientFactoryMock.Setup(f => f.CreateClient(It.IsAny<string>())).Returns(_httpClient.Object);

        _service = new PayUPaymentProviderService(loggerMock.Object, httpClientFactoryMock.Object,
            dateTimeProviderMock.Object);
    }

    [Fact]
    public async Task GetPreProcessRedirectUrlAsync_ShouldReturnRedirectUrl_WhenUpdatePaymentIsValid()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateValidUpdatePaymentAggregate();
        _httpClient
            .Setup(c => c.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateHttpResponse(_sessionResponseJson));

        JsonElement dynamicFields = JsonDocument.Parse(ValidDynamicFieldsJson).RootElement;

        // Act
        RedirectUrlOutput result = await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields);

        // Assert
        result.Should().NotBeNull();
        result.RedirectUrl.Should().BeNull();
        result.Data.Should().NotBeNull();
        result.Data["sessionId"].Should().Be("d64f4647751b98f2e9412d181d598bf2");
    }

    [Fact]
    public async Task GetPreProcessRedirectUrlAsync_ShouldReturnRedirectUrl_WhenPaymentIsValid()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateValidPaymentAggregate();
        _httpClient
            .Setup(c => c.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateHttpResponse(_authorizationResponseJson));

        JsonElement dynamicFields = JsonDocument.Parse(ValidDynamicFieldsJson).RootElement;

        // Act
        RedirectUrlOutput result = await _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields);

        // Assert
        result.Should().NotBeNull();
        result.RedirectUrl.Should()
            .Be(new Uri(
                "https://sandbox.payu.ro/pay/v2/?action=continue-payment-with-hosted-page&id=2Xrl83KakaSz2HPucq-6ap-RuLc"));
    }

    [Fact]
    public async Task GetPreProcessRedirectUrlAsync_ShouldThrowException_WhenPaymentHasInvalidAmount()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateInvalidPaymentAggregate();
        JsonElement dynamicFields = JsonDocument.Parse(ValidDynamicFieldsJson).RootElement;

        // Act
        Func<Task> act = () => _service.GetPreProcessRedirectUrlAsync(payment, dynamicFields);

        // Assert
        await act.Should().ThrowAsync<DomainException>().WithMessage("Mismatching payment and product amount");
    }

    [Fact]
    public async Task CapturePaymentAsync_ShouldUpdatePaymentStatus_WhenCaptureIsValid()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateValidPreauthPaymentAggregate();
        var capturePayment = new CapturePaymentAggregate(payment);

        _httpClient
            .Setup(c => c.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateHttpResponse(_authorizationResponseJson));

        // Act
        CapturePaymentAggregate result =
            await _service.CapturePaymentAsync(capturePayment, payment.Id, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Status.Should().Be(PaymentStatus.Succeeded);
    }

    [Fact]
    public async Task FinalizePaymentAsync_ShouldUpdateTokenAndExternalReference_WhenUpdatePaymentIsValid()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateValidUpdatePaymentAggregate();

        _httpClient
            .SetupSequence(c => c.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateHttpResponse(_oneTimeTokenAuthorizationResponseJson))
            .ReturnsAsync(CreateHttpResponse(_tokenResponseJson));

        JsonElement dynamicFields = JsonDocument.Parse(ValidOneTimeTokenDynamicFieldsJson).RootElement;

        // Act
        await _service.FinalizePaymentAsync(payment, null, dynamicFields, CancellationToken.None);

        // Assert
        payment.ExternalReference.Should().Be("5054561");
        (payment.InitialBearer as PSPBearerPseudoCC)?.Token.Should().Be("d64f4647751b98f2e9412d181d598bf2");
    }

    [Fact]
    public async Task FinalizePaymentAsync_ShouldUpdatePayerDataAndExternalReference_WhenPaymentIsValid()
    {
        // Arrange
        PreauthPaymentAggregate payment = CreateValidPreauthPaymentAggregate();

        _httpClient
            .SetupSequence(c => c.SendAsync(It.IsAny<HttpRequestMessage>(), It.IsAny<CancellationToken>()))
            .ReturnsAsync(CreateHttpResponse(_finalizationResponseJson))
            .ReturnsAsync(CreateHttpResponse(_tokenResponseJson));

        // Act
        await _service.FinalizePaymentAsync(payment, null, null, CancellationToken.None);

        // Assert
        payment.ExternalReference.Should().Be("5054561");
        (payment.InitialBearer as PSPBearerPseudoCC)?.Token.Should().Be("d64f4647751b98f2e9412d181d598bf2");
    }

    #region Helper Methods

    private static PreauthPaymentAggregate CreateValidUpdatePaymentAggregate()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.PayU, new PaymentMoney("840", "USD", 0, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null, null, true);
        payment.SetPspSettings(ValidPaymentProviderSettingsJson);
        payment.SetDynamicFields(ValidDynamicFieldsJson);
        payment.SetInternalReference();
        return payment;
    }

    private static PreauthPaymentAggregate CreateValidPaymentAggregate()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.PayU, new PaymentMoney("840", "USD", 250, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(ValidPaymentProviderSettingsJson);
        payment.SetInternalReference();
        return payment;
    }

    private static PreauthPaymentAggregate CreateInvalidPaymentAggregate()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.PayU, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(ValidPaymentProviderSettingsJson);
        payment.SetInternalReference();
        return payment;
    }

    private static PreauthPaymentAggregate CreateValidPreauthPaymentAggregate()
    {
        var payment = new PreauthPaymentAggregate(PaymentProvider.PayU, new PaymentMoney("840", "USD", 1000, 2),
            Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), Guid.NewGuid().ToString(), null);
        payment.SetPspSettings(ValidPaymentProviderSettingsJson);
        payment.SetInternalReference();
        return payment;
    }

    private static HttpResponseMessage CreateHttpResponse(string content) =>
        new() { StatusCode = HttpStatusCode.OK, Content = new StringContent(content) };

    private static string GetAuthorizationResponse() => """
                                                            {
                                                                "payuPaymentReference": "5054561",
                                                                "status": "SUCCESS",
                                                                "paymentResult": {
                                                                    "payuResponseCode": "PENDING_AUTHORIZATION",
                                                                    "url": "https://sandbox.payu.ro/pay/v2/?action=continue-payment-with-hosted-page&id=2Xrl83KakaSz2HPucq-6ap-RuLc",
                                                                    "type": "redirect"
                                                                },
                                                                "message": "Order saved and pending authorization.",
                                                                "merchantPaymentReference": "04fbec85-3466-4b87-b0b9-92788857a631",
                                                                "code": 200,
                                                                "amount": "50",
                                                                "currency": "RON",
                                                                "authorization": {
                                                                    "authorized": "PENDING"
                                                                }
                                                            }
                                                        """;

    private static string GetFinalizationResponse() => """
                                                           {
                                                               "paymentStatus": "PAYMENT_AUTHORIZED",
                                                               "payuPaymentReference": "5054561",
                                                               "authorizations": [
                                                                   {
                                                                       "timestamp": "2024-10-01T15:05:06+00:00",
                                                                       "authorized": "SUCCESS",
                                                                       "cardDetails": {
                                                                           "cardScheme": "VISA",
                                                                           "lastFourDigits": "1111",
                                                                           "binNumber": "444433"
                                                                       }
                                                                   }
                                                               ],
                                                               "code": 200,
                                                               "message": "Success",
                                                               "status": ""
                                                           }
                                                       """;

    private static string GetTokenResponse() => """
                                                    {
                                                        "token": "d64f4647751b98f2e9412d181d598bf2",
                                                        "cardUniqueIdentifier": "cf8fe045b791b01791304c613537430037b021ca5e92420627dce3a8cee23b28",
                                                        "expirationDate": "2029-12-31",
                                                        "cardHolderName": "Test Holder",
                                                        "tokenStatus": "ACTIVE",
                                                        "lastFourDigits": "1111",
                                                        "cardExpirationDate": "2029-12-31",
                                                        "code": 200,
                                                        "message": "Success",
                                                        "status": "SUCCESS"
                                                    }
                                                """;

    private static string GetSessionResponse() => """
                                                    {
                                                        "sessionId": "d64f4647751b98f2e9412d181d598bf2",
                                                        "lifeTimeMinutes": "10",
                                                        "createdAt": "2029-12-31",
                                                        "code": 200,
                                                        "message": "Success",
                                                        "status": "SUCCESS"
                                                    }
                                                """;

    private static string GetOneTimeTokenAuthorizationResponse() => """
                                                            {
                                                                "payuPaymentReference": "5054561",
                                                                "status": "SUCCESS",
                                                                "paymentResult": {
                                                                    "payuResponseCode": "AUTHORIZED"
                                                                },
                                                                "message": "Authorized.",
                                                                "merchantPaymentReference": "04fbec85-3466-4b87-b0b9-92788857a631",
                                                                "code": 200,
                                                                "amount": "0",
                                                                "currency": "RON",
                                                                "authorization": {
                                                                    "timestamp": "2024-10-01T15:05:06+00:00",
                                                                    "authorized": "SUCCESS",
                                                                    "cardDetails": {
                                                                        "cardScheme": "VISA",
                                                                        "lastFourDigits": "1111",
                                                                        "binNumber": "444433"
                                                                    }
                                                                }
                                                            }
                                                        """;

    #endregion
}