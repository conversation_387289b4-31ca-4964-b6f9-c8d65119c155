﻿using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Application.Encryption;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.IntegrationTests.Encryption.TestData
{
    public class SimpleAggregate : AggregateRootBase<string>
    {
        public SimpleAggregate(string id) : base(id)
        {
        }

        [Encrypted]
        public string? SensitiveString { get; set; }

        [Encrypted]
        public DateTime SensitiveDateTime { get; set; }

        [Encrypted]
        public int SensitiveInt { get; set; }

        [Encrypted]
        public decimal SensitiveDecimal { get; set; }

        [Encrypted]
        public bool SensitiveBool { get; set; }

        [Encrypted]
        public float SensitiveFloat { get; set; }

        [Encrypted]
        public long SensitiveLong { get; set; }

        public string? NormalString { get; set; }
    }
}
