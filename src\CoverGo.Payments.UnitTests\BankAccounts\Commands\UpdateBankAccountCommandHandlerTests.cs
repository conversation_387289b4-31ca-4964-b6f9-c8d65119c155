using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.BankAccounts.Commands.UpdateBankAccount;
using CoverGo.Payments.Domain.BankAccount;
using FluentAssertions;
using Moq;

namespace CoverGo.Payments.UnitTests.BankAccounts.Commands;

[Trait("Ticket", "CH-28394")]
public class UpdateBankAccountCommandHandlerTests
{
    private readonly Mock<IRepository<BankAccount, string>> _repositoryMock;
    private readonly UpdateBankAccountCommandHandler _handler;

    public UpdateBankAccountCommandHandlerTests()
    {
        _repositoryMock = new Mock<IRepository<BankAccount, string>>();
        _handler = new UpdateBankAccountCommandHandler(_repositoryMock.Object);
    }

    [Fact]
    public async Task GIVEN_ValidCommand_WHEN_Handle_THEN_UpdateBankAccountSuccessfully()
    {
        // Arrange
        var command = CreateValidCommand();
        var existingAggregate = CreateExistingBankAccountAggregate();
        var updatedAggregate = CreateUpdatedBankAccountAggregate();

        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Country.Should().Be(command.Country);
        result.Currency.Should().Be(command.Currency);
        result.AccountHolderName.Should().Be(command.AccountHolderName);
        result.BankName.Should().Be(command.BankName);
        result.Bic.Should().Be(command.Bic);
        result.AccountNumber.Should().Be(command.AccountNumber);
        result.Iban.Should().Be(command.Iban);
        result.UsedFor.Should().BeEquivalentTo(command.UsedFor);

        _repositoryMock.Verify(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()), Times.Once);

        // Verify the aggregate was updated with correct values
        existingAggregate.Country.Should().Be(command.Country);
        existingAggregate.Currency.Should().Be(command.Currency);
        existingAggregate.AccountHolderName.Should().Be(command.AccountHolderName);
        existingAggregate.BankName.Should().Be(command.BankName);
        existingAggregate.Bic.Should().Be(command.Bic);
        existingAggregate.AccountNumber.Should().Be(command.AccountNumber);
        existingAggregate.Iban.Should().Be(command.Iban);
        existingAggregate.UsedFor.Should().BeEquivalentTo(command.UsedFor);
    }

    [Fact]
    public async Task GIVEN_BankAccountNotFound_WHEN_Handle_THEN_PropagateRepositoryException()
    {
        // Arrange
        var command = CreateValidCommand();
        var expectedException = new InvalidOperationException("Bank account not found");

        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var act = () => _handler.Handle(command, CancellationToken.None);
        
        await act.Should().ThrowAsync<InvalidOperationException>();

        _repositoryMock.Verify(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()), Times.Never);
    }

    [Fact]
    public async Task GIVEN_OptionalFieldsNull_WHEN_Handle_THEN_UpdateBankAccountSuccessfully()
    {
        // Arrange
        var command = CreateValidCommand() with 
        { 
            AccountNumber = null, 
            Iban = null 
        };
        var existingAggregate = CreateExistingBankAccountAggregate();
        var updatedAggregate = CreateUpdatedBankAccountAggregate();


        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);



        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();

        // Verify optional fields are set to null
        existingAggregate.AccountNumber.Should().BeNull();
        existingAggregate.Iban.Should().BeNull();
    }

    [Fact]
    public async Task GIVEN_RepositoryUpdateFails_WHEN_Handle_THEN_PropagateException()
    {
        // Arrange
        var command = CreateValidCommand();
        var existingAggregate = CreateExistingBankAccountAggregate();
        var expectedException = new InvalidOperationException("Update failed");

        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ThrowsAsync(expectedException);

        // Act & Assert
        var act = () => _handler.Handle(command, CancellationToken.None);
        
        await act.Should().ThrowAsync<InvalidOperationException>();

        _repositoryMock.Verify(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task GIVEN_EmptyUsedFor_WHEN_Handle_THEN_ThrowBankAccountUsedForInvalidException()
    {
        // Arrange
        var command = CreateValidCommand() with { UsedFor = new List<BankAccountUsage>() };
        var existingAggregate = CreateExistingBankAccountAggregate();

        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        // Act & Assert
        var act = () => _handler.Handle(command, CancellationToken.None);
        
        await act.Should().ThrowAsync<BankAccountUsedForInvalidException>();

        _repositoryMock.Verify(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()), Times.Once);
        _repositoryMock.Verify(r => r.UpdateAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()), Times.Never);
    }



    [Fact]
    public async Task GIVEN_CancellationToken_WHEN_Handle_THEN_PassCancellationTokenToRepository()
    {
        // Arrange
        var command = CreateValidCommand();
        var cancellationToken = new CancellationToken(true);

        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, cancellationToken))
            .ThrowsAsync(new OperationCanceledException());

        // Act & Assert
        var act = () => _handler.Handle(command, cancellationToken);
        
        await act.Should().ThrowAsync<OperationCanceledException>();

        _repositoryMock.Verify(r => r.GetByIdAsync(command.Id, cancellationToken), Times.Once);
    }

    [Theory]
    [InlineData(BankAccountUsage.Billing)]
    [InlineData(BankAccountUsage.Claim)]
    public async Task GIVEN_DifferentUsageTypes_WHEN_Handle_THEN_UpdateBankAccountSuccessfully(BankAccountUsage usage)
    {
        // Arrange
        var command = CreateValidCommand() with { UsedFor = new List<BankAccountUsage> { usage } };
        var existingAggregate = CreateExistingBankAccountAggregate();
        var updatedAggregate = CreateUpdatedBankAccountAggregate();


        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);



        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        existingAggregate.UsedFor.Should().Contain(usage);
    }

    [Fact]
    public async Task GIVEN_MultipleUsageTypes_WHEN_Handle_THEN_UpdateBankAccountSuccessfully()
    {
        // Arrange
        var usageTypes = new List<BankAccountUsage> { BankAccountUsage.Billing, BankAccountUsage.Claim };
        var command = CreateValidCommand() with { UsedFor = usageTypes };
        var existingAggregate = CreateExistingBankAccountAggregate();
        var updatedAggregate = CreateUpdatedBankAccountAggregate();


        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);



        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        existingAggregate.UsedFor.Should().Contain(BankAccountUsage.Billing);
        existingAggregate.UsedFor.Should().Contain(BankAccountUsage.Claim);
    }

    [Fact]
    public async Task GIVEN_ValidCommand_WHEN_Handle_THEN_NotUpdateImmutableProperties()
    {
        // Arrange
        var command = CreateValidCommand();
        var existingAggregate = CreateExistingBankAccountAggregate();
        var originalPayorId = existingAggregate.PayorId;
        var originalPayorType = existingAggregate.PayorType;
        var updatedAggregate = CreateUpdatedBankAccountAggregate();


        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);



        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        // PayorId and PayorType should remain unchanged since they are immutable
        existingAggregate.PayorId.Should().Be(originalPayorId);
        existingAggregate.PayorType.Should().Be(originalPayorType);
    }

    [Fact]
    public async Task GIVEN_ValidCommand_WHEN_Handle_THEN_UpdateAllMutableProperties()
    {
        // Arrange
        var command = CreateValidCommand();
        var existingAggregate = CreateExistingBankAccountAggregate();
        var updatedAggregate = CreateUpdatedBankAccountAggregate();


        _repositoryMock
            .Setup(r => r.GetByIdAsync(command.Id, It.IsAny<CancellationToken>()))
            .ReturnsAsync(existingAggregate);

        _repositoryMock
            .Setup(r => r.UpdateAsync(existingAggregate, It.IsAny<CancellationToken>()))
            .ReturnsAsync(updatedAggregate);



        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        // Verify all mutable properties were updated
        existingAggregate.Country.Should().Be(command.Country);
        existingAggregate.Currency.Should().Be(command.Currency);
        existingAggregate.AccountHolderName.Should().Be(command.AccountHolderName);
        existingAggregate.BankName.Should().Be(command.BankName);
        existingAggregate.Bic.Should().Be(command.Bic);
        existingAggregate.AccountNumber.Should().Be(command.AccountNumber);
        existingAggregate.Iban.Should().Be(command.Iban);
        existingAggregate.UsedFor.Should().BeEquivalentTo(command.UsedFor);
    }



    private static UpdateBankAccountCommand CreateValidCommand()
    {
        return new UpdateBankAccountCommand
        {
            Id = "test-id",
            Country = "CA",
            Currency = "CAD",
            AccountHolderName = "Jane Smith",
            BankName = "Updated Bank",
            Bic = "UPDTCA44",
            UsedFor = new List<BankAccountUsage> { BankAccountUsage.Claim },
            AccountNumber = "**********",
            Iban = "CA**********987654321"
        };
    }

    private static BankAccount CreateExistingBankAccountAggregate()
    {
        return new BankAccount
        {
            Country = "US",
            Currency = "USD",
            AccountHolderName = "John Doe",
            BankName = "Original Bank",
            Bic = "ORIGUS33",
            UsedFor = new List<BankAccountUsage> { BankAccountUsage.Billing },
            PayorId = "payor-123",
            PayorType = PayorType.RiskCarrier,
            EntityId = "entity-123",
            AccountNumber = "**********",
            Iban = "US**********123456789"
        };
    }

    private static BankAccount CreateUpdatedBankAccountAggregate()
    {
        var aggregate = new BankAccount
        {
            Country = "CA",
            Currency = "CAD",
            AccountHolderName = "Jane Smith",
            BankName = "Updated Bank",
            Bic = "UPDTCA44",
            UsedFor = new List<BankAccountUsage> { BankAccountUsage.Claim },
            PayorId = "payor-123",
            PayorType = PayorType.RiskCarrier,
            EntityId = "entity-123",
            AccountNumber = "**********",
            Iban = "CA**********987654321"
        };
        return aggregate;
    }


}