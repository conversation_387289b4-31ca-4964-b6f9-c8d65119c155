﻿using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.Domain.PspSettings;

public class WalaaPspSettingsAggregate : PspSettingsAggregate
{
    public required string ApiUsername { get; set; }

    [Encrypted]
    public required string ApiPassword { get; set; }
    public required string WebhookUsername { get; set; }

    [Encrypted]
    public required string WebhookPassword { get; set; }

    public static string CurrencyCode => "682"; // SAR
}
