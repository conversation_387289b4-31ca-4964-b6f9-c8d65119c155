﻿using System.Text.Json;
using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.InitializePayment
{
    public class InitializePaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<InitializePaymentCommandHandler> logger)
        : ICommandHandler<InitializePaymentCommand, ProcessInitialPaymentResultDto>
    {
        /// <param name="initializePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<ProcessInitialPaymentResultDto> Handle(InitializePaymentCommand initializePaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("InitializePaymentCommandHandler.Handle: Starting payment initialization. InitializationToken: {InitializationToken}, PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}, Amount: {Amount} {CurrencyDesc}, InvoiceNumber: {InvoiceNumber}, HasDynamicFields: {HasDynamicFields}",
                initializePaymentCommand.InitializationToken ?? "null",
                initializePaymentCommand.PolicyId ?? "null",
                initializePaymentCommand.PayorId ?? "null",
                initializePaymentCommand.PaymentProvider?.ToString() ?? "null",
                initializePaymentCommand.Amount?.ToString() ?? "null",
                initializePaymentCommand.CurrencyDesc ?? "null",
                initializePaymentCommand.InvoiceNumber ?? "null",
                initializePaymentCommand.DynamicFields.HasValue);

            JsonDocument? dynamicFieldsDocument = null;
            try
            {
                PreauthPaymentAggregate payment;
                JsonElement? dynamicFields;
                if (initializePaymentCommand.InitializationToken != null)
                {
                    logger.LogInformation("InitializePaymentCommandHandler.Handle: Processing payment initialization using initialization token. InitializationToken: {InitializationToken}",
                        initializePaymentCommand.InitializationToken);

                    TokenizedPaymentInitializationAggregate? tokenizedPaymentInitializationAggregate =
                        await paymentService.GetTokenizedPaymentInitializationAsync(
                            initializePaymentCommand.InitializationToken,
                            throwIfNotFound: true,
                            cancellationToken);

                    tokenizedPaymentInitializationAggregate!.ThrowIfCanceled();

                    // Check if an existing payment can be reused
                    var existingPaymentResult = await ProcessPaymentWithInitializationTokenAsync(
                        tokenizedPaymentInitializationAggregate,
                        cancellationToken);

                    if (existingPaymentResult != null)
                    {
                        return existingPaymentResult;
                    }

                    payment = new PreauthPaymentAggregate(
                        tokenizedPaymentInitializationAggregate.PaymentProvider,
                        new PaymentMoney(
                            tokenizedPaymentInitializationAggregate.PaymentCurrencyCode,
                            tokenizedPaymentInitializationAggregate.PaymentCurrencyDesc,
                            tokenizedPaymentInitializationAggregate.PaymentAmount,
                            tokenizedPaymentInitializationAggregate.PaymentDecimalPrecision
                        ),
                        tokenizedPaymentInitializationAggregate.PolicyId,
                        tokenizedPaymentInitializationAggregate.InvoiceNumber,
                        tokenizedPaymentInitializationAggregate.PayorId,
                        null,
                        initializePaymentCommand.InitializationToken,
                        false,
                        tokenizedPaymentInitializationAggregate.IsCardUpdate ?? false
                    );

                    dynamicFieldsDocument = string.IsNullOrEmpty(tokenizedPaymentInitializationAggregate.DynamicFields)
                        ? null
                        : JsonDocument.Parse(tokenizedPaymentInitializationAggregate.DynamicFields);
                    dynamicFields = dynamicFieldsDocument?.RootElement;
                }
                else
                {
                    logger.LogInformation("InitializePaymentCommandHandler.Handle: Processing payment initialization with direct parameters. PolicyId: {PolicyId}, PayorId: {PayorId}",
                        initializePaymentCommand.PolicyId,
                        initializePaymentCommand.PayorId);

                    payment = new PreauthPaymentAggregate(
                        initializePaymentCommand.PaymentProvider!.Value,
                        new PaymentMoney(
                            initializePaymentCommand.CurrencyCode!,
                            initializePaymentCommand.CurrencyDesc!,
                            initializePaymentCommand.Amount!.Value,
                            initializePaymentCommand.DecimalPrecision!.Value
                        ),
                        initializePaymentCommand.PolicyId!,
                        initializePaymentCommand.InvoiceNumber!,
                        initializePaymentCommand.PayorId!,
                        null
                    );

                    dynamicFields = initializePaymentCommand.DynamicFields;
                }

                string? dynamicFieldsStr = dynamicFields?.GetRawText();
                if (!string.IsNullOrWhiteSpace(dynamicFieldsStr))
                {
                    payment.SetDynamicFields(dynamicFieldsStr);
                }

                ProcessInitialPaymentResult processInitialPaymentResult =
                    await paymentService.ProcessInitialPaymentAsync(
                        payment,
                        dynamicFields,
                        cancellationToken
                    );

                ProcessInitialPaymentResultDto? result =
                    mapper.Map<ProcessInitialPaymentResultDto>(processInitialPaymentResult);

                logger.LogInformation("InitializePaymentCommandHandler.Handle: Payment initialization completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}, HasRedirectUrl: {HasRedirectUrl}",
                    processInitialPaymentResult.Payment.Id,
                    processInitialPaymentResult.Payment.PolicyId,
                    processInitialPaymentResult.Payment.PayorId,
                    processInitialPaymentResult.Payment.Status,
                    processInitialPaymentResult.RedirectUrl != null);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "InitializePaymentCommandHandler.Handle: Unexpected error during payment initialization. InitializationToken: {InitializationToken}, PolicyId: {PolicyId}, PayorId: {PayorId}",
                    initializePaymentCommand.InitializationToken ?? "null",
                    initializePaymentCommand.PolicyId ?? "null",
                    initializePaymentCommand.PayorId ?? "null");
                throw;
            }
            finally
            {
                dynamicFieldsDocument?.Dispose();
            }
        }

        /// <summary>
        /// Processes payment initialization with an initialization token, checking for existing payments to reuse.
        /// </summary>
        /// <param name="tokenPayment">The initialization token</param>
        /// <param name="cancellationToken">Cancellation token</param>
        /// <returns>ProcessInitialPaymentResultDto if existing payment found and reused, null if no existing payment found</returns>
        private async Task<ProcessInitialPaymentResultDto?> ProcessPaymentWithInitializationTokenAsync(
            TokenizedPaymentInitializationAggregate tokenPayment,
            CancellationToken cancellationToken)
        {
            try
            {
                // Check if a payment with the same invoice number and initialization token already exists
                if (!string.IsNullOrWhiteSpace(tokenPayment.InvoiceNumber))
                {
                    var existingPayment = await paymentService.FindExistingPaymentAsync(
                        tokenPayment.PolicyId,
                        tokenPayment.PayorId,
                        tokenPayment.InvoiceNumber,
                        tokenPayment.Id,
                        cancellationToken);

                    if (existingPayment != null)
                    {
                        JsonElement? dynamicFields = string.IsNullOrEmpty(tokenPayment.DynamicFields)
                        ? null
                        : JsonDocument.Parse(tokenPayment.DynamicFields)?.RootElement;

                        var processInitialPaymentResult = await paymentService.ProcessInitWithExistingPaymentAsync(
                            existingPayment,
                            dynamicFields,
                            cancellationToken);

                        var existingResultDto = mapper.Map<ProcessInitialPaymentResultDto>(processInitialPaymentResult);

                        logger.LogInformation("InitializePaymentCommandHandler.ProcessPaymentWithInitializationTokenAsync: Payment initialization completed using existing payment. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}, HasRedirectUrl: {HasRedirectUrl}",
                            existingPayment.Id,
                            existingPayment.PolicyId,
                            existingPayment.PayorId,
                            existingPayment.Status,
                            processInitialPaymentResult.RedirectUrl != null);

                        return existingResultDto;
                    }
                }

                // No existing payment found, continue with new payment creation
                return null; 
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "InitializePaymentCommandHandler.ProcessPaymentWithInitializationTokenAsync: Error processing payment with initialization token. InitializationToken: {InitializationToken}",
                    tokenPayment.Id);
                throw;
            }
        }


    }
}