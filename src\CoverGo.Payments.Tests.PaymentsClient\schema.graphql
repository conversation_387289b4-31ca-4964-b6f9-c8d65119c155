schema {
  query: Query
  mutation: Mutation
}

type Query {
  payment(id: IdInput!): PaymentAggregate!
  payments(skip: Int take: Int where: PaymentsWhereInput): PagedResultOfPaymentAggregate!
  tokenizedPaymentInitializationPublicFields(initializationToken: String!): [KeyValuePairOfStringAndString!]!
}

enum ApplyPolicy {
  BEFORE_RESOLVER
  AFTER_RESOLVER
  VALIDATION
}

type PaymentAggregate {
  initialBearer: PspBearerUnionType
  reason: String
  transactionType: String
  description: String!
  calculateRefundedAmount: Decimal!
  policyId: String!
  invoiceNumber: String
  paymentMethod: String
  payorId: String!
  payerData: PayerData
  providerPaymentId: String
  paymentProvider: PaymentProvider!
  status: PaymentStatus
  type: PaymentType
  money: PaymentMoney!
  internalReference: String!
  externalReference: String
  effectiveDate: DateTime
  isSuccessful: Boolean!
  isFailure: Boolean!
  id: String!
}

union PspBearerUnionType = PSPBearer | PSPBearerPseudoCC

type PSPBearer {
  typeName: String
}

type PSPBearerPseudoCC {
  pseudoCardPan: String
  cardType: String
  country: String
  expiryMonth: Int
  expiryYear: Int
  holder: String
  truncatedCardPan: String
}

"The built-in `Decimal` scalar type."
scalar Decimal

type PayerData {
  companyName: String
  externalCustomerId: String
  firstName: String
  lastName: String
  address: Address
  emailAddress: String
  language: String
  phoneNumber: String
  customerName: String!
}

type Address {
  addressLine1: String
  addressLine2: String
  street: String
  houseNumber: String
  postalCode: String
  city: String
  state: String
  country: String
}

enum PaymentProvider {
  NONE
  FAKE
  MONERIS
  STRIPE
  AMAZON
  PAY_U
  WALAA
  ING
  EXTERNAL_FILE
}

enum PaymentStatus {
  CREATED
  IN_PROGRESS
  PREPARED
  PRELIMINARY_SUCCEEDED
  SUCCEEDED
  UNDEFINED
  CANCELED
  CHARGEBACK
  PENDING
  FAILED
  UNMAPPED
  REFUNDED
  THREE_D_SECURE_PENDING
  PARTIALLY_REFUNDED
  PARTIAL_CHARGEBACK
  IN_DISPUTE
  FINALIZING
  SCHEDULED
  PARTIALLY_PAID
  SUCCESSFULLY_IMPORTED
  OVER_PAID
  EXPIRED
}

enum PaymentType {
  NONE
  INITIAL
  RECURRING
  REFUND
  RECEIPT
}

type PaymentMoney {
  paymentCurrencyCode: String!
  paymentCurrencyDesc: String!
  paymentAmount: Decimal!
  paymentDecimalPrecision: Int!
  paymentMcDetails: MCPDetails
}

type MCPDetails {
  mcpMerchantSettlementAmount: String
  mcpCurrencyCode: String
  mcpRate: String
  mcpDecimalPrecision: Int
  mcpAmount: String
  mcpCurrencyDesc: String
}

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime

input IdInput {
  value: String!
}

type PagedResultOfPaymentAggregate {
  totalCount: Long!
  items: [PaymentAggregate!]!
}

"The `Long` scalar type represents non-fractional signed whole 64-bit numeric values. Long can represent values between -(2^63) and 2^63 - 1."
scalar Long

input PaymentsWhereInput {
  and: [PaymentsWhereInput!]
  or: [PaymentsWhereInput!]
  transactionType: TransactionTypeWhereInput
  paymentSource: PaymentSourceWhereInput
  status: PaymentStatusWhereInput
  effectiveDate: DateTimeWhereInput
}

input TransactionTypeWhereInput {
  in: [PaymentType]
}

input PaymentSourceWhereInput {
  in: [PaymentProvider!]
}

input PaymentStatusWhereInput {
  in: [PaymentStatus]
}

input DateTimeWhereInput {
  gte: DateTime
  lt: DateTime
}

type KeyValuePairOfStringAndString {
  key: String!
  value: String!
}

type Mutation {
  cancelPaymentInitializations(input: CancelPaymentInitializationsInput!): CancelPaymentInitializationsPayload!
  cancelPayment(input: CancelPaymentInput!): CancelPaymentPayload!
  capturePayment(input: CapturePaymentInput!): CapturePaymentPayload!
  failPayment(input: FailPaymentInput!): FailPaymentPayload!
  finalizePayment(input: FinalizePaymentInput!): FinalizePaymentPayload!
  initializePayment(input: InitializePaymentInput!): InitializePaymentPayload!
  initializeUpdatePayment(input: InitializeUpdatePaymentInput!): InitializeUpdatePaymentPayload!
  recurringPayment(input: RecurringPaymentInput!): RecurringPaymentPayload!
  registerPayment(input: RegisterPaymentInput!): RegisterPaymentPayload!
  tokenizePaymentInitialization(input: TokenizePaymentInitializationInput!): TokenizePaymentInitializationPayload!
  createPspSettings(input: CreatePspSettingsInput!): CreatePspSettingsPayload!
  encryptPspSettings(input: EncryptPspSettingsInput!): EncryptPspSettingsPayload!
  refundPayment(input: RefundPaymentInput!): RefundPaymentPayload!
}

type CancelPaymentInitializationsPayload {
  paymentInitializationsCancellationResult: PaymentInitializationsCancellationResultDto
  errors: [CancelPaymentInitializationsError!]
}

type PaymentInitializationsCancellationResultDto {
  isSuccessful: Boolean!
}

union CancelPaymentInitializationsError = InputDataValidationError | DomainError

type InputDataValidationError implements UserError {
  errors: [FieldValidationError!]!
  message: String!
  code: String!
}

interface UserError {
  message: String!
  code: String!
}

type DomainError implements UserError {
  message: String!
  code: String!
}

type PspSettingsAlreadyExistsError implements UserError {
  message: String!
  code: String!
}

interface FieldValidationError {
  propertyPath: String!
  message: String!
  code: String!
}

type FieldError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

type FieldRequiredError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

type FieldUniqueError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

input CancelPaymentInitializationsInput {
  initializationTokens: [String!]!
}

type CancelPaymentPayload {
  payment: PaymentDto
  errors: [CancelPaymentError!]
}

type PaymentDto {
  id: String!
  money: MoneyDto!
  paymentProvider: PaymentProvider!
  internalReference: String!
  externalReference: String
  paymentStatus: PaymentStatus
  auditInfo: AuditInfo!
  refundedAmount: Decimal!
}

type MoneyDto {
  paymentCurrencyCode: String!
  paymentCurrencyDesc: String!
  paymentDecimalPrecision: Int!
  paymentAmount: Decimal!
}

type AuditInfo {
  createdAt: DateTime!
  createdBy: String!
  lastModifiedAt: DateTime
  lastModifiedBy: String
  deletedAt: DateTime
  deletedBy: String
}

union CancelPaymentError = InputDataValidationError | DomainError

input CancelPaymentInput {
  paymentId: String!
}

type CapturePaymentPayload {
  payment: PaymentDto
  errors: [CapturePaymentError!]
}

union CapturePaymentError = InputDataValidationError | DomainError

input CapturePaymentInput {
  paymentId: String!
}

type FailPaymentPayload {
  payment: PaymentDto
  errors: [FailPaymentError!]
}

union FailPaymentError = InputDataValidationError | DomainError

input FailPaymentInput {
  paymentId: String!
}

type FinalizePaymentPayload {
  payment: PaymentDto
  errors: [FinalizePaymentError!]
}

union FinalizePaymentError = InputDataValidationError | DomainError

input FinalizePaymentInput {
  paymentId: String!
  dynamicFields: JSON
}

scalar JSON

type InitializePaymentPayload {
  initialPaymentResult: ProcessInitialPaymentResultDto
  errors: [InitializePaymentError!]
}

type ProcessInitialPaymentResultDto {
  payment: PaymentDto!
  redirectUrl: URL
  data: [KeyValuePairOfStringAndString!]
}

scalar URL

union InitializePaymentError = InputDataValidationError | DomainError

input InitializePaymentInput {
  amount: Decimal
  currencyCode: String
  currencyDesc: String
  decimalPrecision: Int
  policyId: String
  invoiceNumber: String
  payorId: String
  paymentProvider: PaymentProvider
  dynamicFields: JSON
  initializationToken: String
}

type InitializeUpdatePaymentPayload {
  initializeUpdatePaymentResult: ProcessInitialPaymentResultDto
  errors: [InitializeUpdatePaymentError!]
}

union InitializeUpdatePaymentError = InputDataValidationError | DomainError

input InitializeUpdatePaymentInput {
  currencyDesc: String!
  policyId: String!
  payorId: String!
  paymentProvider: PaymentProvider!
}

type RecurringPaymentPayload {
  payment: PaymentDto
  errors: [RecurringPaymentError!]
}

union RecurringPaymentError = InputDataValidationError | DomainError

input RecurringPaymentInput {
  amount: Decimal!
  currencyDesc: String!
  decimalPrecision: Int!
  policyId: String!
  invoiceNumber: String!
  payorId: String!
}

type RegisterPaymentPayload {
  payment: PaymentDto
  errors: [RegisterPaymentError!]
}

union RegisterPaymentError = InputDataValidationError | DomainError

input RegisterPaymentInput {
  paymentProvider: PaymentProvider!
  amount: Decimal!
  currencyCode: String!
  effectiveDate: DateTime!
  policyId: String!
  invoiceNumber: String
  payorId: String!
  status: String
  paymentMethod: String
  error: String
  dynamicFields: JSON
  transactionType: String!
}

type TokenizePaymentInitializationPayload {
  tokenizedPaymentInitialization: TokenizedPaymentInitializationDto
  errors: [TokenizePaymentInitializationError!]
}

type TokenizedPaymentInitializationDto {
  initializationToken: String!
}

union TokenizePaymentInitializationError = InputDataValidationError | DomainError

input TokenizePaymentInitializationInput {
  amount: Decimal!
  currencyCode: String!
  currencyDesc: String!
  decimalPrecision: Int!
  policyId: String!
  invoiceNumber: String!
  payorId: String!
  paymentProvider: PaymentProvider!
  dynamicFields: JSON
  publicFields: JSON
}

type CreatePspSettingsPayload {
  result: ResultDto
  errors: [CreatePspSettingsError!]
}

type ResultDto {
  success: Boolean!
  id: String
}

union CreatePspSettingsError = InputDataValidationError | PspSettingsAlreadyExistsError | DomainError

input CreatePspSettingsInput {
  paymentProvider: PaymentProvider!
  pspSettings: JSON!
}

type EncryptPspSettingsPayload {
  result: String
  errors: [EncryptPspSettingsError!]
}

union EncryptPspSettingsError = DomainError

input EncryptPspSettingsInput {
  paymentProvider: PaymentProvider
}

type RefundPaymentPayload {
  payment: PaymentRefundDto
  errors: [RefundPaymentError!]
}

type PaymentRefundDto {
  id: String!
  paymentId: String!
  status: RefundStatus!
  money: MoneyDto!
  createdAtDateUtc: DateTime!
  providerPaymentId: String
}

enum RefundStatus {
  CREATED
  SUCCEEDED
  FAILED
  CANCELED
}

union RefundPaymentError = InputDataValidationError | DomainError

input RefundPaymentInput {
  paymentId: String!
  amount: Decimal!
  decimalPrecision: Int!
}