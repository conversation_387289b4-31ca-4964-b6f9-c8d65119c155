﻿using System.Globalization;
using System.Net.Http.Headers;
using System.Text.Json;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Exceptions;
using CoverGo.Payments.Infrastructure.Payments.Walaa.Models;
using GuardClauses;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace CoverGo.Payments.Infrastructure.Payments.Walaa;

public class WalaaPaymentProviderService(
    ILogger<WalaaPaymentProviderService> logger,
    IWalaaCentralizedPaymentClient walaaCentralizedPaymentClient,
    IHttpContextAccessor httpContextAccessor) : BasePaymentProviderService(logger)
{
    private static readonly DateOnly DateInThePast = new(2024, 8, 31);

    public override PaymentProvider Type => PaymentProvider.Walaa;

    public override async Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields, CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        try
        {
            logger.LogInformation("Starting pre-processing for payment ID: {PaymentId}", payment.Id);

            WalaaPspSettingsAggregate? pspSettings = GetPspSettings<WalaaPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            if (payment.Money.PaymentCurrencyCode != WalaaPspSettingsAggregate.CurrencyCode)
                throw new DomainException("Unsupported currency.");
            if (dynamicFields == null)
                throw new DomainException("Dynamic fields are required for this payment provider.");

            payment.SetDynamicFields(dynamicFields);

            InvoiceUploadRequest invoiceUploadRequest = CreateInvoiceUploadRequest(payment, dynamicFields.Value);

            if (string.IsNullOrEmpty(invoiceUploadRequest.InvoiceDetails![0].InvoiceNumber))
                throw new DomainException("The invoice number is missing in the invoice details.");
            payment.SetExternalReference(invoiceUploadRequest.InvoiceDetails[0].InvoiceNumber!);

            InvoiceUploadResponse invoiceUploadResponse = await walaaCentralizedPaymentClient.InvoiceUploadAsync(
                invoiceUploadRequest,
                new Uri(pspSettings!.ApiUrl),
                pspSettings.ApiUsername,
                pspSettings.ApiPassword,
                cancellationToken);

            if (!invoiceUploadResponse.IsSuccess)
            {
                string error = JsonConvert.SerializeObject(invoiceUploadResponse.Errors);
                payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, error);

                logger.LogError("Failed to upload the invoice: {errors}",
                    JsonConvert.SerializeObject(invoiceUploadResponse.Errors));

                throw new DomainException("Failed to upload the invoice.");
            }

            if (invoiceUploadResponse.InvoiceDetails?.Length != 1)
                throw new DomainException("Unsupported amount of invoice details.");
            if (string.IsNullOrEmpty(invoiceUploadResponse.InvoiceDetails[0].PaymentLink))
                throw new DomainException("The payment link is missing in the invoice upload response.");
            if (string.IsNullOrEmpty(invoiceUploadResponse.InvoiceDetails[0].LinkExpiryDate))
                throw new DomainException("The link expiry date is missing in the invoice upload response.");

            logger.LogInformation("Pre-processing completed for payment ID: {PaymentId}", payment.Id);

            return new RedirectUrlOutput(new Uri(invoiceUploadResponse.InvoiceDetails[0].PaymentLink!),
                new Dictionary<string, string>
                {
                    { "linkExpiryDate", invoiceUploadResponse.InvoiceDetails[0].LinkExpiryDate! }
                });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-processing for payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private static InvoiceUploadRequest CreateInvoiceUploadRequest(PaymentAggregate payment, JsonElement dynamicFields)
    {
        InvoiceUploadRequest? request = JsonSerializer.Deserialize<InvoiceUploadRequest>(dynamicFields);

        GuardClause.ArgumentIsNotNull(request, nameof(request));
        GuardClause.ArgumentIsNotNull(request!.InvoiceDetails, nameof(request.InvoiceDetails));
        if (request.InvoiceDetails!.Length != 1) throw new DomainException("Unsupported amount of invoice details.");

        request.TotalAmount = payment.Money.PaymentAmount.ToString("F", CultureInfo.InvariantCulture);
        request.InvoiceDetails[0].InvoiceAmount = request.TotalAmount;

        return request;
    }

    public override async Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        try
        {
            logger.LogInformation("Cancelling payment ID: {PaymentId}", payment.Id);

            if (string.IsNullOrEmpty(payment.ExternalReference))
                throw new DomainException("Unable to find the invoice number.");

            WalaaPspSettingsAggregate? pspSettings = GetPspSettings<WalaaPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            UpdateInvoiceExpiryDateResponse updateInvoiceExpiryDateResponse =
                await walaaCentralizedPaymentClient.UpdateInvoiceExpiryDateAsync(
                    new UpdateInvoiceExpiryDateRequest
                    {
                        InvoiceId = payment.ExternalReference, ExpiryDate = DateInThePast
                    },
                    new Uri(pspSettings!.ApiUrl),
                    pspSettings.ApiUsername,
                    pspSettings.ApiPassword,
                    cancellationToken);

            if (!updateInvoiceExpiryDateResponse.IsSuccess)
            {
                logger.LogError("Failed to cancel the payment: Failed response (code {code}, message {message})",
                    updateInvoiceExpiryDateResponse.Code,
                    updateInvoiceExpiryDateResponse.Message);
                throw new DomainException("Failed to cancel the payment.");
            }

            payment.AddPaymentStatusHistoryItem(PaymentStatus.Canceled, payment.Money);

            logger.LogInformation("Cancellation completed for payment ID: {PaymentId}", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error when cancelling payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    public override Task<CapturePaymentAggregate> CapturePaymentAsync(CapturePaymentAggregate capturePayment,
        string providerPaymentId, CancellationToken cancellationToken = default)
        => throw new InvalidOperationException();

    public override Task<RecurringPaymentAggregate> RecurringPaymentAsync(RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default)
        => throw new InvalidOperationException();

    public override Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
        => throw new InvalidOperationException();

    public override Task FinalizePaymentAsync(PreauthPaymentAggregate payment, PreauthPaymentAggregate? prevPayment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Starting finalizing payment with ID: {PaymentId}", payment.Id);

        GuardClause.ArgumentIsNotNull(payment, nameof(payment));

        PaymentStatusHistoryItem? lastWebhookStatusHistoryItem =
            payment.PaymentStatusHistoryItems.LastOrDefault(i =>
                !string.IsNullOrEmpty(i.WebhookBody) && i.Status == PaymentStatus.Finalizing);
        GuardClause.ArgumentIsNotNull(lastWebhookStatusHistoryItem, nameof(lastWebhookStatusHistoryItem));

        payment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, lastWebhookStatusHistoryItem!.Money);

        logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);

        return Task.CompletedTask;
    }

    public override Task<RefundAggregate> RefundAsync(PaymentAggregate payment, RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default)
        => throw new InvalidOperationException();

    public override async
        Task<(string paymentId, string providerPaymentId, string externalReference, string internalReference,
            PaymentStatus paymentStatus, decimal
            ? amount, string webhookAcknowledgeMessage, bool isFinalizationRequired, bool skip)> HandleWebhookAsync(
            string webhookBody, PspSettingsAggregate pspSettings, CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Handling the webhook");

        var walaaPspSettingsAggregate = pspSettings as WalaaPspSettingsAggregate;
        GuardClause.ArgumentIsNotNull(walaaPspSettingsAggregate, nameof(walaaPspSettingsAggregate));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(walaaPspSettingsAggregate!.WebhookUsername,
            nameof(walaaPspSettingsAggregate.WebhookUsername));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(walaaPspSettingsAggregate.WebhookPassword,
            nameof(walaaPspSettingsAggregate.WebhookPassword));
        BasicAuthenticationHeaderValue expectedAuthHeader = new(walaaPspSettingsAggregate.WebhookUsername,
            walaaPspSettingsAggregate.WebhookPassword);

        HttpContext httpContext = httpContextAccessor.HttpContext ??
                                  throw new InvalidOperationException("No active HTTP context.");

        if (!AuthenticationHeaderValue.TryParse(httpContext.Request.Headers.Authorization, out var authorizationHeader)
            || authorizationHeader.Scheme != expectedAuthHeader.Scheme ||
            authorizationHeader.Parameter != expectedAuthHeader.Parameter)
        {
            logger.LogInformation("Failed to handle the webhook: Unauthorized");
            throw new UnauthorizedException();
        }

        WebhookRequest? webhookRequest = JsonConvert.DeserializeObject<WebhookRequest>(webhookBody);
        if (webhookRequest == null || string.IsNullOrEmpty(webhookRequest.InvoiceNumber))
        {
            logger.LogInformation("Failed to handle the webhook: Invalid webhook body {webhookBody}", webhookBody);
            return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Invalid webhook body",
                false, false);
        }

        VerifyPaymentResponse verifyPaymentResponse = await walaaCentralizedPaymentClient.VerifyPaymentAsync(
            new VerifyPaymentRequest { InvoiceId = webhookRequest.InvoiceNumber },
            new Uri(walaaPspSettingsAggregate!.ApiUrl),
            walaaPspSettingsAggregate.ApiUsername,
            walaaPspSettingsAggregate.ApiPassword,
            cancellationToken);

        if (!verifyPaymentResponse.IsSuccess
            || verifyPaymentResponse.Data?.InvoiceId != webhookRequest.InvoiceNumber
            || !decimal.TryParse(verifyPaymentResponse.Data.Amount, CultureInfo.InvariantCulture, out decimal amount))
        {
            logger.LogInformation(
                "Failed to handle the webhook for invoice number {invoiceNumber}: Failed payment (code {code}, message {message}, status {status}, invoice ID {invoiceId}, amount {amount})",
                webhookRequest.InvoiceNumber,
                verifyPaymentResponse.Code,
                verifyPaymentResponse.Message,
                verifyPaymentResponse.Data?.Status,
                verifyPaymentResponse.Data?.InvoiceId,
                verifyPaymentResponse.Data?.Amount);
            return (string.Empty, string.Empty, string.Empty, string.Empty, PaymentStatus.Failed, null,
                "Failed payment", false,
                false);
        }

        return (string.Empty, string.Empty, webhookRequest.InvoiceNumber, string.Empty, PaymentStatus.Finalizing,
            amount,
            string.Empty,
            isFinalizationRequired: true, false);
    }
}