using CoverGo.Payments.Application.PaymentMethods.Services;
using MediatR;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.PaymentMethods.Commands.CreatePaymentMethod;

public class CreatePaymentMethodCommandHandler : IRequestHandler<CreatePaymentMethodCommand>
{
    private readonly IPaymentMethodService _paymentMethodService;
    private readonly ILogger<CreatePaymentMethodCommandHandler> _logger;

    public CreatePaymentMethodCommandHandler(IPaymentMethodService paymentMethodService,
         ILogger<CreatePaymentMethodCommandHandler> logger)
    {
        _paymentMethodService = paymentMethodService;
        _logger = logger;
    }

    public async Task Handle(CreatePaymentMethodCommand request, CancellationToken cancellationToken)
    {
        if (request.Payment.InitialBearer?.Token == null)
        {
            _logger.LogDebug("CreatePaymentMethodCommandHandler: Skipping PaymentMethod creation - InitialBearer.Token is null. PaymentId: {PaymentId}", request.Payment.Id);
            return;
        }

        _logger.LogInformation("CreatePaymentMethodCommandHandler: Creating PaymentMethod from payment. PaymentId: {PaymentId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
            request.Payment.Id, request.Payment.PayorId, request.Payment.PaymentProvider);

        await _paymentMethodService.CreatePayorPaymentMethodAsync(request.Payment, cancellationToken);

        _logger.LogInformation("CreatePaymentMethodCommandHandler: Successfully created PaymentMethod. PaymentId: {PaymentId}, PayorId: {PayorId}",
               request.Payment.Id, request.Payment.PayorId);
    }
}