﻿using CoverGo.Payments.Application.Encryption;
using MongoDB.Bson.Serialization;
using MongoDB.Bson;
using System.Text.Json;
using System.Security.Cryptography;

namespace CoverGo.Payments.Infrastructure.DataAccess
{
    public class EncryptedFieldSerializer<T> : IBsonSerializer<T>
    {
        private readonly IEncryptionService _encryptionService;

        public EncryptedFieldSerializer(IEncryptionService encryptionService)
        {
            _encryptionService = encryptionService ?? throw new ArgumentNullException(nameof(encryptionService));
        }

        public Type ValueType => typeof(T);

        public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, T value)
        {
            if (value == null)
            {
                context.Writer.WriteNull();
                return;
            }

            var json = JsonSerializer.Serialize(value);
            var encryptedJson = _encryptionService.Encrypt(json);
            context.Writer.WriteString(encryptedJson);
        }

        public T Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            var bsonType = context.Reader.GetCurrentBsonType();
            if (bsonType == BsonType.Null)
            {
                context.Reader.ReadNull();
                return default!;
            }

            if (context.Reader.GetCurrentBsonType() == BsonType.String)
            {
                var encryptedString = context.Reader.ReadString();
                string decryptedValue = string.Empty;
                try
                {
                    decryptedValue = _encryptionService.Decrypt(encryptedString);
                }
                catch (Exception)
                {
                    decryptedValue = JsonSerializer.Serialize(encryptedString);
                }
                return JsonSerializer.Deserialize<T>(decryptedValue) ?? default!;
            }

            throw new NotSupportedException($"BSON type {context.Reader.GetCurrentBsonType()} is not supported for decryption.");
        }

        object? IBsonSerializer.Deserialize(BsonDeserializationContext context, BsonDeserializationArgs args)
        {
            return Deserialize(context, args);
        }

        public void Serialize(BsonSerializationContext context, BsonSerializationArgs args, object value)
        {
            Serialize(context, args, (T)value);
        }
    }
}
