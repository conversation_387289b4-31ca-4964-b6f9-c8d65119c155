using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Infrastructure.Payments;
using CoverGo.Payments.UnitTests.Payments;
using FluentAssertions;
using MediatR;
using Moq;
using Xunit;

namespace CoverGo.Payments.UnitTests.Payments.Enhanced
{
    public class EnhancedCancellationPaymentServiceTests : PaymentServiceTests
    {
        [Fact]
        public async Task GIVEN_valid_preauth_payment_WHEN_CancelPreauthPaymentAsync_with_expired_status_THEN_should_set_expired_status()
        {
            // Arrange
            var preauthPayment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null
            );
            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);

            _paymentRepositoryMock
                .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(preauthPayment);

            _paymentRepositoryMock
                .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

            // Act
            PreauthPaymentAggregate result = await _paymentService.CancelPreauthPaymentAsync(
                preauthPayment.Id, 
                PaymentStatus.Expired, 
                "Payment expired due to timeout", 
                CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Id.Should().Be(preauthPayment.Id);
            result.Status.Should().Be(PaymentStatus.Expired);
            result.PaymentStatusHistoryItem?.Error.Should().Be("Payment expired due to timeout");

            _paymentRepositoryMock.Verify(
                repo => repo.UpdateAsync(It.Is<PreauthPaymentAggregate>(p => 
                    p.Id == preauthPayment.Id && 
                    p.Status == PaymentStatus.Expired &&
                    p.PaymentStatusHistoryItem!.Error == "Payment expired due to timeout"), It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task GIVEN_initialization_tokens_WHEN_CancelPaymentInitializationsAsync_with_expired_status_THEN_should_send_commands_with_expired_status()
        {
            // Arrange
            var initializationToken1 = "token1";
            var initializationToken2 = "token2";
            var initializationTokens = new List<string> { initializationToken1, initializationToken2 };

            var tokenizedPaymentInit1 = new TokenizedPaymentInitializationAggregate(
                PaymentProvider.Stripe,
                1000m,
                "978",
                "EUR",
                2,
                null,
                null,
                "policy1",
                "invoice1",
                "payor1"
            );
            
            var tokenizedPaymentInit2 = new TokenizedPaymentInitializationAggregate(
                PaymentProvider.Stripe,
                2000m,
                "978",
                "EUR",
                2,
                null,
                null,
                "policy2",
                "invoice2",
                "payor2"
            );

            var preauthPayment1 = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null,
                initializationToken1
            );

            var preauthPayment2 = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 2000, 2),
                "policy2",
                "invoice2",
                "payor2",
                null,
                initializationToken2
            );

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.FindAllAsync(
                    It.Is<List<string>>(ids => ids.Contains(initializationToken1) && ids.Contains(initializationToken2)),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<TokenizedPaymentInitializationAggregate> { tokenizedPaymentInit1, tokenizedPaymentInit2 });

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.UpdateBatchAsync(It.IsAny<List<TokenizedPaymentInitializationAggregate>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _paymentRepositoryMock
                .Setup(repo => repo.FindAllByAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<PaymentAggregate, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PaymentAggregate> { preauthPayment1, preauthPayment2 });

            // Act
            await _paymentService.CancelPaymentInitializationsAsync(
                initializationTokens, 
                PaymentStatus.Expired, 
                "Payments expired", 
                CancellationToken.None);

            // Assert
            _mediatorMock.Verify(
                m => m.Send(It.Is<CancelPaymentCommand>(cmd => 
                    cmd.PaymentId == preauthPayment1.Id && 
                    cmd.CancellationStatus == PaymentStatus.Expired &&
                    cmd.CancellationReason == "Payments expired"), It.IsAny<CancellationToken>()),
                Times.Once);

            _mediatorMock.Verify(
                m => m.Send(It.Is<CancelPaymentCommand>(cmd => 
                    cmd.PaymentId == preauthPayment2.Id && 
                    cmd.CancellationStatus == PaymentStatus.Expired &&
                    cmd.CancellationReason == "Payments expired"), It.IsAny<CancellationToken>()),
                Times.Once);

            // Verify that the tokenized payment initializations were marked as expired
            _tokenizedPaymentInitializationRepositoryMock.Verify(
                repo => repo.UpdateBatchAsync(
                    It.Is<List<TokenizedPaymentInitializationAggregate>>(list => 
                        list.Count == 2 && 
                        list.All(a => a.IsExpired == true)),
                    It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Theory]
        [InlineData(PaymentStatus.Succeeded)]
        [InlineData(PaymentStatus.InProgress)]
        [InlineData(PaymentStatus.Pending)]
        public async Task GIVEN_invalid_cancellation_status_WHEN_CancelPreauthPaymentAsync_THEN_should_throw_domain_exception(PaymentStatus invalidStatus)
        {
            // Arrange
            var preauthPayment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null
            );

            _paymentRepositoryMock
                .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(preauthPayment);

            // Act & Assert
            var exception = await FluentActions
                .Invoking(() => _paymentService.CancelPreauthPaymentAsync(
                    preauthPayment.Id, 
                    invalidStatus, 
                    "Test reason", 
                    CancellationToken.None))
                .Should().ThrowAsync<DomainException>();

            exception.WithMessage($"Invalid cancellation status: {invalidStatus}*");
        }

        [Fact]
        public async Task GIVEN_backward_compatibility_WHEN_CancelPreauthPaymentAsync_without_status_THEN_should_default_to_canceled()
        {
            // Arrange
            var preauthPayment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null
            );
            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);

            _paymentRepositoryMock
                .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(preauthPayment);

            _paymentRepositoryMock
                .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

            // Act
            PreauthPaymentAggregate result = await _paymentService.CancelPreauthPaymentAsync(
                preauthPayment.Id, 
                cancellationToken: CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be(PaymentStatus.Canceled);
            result.PreauthStatus.Should().Be(PreauthPaymentStatus.Cancelled);
        }

        [Theory]
        [InlineData(PaymentStatus.Canceled, PreauthPaymentStatus.Cancelled)]
        [InlineData(PaymentStatus.Expired, PreauthPaymentStatus.Cancelled)]
        [InlineData(PaymentStatus.Failed, PreauthPaymentStatus.CancelFailed)]
        public async Task GIVEN_different_cancellation_statuses_WHEN_CancelPreauthPaymentAsync_THEN_should_map_to_correct_preauth_status(
            PaymentStatus cancellationStatus, 
            PreauthPaymentStatus expectedPreauthStatus)
        {
            // Arrange
            var preauthPayment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null
            );
            preauthPayment.AddPaymentStatusHistoryItem(PaymentStatus.Prepared, preauthPayment.Money);

            _paymentRepositoryMock
                .Setup(repo => repo.GetByIdAsync(preauthPayment.Id, It.IsAny<CancellationToken>()))
                .ReturnsAsync(preauthPayment);

            _paymentRepositoryMock
                .Setup(repo => repo.UpdateAsync(It.IsAny<PreauthPaymentAggregate>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync((PreauthPaymentAggregate payment, CancellationToken _) => payment);

            // Act
            PreauthPaymentAggregate result = await _paymentService.CancelPreauthPaymentAsync(
                preauthPayment.Id, 
                cancellationStatus, 
                "Test reason", 
                CancellationToken.None);

            // Assert
            result.Should().NotBeNull();
            result.Status.Should().Be(cancellationStatus);
            result.PreauthStatus.Should().Be(expectedPreauthStatus);
        }

        [Fact]
        public async Task GIVEN_initialization_tokens_WHEN_CancelPaymentInitializationsAsync_with_expired_status_THEN_should_call_expired_method()
        {
            // Arrange
            var initializationToken1 = "token1";
            var initializationToken2 = "token2";
            var initializationTokens = new List<string> { initializationToken1, initializationToken2 };

            var tokenizedPaymentInit1 = new TokenizedPaymentInitializationAggregate(
                PaymentProvider.Stripe,
                1000m,
                "978",
                "EUR",
                2,
                null,
                null,
                "policy1",
                "invoice1",
                "payor1"
            );
            
            var tokenizedPaymentInit2 = new TokenizedPaymentInitializationAggregate(
                PaymentProvider.Stripe,
                2000m,
                "978",
                "EUR",
                2,
                null,
                null,
                "policy2",
                "invoice2",
                "payor2"
            );

            var preauthPayment1 = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null,
                initializationToken1
            );

            var preauthPayment2 = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 2000, 2),
                "policy2",
                "invoice2",
                "payor2",
                null,
                initializationToken2
            );

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.FindAllAsync(
                    It.Is<List<string>>(ids => ids.Contains(initializationToken1) && ids.Contains(initializationToken2)),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<TokenizedPaymentInitializationAggregate> { tokenizedPaymentInit1, tokenizedPaymentInit2 });

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.UpdateBatchAsync(It.IsAny<List<TokenizedPaymentInitializationAggregate>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _paymentRepositoryMock
                .Setup(repo => repo.FindAllByAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<PaymentAggregate, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PaymentAggregate> { preauthPayment1, preauthPayment2 });

            // Act
            await _paymentService.CancelPaymentInitializationsAsync(
                initializationTokens, 
                PaymentStatus.Expired, 
                "Payment links expired", 
                CancellationToken.None);

            // Assert
            // Verify that Expired() was called on the aggregates
            tokenizedPaymentInit1.IsExpired.Should().BeTrue();
            tokenizedPaymentInit2.IsExpired.Should().BeTrue();

            // Verify that the batch update was called
            _tokenizedPaymentInitializationRepositoryMock.Verify(
                repo => repo.UpdateBatchAsync(
                    It.Is<List<TokenizedPaymentInitializationAggregate>>(list => 
                        list.Count == 2 && 
                        list.All(a => a.IsExpired == true)),
                    It.IsAny<CancellationToken>()),
                Times.Once);

            // Verify that CancelPaymentCommand was sent with correct status
            _mediatorMock.Verify(
                m => m.Send(It.Is<CancelPaymentCommand>(cmd => 
                    cmd.PaymentId == preauthPayment1.Id && 
                    cmd.CancellationStatus == PaymentStatus.Expired &&
                    cmd.CancellationReason == "Payment links expired"), It.IsAny<CancellationToken>()),
                Times.Once);

            _mediatorMock.Verify(
                m => m.Send(It.Is<CancelPaymentCommand>(cmd => 
                    cmd.PaymentId == preauthPayment2.Id && 
                    cmd.CancellationStatus == PaymentStatus.Expired &&
                    cmd.CancellationReason == "Payment links expired"), It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task GIVEN_initialization_tokens_WHEN_CancelPaymentInitializationsAsync_with_canceled_status_THEN_should_call_cancel_method()
        {
            // Arrange
            var initializationToken = "token1";
            var initializationTokens = new List<string> { initializationToken };

            var tokenizedPaymentInit = new TokenizedPaymentInitializationAggregate(
                PaymentProvider.Stripe,
                1000m,
                "978",
                "EUR",
                2,
                null,
                null,
                "policy1",
                "invoice1",
                "payor1"
            );

            var preauthPayment = new PreauthPaymentAggregate(
                PaymentProvider.Stripe,
                new PaymentMoney("978", "EUR", 1000, 2),
                "policy1",
                "invoice1",
                "payor1",
                null,
                initializationToken
            );

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.FindAllAsync(
                    It.Is<List<string>>(ids => ids.Contains(initializationToken)),
                    It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<TokenizedPaymentInitializationAggregate> { tokenizedPaymentInit });

            _tokenizedPaymentInitializationRepositoryMock
                .Setup(repo => repo.UpdateBatchAsync(It.IsAny<List<TokenizedPaymentInitializationAggregate>>(), It.IsAny<CancellationToken>()))
                .Returns(Task.CompletedTask);

            _paymentRepositoryMock
                .Setup(repo => repo.FindAllByAsync(It.IsAny<System.Linq.Expressions.Expression<System.Func<PaymentAggregate, bool>>>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(new List<PaymentAggregate> { preauthPayment });

            // Act
            await _paymentService.CancelPaymentInitializationsAsync(
                initializationTokens, 
                PaymentStatus.Canceled, 
                "Payment manually canceled", 
                CancellationToken.None);

            // Assert
            // Verify that Cancel() was called (not Expired())
            tokenizedPaymentInit.IsCanceled.Should().BeTrue();
            tokenizedPaymentInit.IsExpired.Should().NotBe(true);

            // Verify that the batch update was called
            _tokenizedPaymentInitializationRepositoryMock.Verify(
                repo => repo.UpdateBatchAsync(
                    It.Is<List<TokenizedPaymentInitializationAggregate>>(list => 
                        list.Count == 1 && 
                        list.All(a => a.IsCanceled == true)),
                    It.IsAny<CancellationToken>()),
                Times.Once);

            // Verify that CancelPaymentCommand was sent with correct status
            _mediatorMock.Verify(
                m => m.Send(It.Is<CancelPaymentCommand>(cmd => 
                    cmd.PaymentId == preauthPayment.Id && 
                    cmd.CancellationStatus == PaymentStatus.Canceled &&
                    cmd.CancellationReason == "Payment manually canceled"), It.IsAny<CancellationToken>()),
                Times.Once);
        }
    }
}
