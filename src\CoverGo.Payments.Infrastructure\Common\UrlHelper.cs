﻿using GuardClauses;

namespace CoverGo.Payments.Infrastructure.Common
{
    public static class UrlHelper
    {
        public static string ReplaceOrigin(string url, string newOrigin)
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(url, nameof(url));
            if (string.IsNullOrEmpty(newOrigin))
            {
                return url;
            }

            var originalUri = new Uri(url);
            var newOriginUri = new Uri(newOrigin);

            var updatedUrl = new UriBuilder(originalUri)
            {
                Scheme = newOriginUri.Scheme,
                Host = newOriginUri.Host,
                Port = newOriginUri.IsDefaultPort ? -1 : newOriginUri.Port
            };

            return updatedUrl.Uri.ToString();
        }
    }
}
