﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class Mcp
{
    [JsonProperty("merchant_settlement_amount")]
    public string? MerchantSettlementAmount { get; set; }

    [JsonProperty("cardholder_currency_code")]
    public string? CardholderCurrencyCode { get; set; }

    [JsonProperty("mcp_rate")]
    public string? McpRate { get; set; }

    [JsonProperty("decimal_precision")]
    public string? DecimalPrecision { get; set; }

    [JsonProperty("cardholder_amount")]
    public string? CardholderAmount { get; set; }

    [JsonProperty("cardholder_currency_desc")]
    public string? CardholderCurrencyDesc { get; set; }
}