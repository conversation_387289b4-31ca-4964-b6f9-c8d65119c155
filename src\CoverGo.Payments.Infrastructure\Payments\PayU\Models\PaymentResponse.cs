﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class PaymentResponse
{
    [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
    public int Code { get; set; }

    [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
    public string Status { get; set; }

    [JsonProperty(PayUContainer.PropsName.PaymentStatus, NullValueHandling = NullValueHandling.Ignore)]
    public string PaymentStatus { get; set; }

    [JsonProperty(PayUContainer.PropsName.Message, NullValueHandling = NullValueHandling.Ignore)]
    public string Message { get; set; }

    [JsonProperty(PayUContainer.PropsName.PayuPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public string PayuPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount, NullValueHandling = NullValueHandling.Ignore)]
    public double Amount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.PaymentResult, NullValueHandling = NullValueHandling.Ignore)]
    public PaymentResult PaymentResult { get; set; }

    [JsonProperty(PayUContainer.PropsName.Authorization, NullValueHandling = NullValueHandling.Ignore)]
    public AuthorizationResource Authorization { get; set; }

    [JsonProperty(PayUContainer.PropsName.Authorizations, NullValueHandling = NullValueHandling.Ignore)]
    public List<AuthorizationResource> Authorizations { get; set; }
}

public class PaymentResult
{
    [JsonProperty(PayUContainer.PropsName.PayuResponseCode, NullValueHandling = NullValueHandling.Ignore)]
    public string PayuResponseCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.AuthCode, NullValueHandling = NullValueHandling.Ignore)]
    public string AuthCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.Rrn, NullValueHandling = NullValueHandling.Ignore)]
    public string Rrn { get; set; }

    [JsonProperty(PayUContainer.PropsName.InstallmentsNumber, NullValueHandling = NullValueHandling.Ignore)]
    public string InstallmentsNumber { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardProgramName, NullValueHandling = NullValueHandling.Ignore)]
    public string CardProgramName { get; set; }

    [JsonProperty(PayUContainer.PropsName.BankResponseDetails, NullValueHandling = NullValueHandling.Ignore)]
    public BankResponseDetails BankResponseDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardDetails, NullValueHandling = NullValueHandling.Ignore)]
    public CardResponseDetails CardDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.Details3ds, NullValueHandling = NullValueHandling.Ignore)]
    public ThreeDsResponseDetails ThreeDsDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.Type, NullValueHandling = NullValueHandling.Ignore)]
    public string Type { get; set; }

    [JsonProperty(PayUContainer.PropsName.WireAccounts, NullValueHandling = NullValueHandling.Ignore)]
    public List<WireAccount> WireAccounts { get; set; }

    [JsonProperty(PayUContainer.PropsName.Url, NullValueHandling = NullValueHandling.Ignore)]
    public string Url { get; set; }
}

public class BankResponseDetails
{
    [JsonProperty(PayUContainer.PropsName.TerminalId, NullValueHandling = NullValueHandling.Ignore)]
    public string TerminalId { get; set; }

    [JsonProperty(PayUContainer.PropsName.Response, NullValueHandling = NullValueHandling.Ignore)]
    public BankResponse Response { get; set; }

    [JsonProperty(PayUContainer.PropsName.HostRefNum, NullValueHandling = NullValueHandling.Ignore)]
    public string HostRefNum { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantId, NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantId { get; set; }

    [JsonProperty(PayUContainer.PropsName.ShortName, NullValueHandling = NullValueHandling.Ignore)]
    public string ShortName { get; set; }

    [JsonProperty(PayUContainer.PropsName.TxRefNo, NullValueHandling = NullValueHandling.Ignore)]
    public string TxRefNo { get; set; }

    [JsonProperty(PayUContainer.PropsName.Oid, NullValueHandling = NullValueHandling.Ignore)]
    public string Oid { get; set; }

    [JsonProperty(PayUContainer.PropsName.TransId, NullValueHandling = NullValueHandling.Ignore)]
    public string TransId { get; set; }

    [JsonProperty(PayUContainer.PropsName.CustomBankNode, NullValueHandling = NullValueHandling.Ignore)]
    public CustomBankNode CustomBankNode { get; set; }
}

public class BankResponse
{
    [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
    public int Code { get; set; }

    [JsonProperty(PayUContainer.PropsName.Message, NullValueHandling = NullValueHandling.Ignore)]
    public string Message { get; set; }

    [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
    public string Status { get; set; }
}

public class CustomBankNode
{
    [JsonProperty(PayUContainer.PropsName.Qr, NullValueHandling = NullValueHandling.Ignore)]
    public string Qr { get; set; }

    [JsonProperty(PayUContainer.PropsName.Url, NullValueHandling = NullValueHandling.Ignore)]
    public string Url { get; set; }
}

public class CardResponseDetails
{
    [JsonProperty(PayUContainer.PropsName.Pan, NullValueHandling = NullValueHandling.Ignore)]
    public string Pan { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExpiryYear, NullValueHandling = NullValueHandling.Ignore)]
    public string ExpiryYear { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExpiryMonth, NullValueHandling = NullValueHandling.Ignore)]
    public string ExpiryMonth { get; set; }
}

public class ThreeDsResponseDetails
{
    [JsonProperty(PayUContainer.PropsName.MdStatus, NullValueHandling = NullValueHandling.Ignore)]
    public string MdStatus { get; set; }

    [JsonProperty(PayUContainer.PropsName.ErrorMessage, NullValueHandling = NullValueHandling.Ignore)]
    public string ErrorMessage { get; set; }

    [JsonProperty(PayUContainer.PropsName.TxStatus, NullValueHandling = NullValueHandling.Ignore)]
    public string TxStatus { get; set; }

    [JsonProperty(PayUContainer.PropsName.Xid, NullValueHandling = NullValueHandling.Ignore)]
    public string Xid { get; set; }

    [JsonProperty(PayUContainer.PropsName.Eci, NullValueHandling = NullValueHandling.Ignore)]
    public int? Eci { get; set; }

    [JsonProperty(PayUContainer.PropsName.Cavv, NullValueHandling = NullValueHandling.Ignore)]
    public int? Cavv { get; set; }
}

public class WireAccount
{
    [JsonProperty(PayUContainer.PropsName.BankIdentifier, NullValueHandling = NullValueHandling.Ignore)]
    public string BankIdentifier { get; set; }

    [JsonProperty(PayUContainer.PropsName.BankAccount, NullValueHandling = NullValueHandling.Ignore)]
    public string BankAccount { get; set; }

    [JsonProperty(PayUContainer.PropsName.RoutingNumber, NullValueHandling = NullValueHandling.Ignore)]
    public int? RoutingNumber { get; set; }

    [JsonProperty(PayUContainer.PropsName.IbanAccount, NullValueHandling = NullValueHandling.Ignore)]
    public string IbanAccount { get; set; }

    [JsonProperty(PayUContainer.PropsName.BankSwift, NullValueHandling = NullValueHandling.Ignore)]
    public string BankSwift { get; set; }

    [JsonProperty(PayUContainer.PropsName.Country, NullValueHandling = NullValueHandling.Ignore)]
    public string Country { get; set; }

    [JsonProperty(PayUContainer.PropsName.RecipientName, NullValueHandling = NullValueHandling.Ignore)]
    public string RecipientName { get; set; }

    [JsonProperty(PayUContainer.PropsName.RecipientVatId, NullValueHandling = NullValueHandling.Ignore)]
    public string RecipientVatId { get; set; }

    [JsonProperty(PayUContainer.PropsName.Url, NullValueHandling = NullValueHandling.Ignore)]
    public string Url { get; set; }
}

public class AuthorizationResource
{
    [JsonProperty(PayUContainer.PropsName.Timestamp, NullValueHandling = NullValueHandling.Ignore)]
    public string Timestamp { get; set; }

    [JsonProperty(PayUContainer.PropsName.Authorized, NullValueHandling = NullValueHandling.Ignore)]
    public string Authorized { get; set; }

    [JsonProperty(PayUContainer.PropsName.Credit, NullValueHandling = NullValueHandling.Ignore)]
    public Credit Credit { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardDetails, NullValueHandling = NullValueHandling.Ignore)]
    public CardDetails CardDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.StoredCredentials, NullValueHandling = NullValueHandling.Ignore)]
    public StoredCredentialsResponse? StoredCredentials { get; set; }

    [JsonProperty(PayUContainer.PropsName.ResponseCode, NullValueHandling = NullValueHandling.Ignore)]
    public string ResponseCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.ResponseMessage, NullValueHandling = NullValueHandling.Ignore)]
    public string ResponseMessage { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantPaymentAttemptReference,
        NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantPaymentAttemptReference { get; set; }
}

public class Credit
{
    [JsonProperty(PayUContainer.PropsName.NrInstalments, NullValueHandling = NullValueHandling.Ignore)]
    public int? NrInstalments { get; set; }

    [JsonProperty(PayUContainer.PropsName.FinancialPartner, NullValueHandling = NullValueHandling.Ignore)]
    public string FinancialPartner { get; set; }

    [JsonProperty(PayUContainer.PropsName.CampaignCode, NullValueHandling = NullValueHandling.Ignore)]
    public string CampaignCode { get; set; }
}

public partial class CardDetails
{
    [JsonProperty(PayUContainer.PropsName.CardScheme, NullValueHandling = NullValueHandling.Ignore)]
    public string CardScheme { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardType, NullValueHandling = NullValueHandling.Ignore)]
    public string CardType { get; set; }

    [JsonProperty(PayUContainer.PropsName.IssuerBank, NullValueHandling = NullValueHandling.Ignore)]
    public string IssuerBank { get; set; }

    [JsonProperty(PayUContainer.PropsName.IssuerCountryCode, NullValueHandling = NullValueHandling.Ignore)]
    public string IssuerCountryCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardProfile, NullValueHandling = NullValueHandling.Ignore)]
    public string CardProfile { get; set; }

    [JsonProperty(PayUContainer.PropsName.LastFourDigits, NullValueHandling = NullValueHandling.Ignore)]
    public string LastFourDigits { get; set; }

    [JsonProperty(PayUContainer.PropsName.BinNumber, NullValueHandling = NullValueHandling.Ignore)]
    public string BinNumber { get; set; }
}

public class StoredCredentialsResponse
{
    [JsonProperty(PayUContainer.PropsName.UseId, NullValueHandling = NullValueHandling.Ignore)]
    public string UseId { get; set; }
}