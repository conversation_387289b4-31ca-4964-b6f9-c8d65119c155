﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class Avs
{
    [JsonProperty("decision_origin")]
    public string? DecisionOrigin { get; set; }

    [JsonProperty("result")]
    public string? Result { get; set; }

    [JsonProperty("condition")]
    public string? Condition { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("code")]
    public string? Code { get; set; }

    [JsonProperty("details")]
    public string? Details { get; set; }
}