using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.CancelPaymentInitializations;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.CancelPaymentInitializations;

[MutationType]
public class CancelPaymentInitializationsMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "paymentInitializationsCancellationResult")]
    //[Authorize]
    public async Task<PaymentInitializationsCancellationResultDto> CancelPaymentInitializations(
        CancelPaymentInitializationsCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken
    ) => await commandProcessor.Send(input, cancellationToken);
}
