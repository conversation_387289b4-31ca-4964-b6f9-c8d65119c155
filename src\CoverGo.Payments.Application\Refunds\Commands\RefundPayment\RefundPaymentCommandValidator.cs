﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Refunds.Commands.RefundPayment
{
    public class RefundPaymentCommandValidator : AbstractValidator<RefundPaymentCommand>
    {
        public RefundPaymentCommandValidator(
            ILogger<RefundPaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PaymentId).NotEmpty().WithMessage("No paymentId found.");
            RuleFor(pc => pc.Amount).GreaterThan(decimal.Zero).WithMessage("Amount should be greater than 0.");
            RuleFor(pc => pc.DecimalPrecision).GreaterThan(0).WithMessage("DecimalPrecision should be greater than 0.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
