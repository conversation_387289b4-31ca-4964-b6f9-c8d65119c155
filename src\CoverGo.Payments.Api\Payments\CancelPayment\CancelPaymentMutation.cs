using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.CancelPayment;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.CancelPayment;

[MutationType]
public class CancelPaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "payment")]
    //[Authorize]
    public async Task<PaymentDto> CancelPayment(
        CancelPaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}