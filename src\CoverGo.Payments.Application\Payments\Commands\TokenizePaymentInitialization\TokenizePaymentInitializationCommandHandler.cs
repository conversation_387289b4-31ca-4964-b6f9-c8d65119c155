﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization
{
    public class TokenizePaymentInitializationCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<TokenizePaymentInitializationCommandHandler> logger)
        : ICommandHandler<TokenizePaymentInitializationCommand, TokenizedPaymentInitializationDto>
    {
        public async Task<TokenizedPaymentInitializationDto> Handle(TokenizePaymentInitializationCommand command,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("TokenizePaymentInitializationCommandHandler.Handle: Starting payment initialization tokenization. PolicyId: {PolicyId}, PayorId: {PayorId}, InvoiceNumber: {InvoiceNumber}, PaymentProvider: {PaymentProvider}, Amount: {Amount} {CurrencyCode} ({CurrencyDesc})",
                command.PolicyId,
                command.PayorId,
                command.InvoiceNumber ?? "null",
                command.PaymentProvider,
                command.Amount,
                command.CurrencyCode,
                command.CurrencyDesc);

            try
            {
                TokenizedPaymentInitializationAggregate aggregate = new(
                    command.PaymentProvider,
                    command.Amount,
                    command.CurrencyCode,
                    command.CurrencyDesc,
                    command.DecimalPrecision,
                    command.DynamicFields,
                    command.PublicFields,
                    command.PolicyId,
                    command.InvoiceNumber,
                    command.PayorId,
                command.IsCardUpdate ?? false
                );

                TokenizedPaymentInitialization tokenizedPaymentInitialization =
                    await paymentService.TokenizePaymentInitializationAsync(aggregate, cancellationToken);

                TokenizedPaymentInitializationDto? result =
                    mapper.Map<TokenizedPaymentInitializationDto>(tokenizedPaymentInitialization);

                logger.LogInformation("TokenizePaymentInitializationCommandHandler.Handle: Payment initialization tokenization completed. InitializationToken: {InitializationToken}, PolicyId: {PolicyId}, PayorId: {PayorId}",
                    tokenizedPaymentInitialization.InitializationToken,
                    command.PolicyId,
                    command.PayorId);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "TokenizePaymentInitializationCommandHandler.Handle: Unexpected error during payment initialization tokenization. PolicyId: {PolicyId}, PayorId: {PayorId}, PaymentProvider: {PaymentProvider}",
                    command.PolicyId,
                    command.PayorId,
                    command.PaymentProvider);
                throw;
            }
        }
    }
}