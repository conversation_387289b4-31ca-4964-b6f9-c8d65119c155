﻿using System.Net;
using System.Text.Json.Serialization;
using Moq;
using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Infrastructure.Payments.PayU;
using CoverGo.Payments.Infrastructure.Payments.PayU.Configurations;
using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq.Protected;
using Newtonsoft.Json;

namespace CoverGo.Payments.UnitTests.Payments.PayU;

public class BaseClientTests
{
    [Fact]
    public async Task ProcessAsync_ShouldSendHttpRequest_AndParseResponseCorrectly()
    {
        // Arrange
        var settings = new PayUClientSettings(
            "url",
            "apiVersion",
            "secretKey",
            "secretKey",
            "merchantCode");
        var mockHttpMessageHandler = new Mock<HttpMessageHandler>();
        var mockHttpClientFactory = new Mock<IHttpClientFactory>();
        var mockDateTimeProvider = new Mock<IDateTimeProvider>();
        var mockLogger = new Mock<ILogger>();

        mockHttpMessageHandler.SetupSendAsync(HttpStatusCode.OK, "{\"test\": \"response\"}");

        var httpClient = new HttpClient(mockHttpMessageHandler.Object);
        mockHttpClientFactory.Setup(factory => factory.CreateClient(It.IsAny<string>())).Returns(httpClient);

        var client = new DummyBaseClient(settings, mockHttpClientFactory.Object, mockDateTimeProvider.Object,
            mockLogger.Object);

        var testUri = new Uri("https://api.test.com/payments");
        HttpMethod method = HttpMethod.Post;
        object content = new { amount = 100 };

        // Act
        DummyResponse? result =
            await client.ProcessAsync<DummyResponse>(testUri, method, CancellationToken.None, content);

        // Assert
        result.Should().NotBeNull();
        mockHttpMessageHandler.VerifySendAsync(Times.Once());
        result?.Test.Should().Be("response");
    }

    private class DummyBaseClient : BaseClient
    {
        public DummyBaseClient(PayUClientSettings settings, IHttpClientFactory clientFactory,
            IDateTimeProvider dateTimeProvider, ILogger logger)
            : base(settings, clientFactory, dateTimeProvider, logger)
        {
        }

        public new Task<T?> ProcessAsync<T>(Uri requestUrl, HttpMethod httpMethod, CancellationToken ct,
            object? content = null)
            where T : class =>
            base.ProcessAsync<T>(requestUrl, httpMethod, ct, content);
    }

    private class DummyResponse
    {
        [JsonProperty("test")]
        [JsonPropertyName("test")]
        public string? Test { get; set; }
    }
}

public static class MockHttpMessageHandlerExtensions
{
    public static void SetupSendAsync(this Mock<HttpMessageHandler> mockHandler, HttpStatusCode statusCode,
        string responseBody) =>
        mockHandler.Protected().Setup<Task<HttpResponseMessage>>(
            "SendAsync",
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>()
        ).ReturnsAsync(new HttpResponseMessage { StatusCode = statusCode, Content = new StringContent(responseBody) });

    public static void VerifySendAsync(this Mock<HttpMessageHandler> mockHandler, Times times) =>
        mockHandler.Protected().Verify(
            "SendAsync",
            times,
            ItExpr.IsAny<HttpRequestMessage>(),
            ItExpr.IsAny<CancellationToken>()
        );
}