﻿using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization
{
    public record TokenizePaymentInitializationCommand(
        decimal Amount,
        string CurrencyCode,
        string CurrencyDesc,
        int DecimalPrecision,
        string PolicyId,
        string InvoiceNumber,
        string PayorId,
        bool? IsCardUpdate,
        PaymentProvider PaymentProvider,
        JsonElement? DynamicFields,
        JsonElement? PublicFields
    ) : ICommand<TokenizedPaymentInitializationDto>;
}
