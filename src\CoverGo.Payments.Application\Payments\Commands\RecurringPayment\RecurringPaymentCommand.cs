﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;

namespace CoverGo.Payments.Application.Payments.Commands.RecurringPayment
{
    public record RecurringPaymentCommand(
        decimal Amount,
        string CurrencyDesc,
        int DecimalPrecision,
        string PolicyId,
        string InvoiceNumber,
        string PayorId,
        string? RenewedFromPolicyId
        ) : ICommand<PaymentDto>;
}