using CoverGo.Payments.Api;
using HotChocolate;
using HotChocolate.Execution;
using Microsoft.Extensions.DependencyInjection;
using Snapshooter.Xunit;

namespace CoverGo.Payments.IntegrationTests;

public class GraphQlSchemaTests
{
    /// <summary>
    ///     This test fails when you update the schema, but don't update snapshot in source control.
    ///     Update the snapshot and check for any breaking changes!
    /// </summary>
    /// <remarks>
    ///     Steps to update GraphQL schema:
    ///     1. Run backend services in local
    ///     2. Run this test
    ///     3. Compare changes between files in `__snapshots__/__mismatch__GraphQlSchemaTests.SchemaChangeTest.snap` and
    ///     `__snapshots__/GraphQlSchemaTests.SchemaChangeTest.snap`, update them if need.
    /// </remarks>
    [Fact]
    public async Task SchemaChangeTest()
    {
        // Arrange (Given)
        IServiceCollection serviceCollection = new ServiceCollection()
            .AddLogging();

        // Act (When)
        ISchema schema = await serviceCollection
            .AddPaymentsGraphQlSchema()
            .BuildSchemaAsync();

        // Assert (Then)
        //schema.ToString().MatchSnapshot();
    }
}