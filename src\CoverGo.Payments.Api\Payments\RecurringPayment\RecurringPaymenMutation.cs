using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Payments.Commands.RecurringPayment;
using CoverGo.Payments.Application.Payments.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Payments.RecurringPayment;

[MutationType]
public class RecurringPaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "payment")]
    //[Authorize]
    public async Task<PaymentDto> RecurringPayment(
        RecurringPaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}