﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.Audit;
using CoverGo.BuildingBlocks.Domain.Core.Audit;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments;

public class PaymentMappingProfile : Profile
{
    public PaymentMappingProfile()
    {
        CreateMap<PaymentMoney, MoneyDto>().ReverseMap();
        CreateMap<RedirectUrlOutputDto, RedirectUrlOutput>().ReverseMap();
        CreateMap<EntityAuditInfo, AuditInfo>().ReverseMap();
        CreateMap<ProcessInitialPaymentResultDto, ProcessInitialPaymentResult>().ReverseMap();
        CreateMap<TokenizedPaymentInitializationDto, TokenizedPaymentInitialization>().ReverseMap();
        CreateMap<PaymentAggregate, PaymentDto>()
            .ForMember(dest => dest.AuditInfo, opt => opt.MapFrom(src => src.EntityAuditInfo))
            .ForMember(dest => dest.InternalReference, opt => opt.MapFrom(src => src.InternalReference))
            .ForMember(dest => dest.ExternalReference, opt => opt.MapFrom(src => src.ExternalReference))
            .ForMember(dest => dest.PaymentStatus, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.RefundedAmount, opt => opt.MapFrom(src => src.CalculateRefundedAmount()))
            .ReverseMap();
    }
}