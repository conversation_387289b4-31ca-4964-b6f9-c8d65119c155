﻿namespace CoverGo.Payments.Domain.Payment
{
    public enum PaymentStatus
    {
        Created = 0,
        InProgress = 1,
        Prepared = 2,
        PreliminarySucceeded = 3,
        Succeeded = 4,
        Undefined = 5,
        Canceled = 6,
        Chargeback = 7,
        Pending = 8,
        Failed = 9,
        Unmapped = 10,
        Refunded = 11,
        ThreeDSecurePending = 12,
        PartiallyRefunded = 13,
        PartialChargeback = 14,
        InDispute = 15,
        Finalizing = 16,
        Scheduled = 17,
        PartiallyPaid = 18,
        SuccessfullyImported = 19,
        OverPaid = 20,
        Expired = 21,
    }
}
