﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPayment
{
    public record CancelPaymentCommand(
        string PaymentId,
        PaymentStatus? CancellationStatus = null,
        string? CancellationReason = null
    ) : ICommand<PaymentDto>;
}