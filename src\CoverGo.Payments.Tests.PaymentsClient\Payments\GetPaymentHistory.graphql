﻿query GetPaymentHistory($skip: Int, $take: Int, $where: PaymentsWhereInput) {
    payments(skip: $skip, take: $take, where: $where) {
        totalCount
        items {
            id
            policyId
            payorId
            transactionType
            status
            effectiveDate
            paymentProvider
            money {
                paymentAmount
                paymentCurrencyCode
            }
            description
            reason
        }
    }
}