using CoverGo.BuildingBlocks.Bootstrapper.Configuration;
using CoverGo.BuildingBlocks.DataAccess.Mongo.Configuration;
using CoverGo.BuildingBlocks.DataAccess.Mongo.Multitenancy;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.BankAccount;
using CoverGo.Payments.Infrastructure.BankAccounts;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using MongoDB.Bson;
using MongoDB.Driver;

namespace CoverGo.Payments.Infrastructure.Indexing;

public static class IndexExtensions
{
    public static async Task IndexAsync(this WebApplication app, CancellationToken cancellationToken = default)
    {
        var dbConfig = app.Configuration.GetConfiguration<MongoDatabaseConfiguration>();
        var client = app.Services.GetRequiredService<IMongoClient>();
        var mongoDatabaseMultitenancyStrategy = app.Services.GetRequiredService<IMongoDatabaseMultitenancyStrategy>();
        var names = await client.ListDatabaseNamesAsync(
            new ListDatabaseNamesOptions { Filter = Builders<BsonDocument>.Filter.Regex("name", $@"^{dbConfig.DatabaseName}-") }, cancellationToken);
        await names.ForEachAsync(async dbName =>
        {
            var db = client.GetDatabase(dbName);
            var tenantId = dbName.Split('-').Last();
            await CreateIndexAsync<BankAccount, BankAccountIndexing>(app.Logger, db, "bank-accounts");
        }, cancellationToken);
    }

    private static async Task CreateIndexAsync<TAggregate, TIndexing>(
        ILogger logger,
        IMongoDatabase db,
        string collectionName) where TAggregate : AggregateRootBase<string> where TIndexing : IIndexing
    {
        var collection = db.GetCollection<TAggregate>(collectionName);
        var constructor = typeof(TIndexing).GetConstructor([typeof(IMongoCollection<TAggregate>)]);
        var indexing = (TIndexing?)constructor?.Invoke([collection]);
        if (indexing is not null)
        {
            try
            {
                await indexing.CreateIndexesAsync();
            }
            catch (MongoCommandException ex) when (ex.Message.Contains("database is in the process of being dropped"))
            {
                logger.LogWarning("Database is in the process of being dropped, skipping indexing");
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Failed to execute index creation for {Index} on collection {CollectionName}", typeof(TIndexing).Name, collectionName);
            }
        }
    }
}