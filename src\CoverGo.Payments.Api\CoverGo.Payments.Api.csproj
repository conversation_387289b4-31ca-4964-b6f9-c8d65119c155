﻿<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
      <UserSecretsId>09f3c279-9135-4df5-ac25-cad360cd166b</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="CoverGo.Applications.HealthCheck" />
        <PackageReference Include="CoverGo.BuildingBlocks.Scheduler.Hangfire" />
        <PackageReference Include="CoverGo.FeatureManagement" />
        <PackageReference Include="HotChocolate.AspNetCore" />
        <PackageReference Include="HotChocolate.AspNetCore.CommandLine" />
        <PackageReference Include="HotChocolate.Types.Analyzers">
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
            <PrivateAssets>all</PrivateAssets>
        </PackageReference>
        <PackageReference Include="HotChocolate.Stitching.Redis" />
        <PackageReference Include="HotChocolate.Data.MongoDb" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
        <PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
        <PackageReference Include="CoverGo.BuildingBlocks.Api.Graphql" />
        <PackageReference Include="CoverGo.BuildingBlocks.Application.Core" />
        <PackageReference Include="CoverGo.BuildingBlocks.Auth" />
        <PackageReference Include="CoverGo.Multitenancy.AspNetCore" />
        <PackageReference Include="AutoMapper" />
        <PackageReference Include="Swashbuckle.AspNetCore" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\CoverGo.Payments.Infrastructure\CoverGo.Payments.Infrastructure.csproj" />
    </ItemGroup>

    <ItemGroup>
        <Content Include="..\..\.dockerignore">
            <Link>.dockerignore</Link>
        </Content>
    </ItemGroup>

</Project>
