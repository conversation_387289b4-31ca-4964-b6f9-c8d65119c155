﻿using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Encryption;

namespace CoverGo.Payments.IntegrationTests.Encryption.TestData
{
    public class Class2Aggregate : AggregateRootBase<string>
    {
        public Class2Aggregate(string id) : base(id)
        {
        }
        public Class2? SensitiveClass { get; set; }
    }

    public class Class2
    {
        [Encrypted]
        public string? SensitiveString { get; set; }

        [Encrypted]
        public DateTime SensitiveDateTime { get; set; }

        public int NormalInt { get; set; }
    }
}
