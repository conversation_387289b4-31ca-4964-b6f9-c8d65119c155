﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.RecurringPayment
{
    public class RecurringPaymentCommandValidator : AbstractValidator<RecurringPaymentCommand>
    {
        public RecurringPaymentCommandValidator(
            ILogger<RecurringPaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PayorId).NotEmpty().WithMessage("No payorId found.");
            RuleFor(pc => pc.PolicyId).NotEmpty().WithMessage("No policyId found.");
            RuleFor(pc => pc.InvoiceNumber).NotEmpty().WithMessage("No invoiceNumber found.");
            RuleFor(pc => pc.Amount).GreaterThan(decimal.Zero).WithMessage("Amount should be greater than 0.");
            RuleFor(pc => pc.DecimalPrecision).GreaterThan(0).WithMessage("DecimalPrecision should be greater than 0.");
            RuleFor(pc => pc.CurrencyDesc).NotEmpty().WithMessage("No currency description found.");

            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}