﻿using CoverGo.BuildingBlocks.MessageBus.Contracts;

namespace CoverGo.Payments.Integration.Events;

public abstract record PaymentEventBase : IntegrationEvent
{
    public required string PolicyId { get; init; }

    public required string PayorId { get; init; }
    
    public string? InvoiceNumber { get; init; }

    public required string Status { get; init; }

    public required DateTime EffectiveDate { get; init; }
    
    public required string Type { get; init; }
    
    public required string ReferenceId { get; init; }

    public required Money Money { get; init; }

    public CreditCard? CreditCard { get; init; }

    public PaymentProviderInfo? PaymentProviderInfo { get; init; }

    public PaymentBankInfo? PaymentBankInfo { get; init; }

    public string? PaymentMethod { get; init; }
}