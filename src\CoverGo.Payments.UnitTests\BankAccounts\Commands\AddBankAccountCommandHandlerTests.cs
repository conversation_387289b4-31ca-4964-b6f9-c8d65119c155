using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;
using CoverGo.Payments.Domain.BankAccount;
using FluentAssertions;
using Moq;

namespace CoverGo.Payments.UnitTests.BankAccounts.Commands;

[Trait("Ticket", "CH-28394")]
public class AddBankAccountCommandHandlerTests
{
    private readonly Mock<IRepository<BankAccount, string>> _repositoryMock;
    private readonly AddBankAccountCommandHandler _handler;

    public AddBankAccountCommandHandlerTests()
    {
        _repositoryMock = new Mock<IRepository<BankAccount, string>>();
        _handler = new AddBankAccountCommandHandler(_repositoryMock.Object);
    }

    [Fact]
    public async Task GIVEN_ValidCommand_WHEN_Handle_THEN_CreateBankAccountSuccessfully()
    {
        // Arrange
        var command = CreateValidCommand();
        BankAccount capturedAggregate = null!;

        _repositoryMock
            .Setup(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()))
            .Returns<BankAccount, CancellationToken>((aggregate, _) =>
            {
                capturedAggregate = aggregate;
                return Task.FromResult(aggregate);
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.Country.Should().Be(command.Country);
        result.Currency.Should().Be(command.Currency);
        result.AccountHolderName.Should().Be(command.AccountHolderName);
        result.BankName.Should().Be(command.BankName);
        result.Bic.Should().Be(command.Bic);
        result.PayorId.Should().Be(command.PayorId);
        result.PayorType.Should().Be(command.PayorType);
        result.EntityId.Should().Be(command.EntityId);
        result.AccountNumber.Should().Be(command.AccountNumber);
        result.Iban.Should().Be(command.Iban);
        result.UsedFor.Should().BeEquivalentTo(command.UsedFor);
        
        _repositoryMock.Verify(
            r => r.InsertAsync(It.Is<BankAccount>(ba => 
                ba.Country == command.Country &&
                ba.Currency == command.Currency &&
                ba.AccountHolderName == command.AccountHolderName &&
                ba.BankName == command.BankName &&
                ba.Bic == command.Bic &&
                ba.PayorId == command.PayorId &&
                ba.PayorType == command.PayorType &&
                ba.EntityId == command.EntityId &&
                ba.AccountNumber == command.AccountNumber &&
                ba.Iban == command.Iban &&
                ba.UsedFor != null && ba.UsedFor.SequenceEqual(command.UsedFor)
            ), It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task GIVEN_NullUsedFor_WHEN_Handle_THEN_ThrowArgumentNullException()
    {
        // Arrange
        var command = CreateValidCommand() with { UsedFor = null! };

        // Act & Assert
        var act = () => _handler.Handle(command, CancellationToken.None);
        
        await act.Should().ThrowAsync<ArgumentNullException>();

        _repositoryMock.Verify(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()), 
            Times.Never);
    }

    [Fact]
    public async Task GIVEN_EmptyUsedFor_WHEN_Handle_THEN_ThrowBankAccountUsedForInvalidException()
    {
        // Arrange
        var command = CreateValidCommand() with { UsedFor = new List<BankAccountUsage>() };

        // Act & Assert
        var act = () => _handler.Handle(command, CancellationToken.None);
        
        await act.Should().ThrowAsync<BankAccountUsedForInvalidException>();

        _repositoryMock.Verify(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()), 
            Times.Never);
    }

    [Fact]
    public async Task GIVEN_OptionalFieldsNull_WHEN_Handle_THEN_CreateBankAccountSuccessfully()
    {
        // Arrange
        var command = CreateValidCommand() with 
        { 
            AccountNumber = null, 
            Iban = null 
        };
        BankAccount capturedAggregate = null!;

        _repositoryMock
            .Setup(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()))
            .Returns<BankAccount, CancellationToken>((aggregate, _) =>
            {
                capturedAggregate = aggregate;
                return Task.FromResult(aggregate);
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.AccountNumber.Should().BeNull();
        result.Iban.Should().BeNull();
        
        _repositoryMock.Verify(
            r => r.InsertAsync(It.Is<BankAccount>(ba => 
                ba.AccountNumber == null &&
                ba.Iban == null
            ), It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Theory]
    [InlineData(BankAccountUsage.Billing)]
    [InlineData(BankAccountUsage.Claim)]
    public async Task GIVEN_DifferentUsageTypes_WHEN_Handle_THEN_CreateBankAccountSuccessfully(BankAccountUsage usage)
    {
        // Arrange
        var command = CreateValidCommand() with { UsedFor = new List<BankAccountUsage> { usage } };
        BankAccount capturedAggregate = null!;

        _repositoryMock
            .Setup(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()))
            .Returns<BankAccount, CancellationToken>((aggregate, _) =>
            {
                capturedAggregate = aggregate;
                return Task.FromResult(aggregate);
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.UsedFor.Should().Contain(usage);
        
        _repositoryMock.Verify(
            r => r.InsertAsync(It.Is<BankAccount>(ba => 
                ba.UsedFor != null && ba.UsedFor.Contains(usage)
            ), It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    [Fact]
    public async Task GIVEN_MultipleUsageTypes_WHEN_Handle_THEN_CreateBankAccountSuccessfully()
    {
        // Arrange
        var usageTypes = new List<BankAccountUsage> { BankAccountUsage.Billing, BankAccountUsage.Claim };
        var command = CreateValidCommand() with { UsedFor = usageTypes };
        BankAccount capturedAggregate = null!;

        _repositoryMock
            .Setup(r => r.InsertAsync(It.IsAny<BankAccount>(), It.IsAny<CancellationToken>()))
            .Returns<BankAccount, CancellationToken>((aggregate, _) =>
            {
                capturedAggregate = aggregate;
                return Task.FromResult(aggregate);
            });

        // Act
        var result = await _handler.Handle(command, CancellationToken.None);

        // Assert
        result.Should().NotBeNull();
        result.UsedFor.Should().Contain(BankAccountUsage.Billing);
        result.UsedFor.Should().Contain(BankAccountUsage.Claim);
        
        _repositoryMock.Verify(
            r => r.InsertAsync(It.Is<BankAccount>(ba => 
                ba.UsedFor != null && 
                ba.UsedFor.Contains(BankAccountUsage.Billing) &&
                ba.UsedFor.Contains(BankAccountUsage.Claim)
            ), It.IsAny<CancellationToken>()), 
            Times.Once);
    }

    private static AddBankAccountCommand CreateValidCommand()
    {
        return new AddBankAccountCommand
        {
            Country = "US",
            Currency =  "USD",
            AccountHolderName = "John Doe",
            BankName = "Test Bank",
            Bic = "TESTUS33",
            UsedFor = new List<BankAccountUsage> { BankAccountUsage.Billing },
            PayorId = "payor-123",
            PayorType = PayorType.RiskCarrier,
            EntityId = "entity-456",
            AccountNumber = "**********",
            Iban = "US**********123456789"
        };
    }

    private static BankAccount CreateBankAccountAggregate()
    {
        return new BankAccount
        {
            Country = "US",
            Currency = "USD",
            AccountHolderName = "John Doe",
            BankName = "Test Bank",
            Bic = "TESTUS33",
            UsedFor = new List<BankAccountUsage> { BankAccountUsage.Billing },
            PayorId = "payor-123",
            PayorType = PayorType.RiskCarrier,
            EntityId = "entity-456",
            AccountNumber = "**********",
            Iban = "US**********123456789"
        };
    }


}