﻿using CoverGo.BuildingBlocks.MessageBus.Abstractions.Interceptors;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using CoverGo.Multitenancy;
using CoverGo.Proxies.Auth;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Common.Auth;

internal class UserInterceptor(
    ITenantProvider tenantProvider,
    AdminLoginCacheProvider adminLoginCacheProvider,
    ILogger<UserInterceptor> logger) : IMessageInterceptor
{
    public async ValueTask<T> Intercept<T>(T message, CancellationToken cancellationToken = default) where T : Message
    {
        try
        {
            if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
            {
                logger.LogWarning("Unable to retrieve tenant information.");
                return message;
            }

            Login? login = await adminLoginCacheProvider.GetAdminLoginAsync(tenantId, cancellationToken);
            if (login != null)
            {
                message.UserId = login.Id;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during message interception for UserId assignment.");
        }

        return message;
    }
}