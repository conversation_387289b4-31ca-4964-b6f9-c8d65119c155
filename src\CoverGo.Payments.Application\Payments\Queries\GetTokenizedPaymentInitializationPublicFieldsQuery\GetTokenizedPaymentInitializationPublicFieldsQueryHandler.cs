using System.Text.Json;
using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Queries.GetTokenizedPaymentInitializationPublicFieldsQuery;

public class GetTokenizedPaymentInitializationPublicFieldsQueryHandler(IPaymentService paymentService)
    : IQueryHandler<GetTokenizedPaymentInitializationPublicFieldsQuery, IReadOnlyDictionary<string, string>>
{
    public async Task<IReadOnlyDictionary<string, string>> Handle(GetTokenizedPaymentInitializationPublicFieldsQuery query, CancellationToken cancellationToken)
    {
        TokenizedPaymentInitializationAggregate? aggregate = await paymentService.GetTokenizedPaymentInitializationAsync(
            query.InitializationToken,
            throwIfNotFound: false,
            cancellationToken);

        Dictionary<string, object?>? values = aggregate?.IsCanceled == true || string.IsNullOrEmpty(aggregate?.PublicFields)
            ? null
            : JsonSerializer.Deserialize<Dictionary<string, object?>>(aggregate.PublicFields);

        var result = values ?? new Dictionary<string, object?>();

        if (aggregate?.IsExpired == true && result.ContainsKey("expiredDate"))
        {
            result["expiredDate"] = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd");
        }

        return result
            .Where(kvp => kvp.Value?.ToString() != null)
            .ToDictionary(kvp => kvp.Key, kvp => kvp.Value!.ToString()!)
            .AsReadOnly();
    }
}
