receivers:
  otlp:
    protocols:
      grpc:
      http:

exporters:
  debug: {} # Replaced logging with debug
  zipkin:
    endpoint: "http://zipkin:9411/api/v2/spans"
    format: proto

processors:
  batch:

extensions:
  health_check:
  zpages:
    endpoint: :55679

service:
  extensions: [zpages, health_check]
  pipelines:
    traces:
      receivers: [otlp]
      processors: [batch]
      exporters: [debug, zipkin] # Use debug instead of logging
