﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.FailPayment
{
    public class FailPaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<FailPaymentCommandHandler> logger)
        : ICommandHandler<FailPaymentCommand, PaymentDto>
    {
        /// <param name="failPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(FailPaymentCommand failPaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("FailPaymentCommandHandler.Handle: Starting payment failure. PaymentId: {PaymentId}",
                failPaymentCommand.PaymentId);

            try
            {
                PaymentAggregate payment = await paymentService.FailPaymentAsync(failPaymentCommand.PaymentId, cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("FailPaymentCommandHandler.Handle: Payment failure completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "FailPaymentCommandHandler.Handle: Unexpected error during payment failure. PaymentId: {PaymentId}",
                    failPaymentCommand.PaymentId);
                throw;
            }
        }
    }
}
