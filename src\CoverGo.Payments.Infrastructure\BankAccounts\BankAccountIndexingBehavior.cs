using CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;
using CoverGo.Payments.Application.BankAccounts.Commands.UpdateBankAccount;
using MediatR.Pipeline;

namespace CoverGo.Payments.Infrastructure.BankAccounts;

public class BankAccountIndexingBehavior(BankAccountIndexing indexing) :
    IRequestPreProcessor<AddBankAccountCommand>,
    IRequestPreProcessor<UpdateBankAccountCommand>
{
    public Task Process(AddBankAccountCommand request, CancellationToken cancellationToken) => 
        indexing.CreateIndexesAsync();

    public Task Process(UpdateBankAccountCommand request, CancellationToken cancellationToken) => 
        indexing.CreateIndexesAsync();
}