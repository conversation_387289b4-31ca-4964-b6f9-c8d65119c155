﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.FinalizePayment
{
    public class FinalizePaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<FinalizePaymentCommandHandler> logger)
        : ICommandHandler<FinalizePaymentCommand, PaymentDto>
    {
        /// <param name="finalizePaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(FinalizePaymentCommand finalizePaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("FinalizePaymentCommandHandler.Handle: Starting payment finalization. PaymentId: {PaymentId}, HasDynamicFields: {HasDynamicFields}",
                finalizePaymentCommand.PaymentId,
                finalizePaymentCommand.DynamicFields.HasValue);

            try
            {
                PaymentAggregate payment = await paymentService.FinalizePreauthPaymentAsync(
                    finalizePaymentCommand.PaymentId, finalizePaymentCommand.DynamicFields, false, cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("FinalizePaymentCommandHandler.Handle: Payment finalization completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "FinalizePaymentCommandHandler.Handle: Unexpected error during payment finalization. PaymentId: {PaymentId}",
                    finalizePaymentCommand.PaymentId);
                throw;
            }
        }
    }
}