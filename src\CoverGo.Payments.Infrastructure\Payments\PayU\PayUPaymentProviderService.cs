﻿using System.Globalization;
using System.Net;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Application.Payments.Models;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.Helpers;
using CoverGo.Payments.Domain.Payment;
using CoverGo.Payments.Domain.PspSettings;
using CoverGo.Payments.Domain.Refund;
using CoverGo.Payments.Infrastructure.Common;
using CoverGo.Payments.Infrastructure.Payments.PayU.Configurations;
using CoverGo.Payments.Infrastructure.Payments.PayU.Helpers;
using CoverGo.Payments.Infrastructure.Payments.PayU.Models;
using GuardClauses;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Authorization = CoverGo.Payments.Infrastructure.Payments.PayU.Models.Authorization;

namespace CoverGo.Payments.Infrastructure.Payments.PayU;

public class PayUPaymentProviderService(
    ILogger<PayUPaymentProviderService> logger,
    IHttpClientFactory clientFactory,
    IDateTimeProvider dateTimeProvider) : BasePaymentProviderService(logger)
{
    private const string StoredCredentialsUseType = "merchant";
    private const string AuthorizationPaymentMethod = "CCVISAMC";

    public override PaymentProvider Type => PaymentProvider.PayU;

    #region GetPreProcessRedirectUrlAsync

    public override async Task<RedirectUrlOutput> GetPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        logger.LogInformation("Starting pre-process for payment ID: {PaymentId}", payment.Id);

        if (payment.IsUpdate)
            return await GetUpdatePreProcessRedirectUrlAsync(payment, cancellationToken);
        return await GetInitialPreProcessRedirectUrlAsync(payment, dynamicFields.Value.GetRawText(), cancellationToken);
    }

    private async Task<RedirectUrlOutput> GetInitialPreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        string dynamicFieldsStr,
        CancellationToken cancellationToken = default)
    {
        try
        {
            PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings);
            PaymentRequest request =
                CreatePaymentRequest(payment, pspSettings, false, dynamicFieldsStr);

            PayerData payerData = CreatePayerData(request.Client);
            payment.SetPayerData(payerData);

            PaymentResponse? response = await payUClient.PostPaymentAsync(request, cancellationToken);
            GuardClause.ArgumentIsNotNull(response, nameof(response));
            if (response.Status != "SUCCESS")
            {
                throw new DomainException("PayU exception occurred while creating redirect url");
            }

            payment.SetExternalReference(response.PayuPaymentReference);

            logger.LogInformation("Pre-process completed for payment ID: {PaymentId}", payment.Id);
            return new RedirectUrlOutput(new Uri(response.PaymentResult.Url), new Dictionary<string, string>());
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-process for payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private async Task<RedirectUrlOutput> GetUpdatePreProcessRedirectUrlAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default)
    {
        try
        {
            PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(payment);
            var sessionRequest = new SessionRequest { LifetimeMinutes = pspSettings!.SessionLifetimeMinutes };

            SessionResponse? response = await payUClient.PostSessionAsync(sessionRequest, cancellationToken);
            GuardClause.ArgumentIsNotNull(response, nameof(response));
            if (response!.Status != "SUCCESS")
            {
                throw new DomainException("PayU exception occurred while creating redirect url");
            }

            logger.LogInformation("Pre-process completed for payment ID: {PaymentId}", payment.Id);

            var data = new Dictionary<string, string> { { "sessionId", response.SessionId } };

            return new RedirectUrlOutput(null, data);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error in pre-process for payment ID: {PaymentId}", payment.Id);
            throw;
        }
    }

    private IPayUClient CreatePayUClient(PreauthPaymentAggregate payment)
    {
        PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

        IPayUClient payUClient = CreatePayUClient(pspSettings!);

        return payUClient;
    }

    private PaymentRequest CreatePaymentRequest(PaymentAggregate payment,
        PayUPspSettingsAggregate pspSettings,
        bool isRecurringPayment,
        string dynamicFieldsJson)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

        var paymentRequest = new PaymentRequest
        {
            MerchantPaymentReference = payment.InternalReference,
            Currency = CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode),
            Authorization = BuildAuthorization(payment, isRecurringPayment),
            Client = BuildClient(dynamicFieldsJson),
            Products = BuildProducts(dynamicFieldsJson)
        };

        if (isRecurringPayment)
        {
            paymentRequest.StoredCredentials = new StoredCredentials { UseType = StoredCredentialsUseType };
            paymentRequest.Products.First().UnitPrice = payment.Money.PaymentAmount;
        }
        else
        {
            paymentRequest.ReturnUrl = BuildReturnUrl(pspSettings.RedirectUrl, payment.Id, dynamicFieldsJson);

            ValidatePaymentAmount(payment, paymentRequest.Products.First().UnitPrice);
        }

        return paymentRequest;
    }

    private string BuildReturnUrl(string redirectUrl, string paymentId, string dynamicFieldsJson)
    {
        string origin = ExtractRequestOrigin(dynamicFieldsJson);
        string newRedirectUrl = UrlHelper.ReplaceOrigin(redirectUrl, origin);
        logger.LogInformation($"Return URL for {PaymentProvider.PayU} with origin: {origin} is {newRedirectUrl}");

        return $"{newRedirectUrl}?paymentId={paymentId}";
    }

    private static Authorization BuildAuthorization(PaymentAggregate payment, bool isRecurringPayment)
    {
        var authorization = new Authorization { PaymentMethod = AuthorizationPaymentMethod };

        if (isRecurringPayment)
        {
            authorization.MerchantToken = new MerchantToken
            {
                TokenHash = (payment.InitialBearer as PSPBearerPseudoCC)?.Token
            };
            authorization.UsePaymentPage = "NO";
        }
        else
        {
            authorization.UsePaymentPage = "YES";
            authorization.PaymentPageOptions = new PaymentPageOptions { OrderTimeout = 3600 };
        }

        return authorization;
    }

    private Client BuildClient(string dynamicFieldsJson) => new() { Billing = ExtractBillingInfo(dynamicFieldsJson) };

    private List<Product> BuildProducts(string dynamicFieldsJson)
    {
        var products = new List<Product> { ExtractProductInfo(dynamicFieldsJson) };
        if (products == null || !products.Any())
        {
            throw new DomainException("No products found in dynamic fields");
        }

        return products;
    }

    private static void ValidatePaymentAmount(PaymentAggregate payment, decimal productUnitPrice)
    {
        if (payment.Money.PaymentAmount != productUnitPrice)
        {
            throw new DomainException("Mismatching payment and product amount");
        }
    }

    private Billing ExtractBillingInfo(string dynamicFieldsJson)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(dynamicFieldsJson, nameof(dynamicFieldsJson));

        try
        {
            Billing? billing = ParseDynamicFields<Billing>(dynamicFieldsJson);

            GuardClause.ArgumentIsNotNull(billing, nameof(billing));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(billing.FirstName, nameof(billing.FirstName));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(billing.LastName, nameof(billing.LastName));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(billing.Email, nameof(billing.Email));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(billing.Phone, nameof(billing.Phone));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(billing.CountryCode, nameof(billing.CountryCode));

            return billing;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the billing information from dynamic fields.");
            throw;
        }
    }

    private Product ExtractProductInfo(string dynamicFieldsJson)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(dynamicFieldsJson, nameof(dynamicFieldsJson));

        try
        {
            Product? product = ParseDynamicFields<Product>(dynamicFieldsJson);

            GuardClause.ArgumentIsNotNull(product, nameof(product));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(product.Name, nameof(product.Name));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(product.Sku, nameof(product.Sku));
            GuardClause.IsZeroOrNegative(product.UnitPrice, nameof(product.UnitPrice));
            GuardClause.IsZeroOrNegative(product.Quantity, nameof(product.Quantity));

            return product;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the product information from dynamic fields.");
            throw;
        }
    }

    private static PayerData CreatePayerData(Client client)
    {
        GuardClause.ArgumentIsNotNull(client, nameof(client));
        GuardClause.ArgumentIsNotNull(client.Billing, nameof(client.Billing));

        Billing billing = client.Billing;
        var address = new Domain.Payment.Address(
            addressLine1: billing.AddressLine1,
            addressLine2: billing.AddressLine2,
            street: null,
            houseNumber: null,
            postalCode: billing.ZipCode,
            city: billing.City,
            state: billing.State,
            country: billing.CountryCode
        );

        return new PayerData(
            language: client.CommunicationLanguage,
            emailAddress: billing.Email,
            address: address,
            lastName: billing.LastName,
            firstName: billing.FirstName,
            externalCustomerId: null,
            companyName: billing.CompanyName,
            phoneNumber: billing.Phone
        );
    }

    #endregion


    #region FinalizePaymentAsync

    public override async Task FinalizePaymentAsync(PreauthPaymentAggregate payment,
        PreauthPaymentAggregate? prevPayment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        logger.LogInformation("Finalizing payment ID: {PaymentId}", payment.Id);

        if (payment.IsUpdate)
            await FinalizeUpdatePaymentAsync(payment, dynamicFields, cancellationToken);
        else
            await FinalizeInitialPaymentAsync(payment, cancellationToken);
    }

    private async Task FinalizeInitialPaymentAsync(PreauthPaymentAggregate payment, CancellationToken cancellationToken)
    {
        try
        {
            PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings);

            PaymentResponse paymentResponse = await GetPaymentAsync(payment, payUClient, cancellationToken);

            TokenResponse? tokenResponse =
                await CreateTokenAsync(paymentResponse.PayuPaymentReference, payUClient, cancellationToken);

            MapPaymentInfo(payment, paymentResponse, tokenResponse);

            payment.AddPaymentStatusHistoryItem(MapPaymentStatus(paymentResponse.PaymentStatus), payment.Money);

            logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finalizing payment ID: {PaymentId}", payment.Id);
            throw new DomainException($"Error finalizing payment ID: {payment.Id}", ex);
        }
    }

    private async Task FinalizeUpdatePaymentAsync(PreauthPaymentAggregate payment, JsonElement? dynamicFields,
        CancellationToken cancellationToken = default)
    {
        try
        {
            PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings!);
            PaymentRequest request = CreateOneTimePaymentRequest(payment, pspSettings!, dynamicFields);

            PayerData payerData = CreatePayerData(request.Client);
            payment.SetPayerData(payerData);

            PaymentResponse? paymentResponse = await payUClient.PostPaymentAsync(request, cancellationToken);
            GuardClause.ArgumentIsNotNull(paymentResponse, nameof(paymentResponse));
            if (paymentResponse!.Status != "SUCCESS")
            {
                throw new DomainException($"Error finalizing payment ID: {payment.Id}");
            }

            TokenResponse? tokenResponse =
                await CreateTokenAsync(paymentResponse.PayuPaymentReference, payUClient, cancellationToken);

            MapPaymentInfo(payment, paymentResponse, tokenResponse);

            string paymentStatus = paymentResponse.PaymentStatus;
            if (string.IsNullOrWhiteSpace(paymentStatus) &&
                !string.IsNullOrWhiteSpace(paymentResponse.PayuPaymentReference))
            {
                paymentStatus = "COMPLETE";
            }

            payment.AddPaymentStatusHistoryItem(MapPaymentStatus(paymentStatus), payment.Money);

            logger.LogInformation("Payment with ID: {PaymentId} finalized successfully", payment.Id);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error finalizing payment ID: {PaymentId}", payment.Id);
            throw new DomainException($"Error finalizing payment ID: {payment.Id}", ex);
        }
    }

    private OneTimeUseToken ExtractOneTimeUseToken(JsonElement? dynamicFields)
    {
        GuardClause.ArgumentIsNotNull(dynamicFields, nameof(dynamicFields));

        try
        {
            OneTimeUseToken? oneTimeUseToken = ParseDynamicFields<OneTimeUseToken>(dynamicFields!.Value.GetRawText());

            GuardClause.ArgumentIsNotNull(oneTimeUseToken, nameof(oneTimeUseToken));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(oneTimeUseToken!.SessionId, nameof(oneTimeUseToken.SessionId));
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(oneTimeUseToken.Token, nameof(oneTimeUseToken.Token));

            return oneTimeUseToken;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error when extracting the one time use token from dynamic fields.");
            throw;
        }
    }

    private PaymentRequest CreateOneTimePaymentRequest(PreauthPaymentAggregate payment,
        PayUPspSettingsAggregate pspSettings,
        JsonElement? dynamicFields)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

        OneTimeUseToken oneTimeUseToken = ExtractOneTimeUseToken(dynamicFields);

        var paymentRequest = new PaymentRequest
        {
            MerchantPaymentReference = payment.Id,
            Currency = CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode),
            Authorization =
                new Authorization
                {
                    PaymentMethod = AuthorizationPaymentMethod,
                    OneTimeUseToken =
                        new OneTimeUseToken { Token = oneTimeUseToken.Token, SessionId = oneTimeUseToken.SessionId }
                },
            Client = BuildClient(payment.DynamicFields),
            Products = new(),
            ReturnUrl = pspSettings.RedirectUrl,
            StoredCredentials = new StoredCredentials { UseType = StoredCredentialsUseType }
        };

        return paymentRequest;
    }

    private static async Task<PaymentResponse> GetPaymentAsync(PaymentAggregate payment, IPayUClient payUClient,
        CancellationToken cancellationToken)
    {
        PaymentResponse? paymentResponse =
            await payUClient.GetPaymentAsync(payment.InternalReference, cancellationToken);
        if (paymentResponse is not { Code: (int)HttpStatusCode.OK })
        {
            throw new DomainException(
                $"Failed to retrieve payment: No payment information received for payment ID: {payment.Id}");
        }

        return paymentResponse;
    }

    private async Task<TokenResponse?> CreateTokenAsync(string payuPaymentReference,
        IPayUClient payUClient,
        CancellationToken cancellationToken)
    {
        try
        {
            GuardClause.IsNullOrEmptyStringOrWhiteSpace(payuPaymentReference, nameof(payuPaymentReference));
            TokenResponse? tokenResponse =
                await payUClient.PostTokenAsync(new TokenRequest { PayuPaymentReference = payuPaymentReference },
                    cancellationToken);
            if (tokenResponse is not { Code: (int)HttpStatusCode.OK })
                throw new DomainException("Failed to create token");

            return tokenResponse;
        }
        catch (Exception ex)
        {
            logger.LogWarning(ex, "Error finalizing payment ID: {PaymentId}. Failed to create token.",
                payuPaymentReference);
        }

        return null;
    }

    private static void MapPaymentInfo(PaymentAggregate payment, PaymentResponse paymentResponse,
        TokenResponse? tokenResponse)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentResponse, nameof(paymentResponse));

        if (string.IsNullOrWhiteSpace(payment.ExternalReference))
        {
            payment.SetExternalReference(paymentResponse.PayuPaymentReference);
        }

        if (payment.InitialBearer == null)
        {
            PSPBearerPseudoCC initialBearer = CreateInitialBearer(paymentResponse, tokenResponse);
            payment.SetInitialBearer(initialBearer);
        }
    }

    private static PSPBearerPseudoCC CreateInitialBearer(PaymentResponse paymentResponse,
        TokenResponse? tokenResponse)
    {
        GuardClause.ArgumentIsNotNull(paymentResponse, nameof(paymentResponse));

        AuthorizationResource authorization =
            paymentResponse.Authorizations?.FirstOrDefault() ?? paymentResponse.Authorization;

        GuardClause.ArgumentIsNotNull(authorization, nameof(paymentResponse.Authorization));
        GuardClause.ArgumentIsNotNull(authorization.CardDetails, nameof(paymentResponse.Authorization.CardDetails));

        CardDetails cardData = authorization.CardDetails;
        var pspBearerPseudoCc = new PSPBearerPseudoCC
        {
            PseudoCardPan = $"{cardData.BinNumber}******{cardData.LastFourDigits}",
            CardType = cardData.CardScheme,
            Country = cardData.IssuerCountryCode,
            TruncatedCardPan = cardData.LastFourDigits,
            OrderId = paymentResponse.PayuPaymentReference,
            IssuerId = null,
            ExpiryMonth = cardData.ExpiryMonth,
            ExpiryYear = cardData.ExpiryYear,
            Holder = cardData.Owner
        };

        if (tokenResponse == null)
            return pspBearerPseudoCc;

        CardHelper.ExtractExpiryMonthAndYear(tokenResponse.CardExpirationDate, out int expiryMonth, out int expiryYear);
        pspBearerPseudoCc.Token = tokenResponse.Token;
        pspBearerPseudoCc.ExpiryMonth = expiryMonth != 0 ? expiryMonth : null;
        pspBearerPseudoCc.ExpiryYear = expiryYear != 0 ? expiryYear : null;
        pspBearerPseudoCc.Holder = tokenResponse.CardHolderName;

        return pspBearerPseudoCc;
    }

    #endregion


    #region CapturePaymentAsync

    public override async Task<CapturePaymentAggregate> CapturePaymentAsync(
        CapturePaymentAggregate capturePayment,
        string providerPaymentId,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(capturePayment, nameof(capturePayment));
        logger.LogInformation("Starting capture for capturePayment ID: {PaymentId}", capturePayment.Id);

        try
        {
            PayUPspSettingsAggregate? pspSettings =
                GetPspSettings<PayUPspSettingsAggregate>(capturePayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings);

            CaptureRequest captureRequest = BuildCaptureRequest(capturePayment);
            CaptureResponse? response = await payUClient.CapturePaymentAsync(captureRequest, cancellationToken);

            ProcessCaptureResponse(capturePayment, response);

            return capturePayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing capture for capturePayment ID: {PaymentId}", capturePayment.Id);
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, ex.Message);
            throw;
        }
    }

    private static CaptureRequest
        BuildCaptureRequest(PaymentAggregate capturePayment) =>
        new()
        {
            PayuPaymentReference = capturePayment.ExternalReference,
            OriginalAmount = capturePayment.Money.PaymentAmount,
            Amount = capturePayment.Money.PaymentAmount,
            Currency = CurrencyHelper.GetCurrency(capturePayment.Money.PaymentCurrencyCode),
        };

    private void ProcessCaptureResponse(PaymentAggregate capturePayment, CaptureResponse response)
    {
        if (response.Code is 200 or 202)
        {
            if (response.Status is "SUCCESS" or "ALREADY_CONFIRMED")
            {
                capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, capturePayment.Money);
                logger.LogInformation("Capture for capturePayment ID: {PaymentId} completed successfully",
                    capturePayment.Id);
            }
            else
            {
                capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money,
                    response.Message);
                logger.LogWarning("Capture for capturePayment ID: {PaymentId} failed with status: {Status}",
                    capturePayment.Id, response.Status);
            }
        }
        else
        {
            capturePayment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, capturePayment.Money, response.Message);
            logger.LogWarning("Capture for capturePayment ID: {PaymentId} failed with HTTP status code: {Code}",
                capturePayment.Id, response.Code);
        }
    }

    #endregion


    #region RefundAsync

    public override async Task<RefundAggregate> RefundAsync(
        PaymentAggregate payment,
        RefundAggregate paymentRefund,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        GuardClause.ArgumentIsNotNull(paymentRefund, nameof(paymentRefund));
        logger.LogInformation("Starting refund for payment ID: {PaymentId}", payment.Id);

        try
        {
            PayUPspSettingsAggregate? pspSettings = GetPspSettings<PayUPspSettingsAggregate>(payment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings);

            RefundRequest refundRequest = BuildRefundRequest(payment, paymentRefund);

            RefundResponse? response =
                await payUClient.PostRefundAsync(refundRequest, cancellationToken);

            ProcessRefundResponse(payment, paymentRefund, response);

            return paymentRefund;
        }
        catch (Exception ex)
        {
            HandleRefundException(paymentRefund, ex, payment.Id);
            throw;
        }
    }

    private static RefundRequest BuildRefundRequest(PaymentAggregate payment, RefundAggregate paymentRefund)
    {
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payment.ExternalReference, nameof(payment.ExternalReference));

        var refundRequest = new RefundRequest
        {
            PayuPaymentReference = payment.ExternalReference!,
            OriginalAmount = payment.Money.PaymentAmount,
            Currency = CurrencyHelper.GetCurrency(payment.Money.PaymentCurrencyCode),
            Amount = paymentRefund.Money.PaymentAmount,
            MerchantRefundReference = paymentRefund.Id,
            UseFastRefund = "try",
        };

        return refundRequest;
    }

    private void ProcessRefundResponse(PaymentAggregate payment, RefundAggregate paymentRefund, RefundResponse response)
    {
        if (response.Code is 200)
        {
            if (response.Status == "SUCCESS")
            {
                paymentRefund.AssignProviderTransaction(response.RefundRequestId);
                paymentRefund.SetStatus(RefundStatus.Succeeded);
                logger.LogInformation("Refund completed successfully");
            }
            else
            {
                paymentRefund.SetStatus(RefundStatus.Failed, response.Message);
                logger.LogWarning("Refund failed with status: {StatusDesc}", response.Message);
                throw new DomainException($"Refund request failed: {response.Message}");
            }
        }
        else
        {
            paymentRefund.SetStatus(RefundStatus.Failed, response.Message);
            logger.LogWarning("Refund for payment ID: {PaymentId} failed with HTTP status code: {Code}",
                payment.Id, response.Code);
        }
    }

    private void HandleRefundException(RefundAggregate paymentRefund, Exception ex, string paymentId)
    {
        logger.LogError(ex, "Error processing refund for payment ID: {PaymentId}", paymentId);
        paymentRefund.SetStatus(RefundStatus.Failed, ex.Message);
    }

    #endregion

    public override Task CancelPreauthPaymentAsync(PreauthPaymentAggregate payment,
        CancellationToken cancellationToken = default) => throw new NotImplementedException();

    #region RecurringPaymentAsync

    public override async Task<RecurringPaymentAggregate> RecurringPaymentAsync(
        RecurringPaymentAggregate recurringPayment,
        CancellationToken cancellationToken = default)
    {
        GuardClause.ArgumentIsNotNull(recurringPayment, nameof(recurringPayment));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(recurringPayment.DynamicFields,
            nameof(recurringPayment.DynamicFields));
        logger.LogInformation("Starting recurring payment for payment ID: {PaymentId}", recurringPayment.Id);

        try
        {
            PayUPspSettingsAggregate? pspSettings =
                GetPspSettings<PayUPspSettingsAggregate>(recurringPayment.PspSettings);
            GuardClause.ArgumentIsNotNull(pspSettings, nameof(pspSettings));

            IPayUClient payUClient = CreatePayUClient(pspSettings);
            PaymentRequest request =
                CreatePaymentRequest(recurringPayment, pspSettings, true, recurringPayment.DynamicFields);

            PaymentResponse? response = await payUClient.PostPaymentAsync(request, cancellationToken);
            GuardClause.ArgumentIsNotNull(response, nameof(response));
            if (response?.Status != "SUCCESS" && response?.Authorization.Authorized != "SUCCESS")
            {
                throw new DomainException($"Error processing recurring payment for payment ID: {recurringPayment.Id}");
            }

            recurringPayment.AddPaymentStatusHistoryItem(PaymentStatus.Succeeded, recurringPayment.Money);
            recurringPayment.SetExternalReference(response.PayuPaymentReference);

            logger.LogInformation("Recurring payment for payment ID: {PaymentId} completed successfully",
                recurringPayment.Id);
            return recurringPayment;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error processing recurring payment for payment ID: {PaymentId}", recurringPayment.Id);
            throw;
        }
    }

    #endregion

    #region FailPaymentAsync

    public override async Task FailPaymentAsync(PaymentAggregate payment, CancellationToken cancellationToken = default)
    {
        payment.AddPaymentStatusHistoryItem(PaymentStatus.Failed, payment.Money, "Failed by PSP.");
        await Task.CompletedTask;
    }

    #endregion

    public override Task<(
        string paymentId,
        string providerPaymentId,
        string externalReference,
        string internalReference,
        PaymentStatus paymentStatus,
        decimal? amount,
        string webhookAcknowledgeMessage,
        bool isFinalizationRequired,
        bool skip)> HandleWebhookAsync(
        string webhookBody,
        PspSettingsAggregate pspSettings,
        CancellationToken cancellationToken = default)
    {
        logger.LogInformation("Handling PayU webhook");

        var payuSettings = pspSettings as PayUPspSettingsAggregate;
        GuardClause.ArgumentIsNotNull(payuSettings, nameof(payuSettings));
        GuardClause.IsNullOrEmptyStringOrWhiteSpace(payuSettings.SecretKey, nameof(payuSettings.SecretKey));

        try
        {
            PayuIpnPayload? payload = JsonConvert.DeserializeObject<PayuIpnPayload>(webhookBody);
            GuardClause.ArgumentIsNotNull(payload, nameof(payload));

            string externalReference = payload.OrderData?.PayuPaymentReference ?? string.Empty;
            string internalReference = payload.OrderData?.MerchantPaymentReference ?? string.Empty;
            string? rawAmount = payload.OrderData?.Amount?.ToString();

            decimal? amount = decimal.TryParse(rawAmount, NumberStyles.Any, CultureInfo.InvariantCulture,
                out decimal parsedAmount)
                ? parsedAmount
                : null;

            string status = payload.OrderData?.Status ?? "UNKNOWN";
            PaymentStatus mappedStatus = MapPaymentStatus(status, true);

            logger.LogInformation(
                "PayU IPN received. Status={Status}, Amount={Amount}, ExternalRef={ExternalRef}, InternalRef={InternalRef}",
                status, amount, externalReference, internalReference);

            bool isFinalizationRequired =
                mappedStatus is PaymentStatus.Succeeded or PaymentStatus.Failed or PaymentStatus.Canceled;

            return Task.FromResult<(string paymentId, string providerPaymentId, string externalReference, string
                internalReference, PaymentStatus paymentStatus, decimal? amount, string webhookAcknowledgeMessage,
                bool isFinalizationRequired, bool skip)>((
                null,
                providerPaymentId: externalReference,
                externalReference: externalReference,
                internalReference: internalReference,
                paymentStatus: mappedStatus,
                amount: amount,
                webhookAcknowledgeMessage: "OK",
                isFinalizationRequired: isFinalizationRequired,
                skip: !isFinalizationRequired
            )!);
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unhandled exception during PayU webhook processing");
            return Task.FromResult<(string paymentId, string providerPaymentId, string externalReference, string
                internalReference, PaymentStatus paymentStatus, decimal? amount, string webhookAcknowledgeMessage,
                bool isFinalizationRequired, bool skip)>((string.Empty, string.Empty, string.Empty, string.Empty,
                PaymentStatus.Failed, null,
                "Unhandled exception", false, false));
        }
    }

    private static PaymentStatus MapPaymentStatus(string paymentStatus, bool isWebhookCall = false) =>
        paymentStatus switch
        {
            "IN_PROGRESS" => PaymentStatus.InProgress,
            "REVERSED" => PaymentStatus.Chargeback,
            "CARD_NOTAUTHORIZED" => PaymentStatus.Failed,
            "WAITING_PAYMENT" => PaymentStatus.Pending,
            "COMPLETE" => PaymentStatus.Succeeded,
            "PAYMENT_AUTHORIZED" => isWebhookCall ? PaymentStatus.InProgress : PaymentStatus.Succeeded,
            "REFUND" => PaymentStatus.Refunded,
            "CANCELED" => PaymentStatus.Canceled,
            "CASH" => PaymentStatus.Pending,
            "TEST" => PaymentStatus.Undefined,
            "FRAUD" => PaymentStatus.Failed,
            "INVALID" => PaymentStatus.Failed,
            "NOT_FOUND" => PaymentStatus.Undefined,
            _ => PaymentStatus.Unmapped
        };

    private IPayUClient CreatePayUClient(PayUPspSettingsAggregate pspSettings) =>
        new PayUClient(
            new PayUClientSettings(
                pspSettings.Environment.Equals("qa", StringComparison.OrdinalIgnoreCase)
                    ? PayUContainer.PayUApiUrl.Sandbox
                    : PayUContainer.PayUApiUrl.Production,
                PayUPspSettingsAggregate.ApiVersion,
                pspSettings.MerchantCode,
                pspSettings.SecretKey,
                "PayUHttpClient"),
            clientFactory,
            dateTimeProvider,
            logger);
}