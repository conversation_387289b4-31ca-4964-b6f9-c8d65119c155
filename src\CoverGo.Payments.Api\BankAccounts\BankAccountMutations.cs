using CoverGo.BuildingBlocks.Auth.Permissions;
using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Domain.BankAccount;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.BankAccounts.Commands.AddBankAccount;
using CoverGo.Payments.Application.BankAccounts.Commands.UpdateBankAccount;

using CoverGo.Payments.Application.Common;
using CoverGo.Payments.Application.HttpContextUtils;

using HotChocolate.Authorization;
using MediatR;

namespace CoverGo.Payments.Api.BankAccounts;

[MutationType]
public class BankAccountMutations
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [Error(typeof(BankAccountAlreadyExistsException))]
    [Error(typeof(BankAccountUsedForInvalidException))]
    [UseMutationConvention(PayloadFieldName = "bankAccount")]
    [Authorize]
    public async Task<BankAccount> AddBankAccount(
        AddBankAccountCommand input,
        [Service] IMediator commandProcessor,
        [Service] IUserClaimExtractor userClaimExtractor,
        [Service] IPermissionValidator permissionValidator,
        CancellationToken cancellationToken)
    {
        var identity = userClaimExtractor.GetClaimsIdentity();
        permissionValidator.AuthorizeByGroup(identity, AuthorizationConstants.PermissionGroups.RiskManager);

        return await commandProcessor.Send(input, cancellationToken);
    }

    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [Error(typeof(BankAccountAlreadyExistsException))]
    [Error(typeof(BankAccountUsedForInvalidException))]
    [UseMutationConvention(PayloadFieldName = "bankAccount")]
    [Authorize]
    public async Task<BankAccount> UpdateBankAccount(
        UpdateBankAccountCommand input,
        [Service] IMediator commandProcessor,
        [Service] IUserClaimExtractor userClaimExtractor,
        [Service] IPermissionValidator permissionValidator,
        CancellationToken cancellationToken)
    {
        var identity = userClaimExtractor.GetClaimsIdentity();
        permissionValidator.AuthorizeByGroup(identity, AuthorizationConstants.PermissionGroups.RiskManager);

        return await commandProcessor.Send(input, cancellationToken);
    }

} 