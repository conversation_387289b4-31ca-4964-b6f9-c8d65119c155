﻿using CoverGo.BuildingBlocks.MessageBus.Abstractions;
using CoverGo.BuildingBlocks.MessageBus.Abstractions.Interceptors;
using CoverGo.BuildingBlocks.MessageBus.Contracts;
using CoverGo.Multitenancy;
using CoverGo.Proxies.Auth;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Common.Auth;

internal sealed class JwtMessageForwarder(
    ITenantProvider tenantProvider,
    ServiceAccountLoginCacheProvider serviceAccountLoginCacheProvider,
    AdminLoginCacheProvider adminLoginCacheProvider,
    IAuthService authService,
    ILogger<JwtMessageForwarder> logger) : ITransportMessageInterceptor
{
    private const string AdminPortalAppId = "admin_portal";

    public async ValueTask<TransportMessage<T>> Intercept<T>(TransportMessage<T> message, CancellationToken cancellationToken = default)
        where T : Message
    {
        try
        {
            if (!tenantProvider.TryGetCurrent(out TenantId? tenantId))
            {
                logger.LogWarning("Unable to retrieve tenant information");
                return message;
            }

            Login? login = await serviceAccountLoginCacheProvider.GetServiceAccountLoginAsync(tenantId, cancellationToken);
            if (login == null)
            {
                logger.LogWarning("No valid service account login found for tenant {TenantId}, cannot retrieve token", tenantId);
                
                // Fallback to admin login if service account login is not found
                login = await adminLoginCacheProvider.GetAdminLoginAsync(tenantId, cancellationToken);
                if (login == null)
                {
                    logger.LogWarning("No valid admin login found for tenant {TenantId}, cannot retrieve token", tenantId);
                    return message;
                }
            }
            
            Token? token = await authService.GetInternalAccessTokenAsync(
                tenantId.Value,
                login.Id,
                AdminPortalAppId,
                cancellationToken);

            if (token != null)
            {
                message.Headers["Authorization"] = $"Bearer {token.AccessToken}";
            }
            else
            {
                logger.LogWarning("Failed to retrieve internal token for tenant {TenantId}, userId {UserId}", tenantId, login.Id);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error during message interception for JWT forwarding");
        }

        return message;
    }
}