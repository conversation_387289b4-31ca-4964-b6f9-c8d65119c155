using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Payments.Domain.Payment;
using GuardClauses;

namespace CoverGo.Payments.Domain.PaymentMethod;

public class PayorPaymentMethodAggregate : AggregateRootBase<string>
{
    protected PayorPaymentMethodAggregate()
        : base(Guid.NewGuid().ToString())
    {
    }

    public PayorPaymentMethodAggregate(
        PaymentProvider paymentProvider,
        string payorId,
        PayerData? payerData,
        CardData? cardData)
        : base(Guid.NewGuid().ToString())
    {
        GuardClause.ArgumentIsNotNull(payorId, nameof(payorId));

        PaymentProvider = paymentProvider;
        PayorId = payorId;
        PayerData = payerData;
        CardData = cardData;
    }

    public PaymentProvider PaymentProvider { get; protected set; }
    
    public string PayorId { get; protected set; }
    
    public PayerData? PayerData { get; protected set; }
    
    public CardData? CardData { get; protected set; }
}
