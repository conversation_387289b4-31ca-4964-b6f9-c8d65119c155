﻿using CoverGo.Payments.Domain.Payment;
using GuardClauses;

namespace CoverGo.Payments.Application.Payments.Models;

public class ProcessInitialPaymentResult : RedirectUrlOutput
{
    public PaymentAggregate Payment { get; set; }

    public ProcessInitialPaymentResult(PaymentAggregate payment)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
            
        Payment = payment;
    }
        
    public ProcessInitialPaymentResult(PaymentAggregate payment, Uri? redirectUrl)
        : base(redirectUrl)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
        Payment = payment;
    }
        
    public ProcessInitialPaymentResult(PaymentAggregate payment, Uri? redirectUrl, IDictionary<string, string>? data)
        : base(redirectUrl, data)
    {
        GuardClause.ArgumentIsNotNull(payment, nameof(payment));
            
        Payment = payment;
    }
}