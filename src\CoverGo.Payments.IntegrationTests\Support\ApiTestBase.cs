using System.Net.Http.Headers;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.BuildingBlocks.DataAccess.Mongo;
using CoverGo.BuildingBlocks.DataAccess.Mongo.Multitenancy;
using CoverGo.BuildingBlocks.Domain.Core.Entities;
using CoverGo.Multitenancy;
using CoverGo.Payments.Tests.PaymentsClient;
using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.AspNetCore.TestHost;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.DependencyInjection.Extensions;
using MongoDB.Driver;

namespace CoverGo.Payments.IntegrationTests.Support;

public abstract class ApiTestBase
{
    protected WebApplicationFactory<Program> _applicationFactory;
    protected IServiceScope _serviceScope;
    private readonly Lazy<IConfiguration> _configuration;
    private readonly Lazy<AuthTokenProvider> _authTokenProvider;
    private readonly Lazy<ITestPaymentsClient> _paymentsClient;
    private readonly Lazy<HttpClient> _paymentsHttpClient;
    protected readonly TenantId? _tenantId;

    protected IConfiguration Configuration => _configuration.Value;
    protected AuthTokenProvider AuthTokenProvider => _authTokenProvider.Value;
    protected ITestPaymentsClient PaymentsClient => _paymentsClient.Value;
    protected HttpClient PaymentsHttpClient => _paymentsHttpClient.Value;

    protected ApiTestBase(PaymentsWebApplicationFactory applicationFactory)
    {
        _tenantId = applicationFactory.Tenant;
        _applicationFactory = applicationFactory;
        _serviceScope = applicationFactory.Services.CreateScope();

        ConfigureApiServices(it =>
        {
            it.RemoveAll<ITenantProvider>();
            it.AddSingleton<ITenantProvider>(new ValueTenantProvider(new("covergo")));
        });

        _configuration = new(CreateConfiguration);
        _authTokenProvider =
            new Lazy<AuthTokenProvider>(() => TestClientsCreator.CreateAuthTokenProvider(_configuration.Value));
        _paymentsClient = new Lazy<ITestPaymentsClient>(GetTestPaymentsClient);
        _paymentsHttpClient = new Lazy<HttpClient>(CreatePaymentsHttpClient);
    }

    protected ApiTestBase() : this(new PaymentsWebApplicationFactory())
    {
    }

    private ITestPaymentsClient GetTestPaymentsClient()
    {
        var serviceCollection = new ServiceCollection();
        var httpClientFactory = new TestHttpClientFactory
        {
            Clients =
            {
                ["TestPaymentsClient"] = () =>
                {
                    HttpClient client = _applicationFactory.CreateClient(
                        new WebApplicationFactoryClientOptions { BaseAddress = GetPaymentsUri(Configuration) });

                    string authToken = AuthTokenProvider.GetToken().GetAwaiter().GetResult();
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", authToken);

                    return client;
                }
            }
        };

        serviceCollection.AddSingleton<IHttpClientFactory>(httpClientFactory);
        serviceCollection.AddTestPaymentsClient();
        ServiceProvider serviceProvider = serviceCollection.BuildServiceProvider();
        return serviceProvider.GetRequiredService<ITestPaymentsClient>();
    }

    private IConfiguration CreateConfiguration()
    {
        IConfiguration configuration = TestConfigFactory.Create();
        return configuration;
    }

    protected IMongoCollection<T> GetMongoCollection<T, TId>(TenantId? tenantId = null) where T : IEntity<TId>
    {
        MongoDatabaseFactory mongoDbFactory = _serviceScope.ServiceProvider.GetRequiredService<MongoDatabaseFactory>();
        IMongoDatabase mongoDb = mongoDbFactory.GetDatabaseForTenant(tenantId ??
                                                                     _serviceScope.ServiceProvider
                                                                         .GetService<TenantId>() ??
                                                                     new(this.AuthTokenProvider.TenantId));
        IMongoDatabaseMultitenancyStrategy collectionNameStr =
            _serviceScope.ServiceProvider.GetRequiredService<IMongoDatabaseMultitenancyStrategy>();

        return mongoDb.GetCollection<T>(
            collectionNameStr.GetCollectionName<T>(tenantId ??
                                                   _tenantId ?? throw new ArgumentNullException(nameof(tenantId))));
    }

    protected IRepository<TAggregate, TIdentity> GetRepository<TAggregate, TIdentity>()
        where TAggregate : IAggregateRoot<TIdentity> =>
        _serviceScope.ServiceProvider.GetRequiredService<IRepository<TAggregate, TIdentity>>();

    private static Uri GetPaymentsUri(IConfiguration configuration) =>
        TestClientsCreator.GetServiceUri(configuration, "payments");

    private HttpClient CreatePaymentsHttpClient() => _applicationFactory.CreateClient();

    protected void ConfigureApiServices(Action<IServiceCollection> servicesConfiguration)
    {
        _applicationFactory = _applicationFactory.WithWebHostBuilder(builder =>
        {
            builder.ConfigureTestServices(servicesConfiguration);
        });
        _serviceScope = _applicationFactory.Services.CreateScope();
    }
}