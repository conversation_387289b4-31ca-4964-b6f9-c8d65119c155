﻿using FluentValidation;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.TokenizePaymentInitialization
{
    public class TokenizePaymentInitializationCommandValidator : AbstractValidator<TokenizePaymentInitializationCommand>
    {
        public TokenizePaymentInitializationCommandValidator(ILogger<TokenizePaymentInitializationCommandValidator> logger)
        {
            RuleFor(c => c.CurrencyDesc).NotEmpty().WithMessage("No currency description found.");
            RuleFor(c => c.CurrencyCode).NotEmpty().WithMessage("No currency code found.");
            RuleFor(c => c.Amount).GreaterThan(decimal.Zero).WithMessage("Amount should be greater than 0.");
            RuleFor(c => c.DecimalPrecision).GreaterThan(0).WithMessage("DecimalPrecision should be greater than 0.");
            RuleFor(c => c.PaymentProvider).NotEmpty().WithMessage("No payment provider found.");

            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }
    }
}
