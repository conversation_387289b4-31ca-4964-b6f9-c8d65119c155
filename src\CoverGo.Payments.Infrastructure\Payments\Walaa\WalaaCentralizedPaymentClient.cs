﻿using System.Net.Mime;
using System.Text;
using CoverGo.Payments.Infrastructure.Payments.Walaa.Models;
using GuardClauses;
using Newtonsoft.Json;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace CoverGo.Payments.Infrastructure.Payments.Walaa;

public interface IWalaaCentralizedPaymentClient
{
    Task<InvoiceUploadResponse> InvoiceUploadAsync(InvoiceUploadRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken);
    Task<VerifyPaymentResponse> VerifyPaymentAsync(VerifyPaymentRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken);
    Task<UpdateInvoiceExpiryDateResponse> UpdateInvoiceExpiryDateAsync(UpdateInvoiceExpiryDateRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken);
}

public class WalaaCentralizedPaymentClient(IHttpClientFactory httpClientFactory) : IWalaaCentralizedPaymentClient
{
    public Task<InvoiceUploadResponse> InvoiceUploadAsync(InvoiceUploadRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken)
        => PostAsync<InvoiceUploadRequest, InvoiceUploadResponse>("/CentralizedPayment/InvoiceUpload", request, baseAddress, username, password, cancellationToken);

    public Task<VerifyPaymentResponse> VerifyPaymentAsync(VerifyPaymentRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken)
        => PostAsync<VerifyPaymentRequest, VerifyPaymentResponse>("/CentralizedPayment/VerifyPayment", request, baseAddress, username, password, cancellationToken);

    public Task<UpdateInvoiceExpiryDateResponse> UpdateInvoiceExpiryDateAsync(UpdateInvoiceExpiryDateRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken)
        => PostAsync<UpdateInvoiceExpiryDateRequest, UpdateInvoiceExpiryDateResponse>("/CentralizedPayment/UpdateInvoiceExpiryDate", request, baseAddress, username, password, cancellationToken);

    private async Task<TResponse> PostAsync<TRequest, TResponse>(string requestUri, TRequest request, Uri baseAddress, string username, string password, CancellationToken cancellationToken)
    {
        using HttpClient httpClient = httpClientFactory.CreateClient();
        httpClient.BaseAddress = baseAddress;
        httpClient.DefaultRequestHeaders.Authorization = new BasicAuthenticationHeaderValue(username, password);

        using StringContent jsonContent = new(JsonSerializer.Serialize(request), Encoding.UTF8, MediaTypeNames.Application.Json);

        using HttpResponseMessage response = await httpClient.PostAsync(requestUri, jsonContent, cancellationToken);

        string responseString = await response.Content.ReadAsStringAsync(cancellationToken);

        TResponse? responseObject = JsonConvert.DeserializeObject<TResponse>(responseString);
        GuardClause.ArgumentIsNotNull(responseObject, nameof(responseObject));

        return responseObject!;
    }
}
