﻿using AutoMapper;
using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.Payments.Application.Payments.Contracts;
using CoverGo.Payments.Application.Payments.Services;
using CoverGo.Payments.Domain.Payment;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.Payments.Commands.RegisterPayment
{
    public class RegisterPaymentCommandHandler(IMapper mapper, IPaymentService paymentService, ILogger<RegisterPaymentCommandHandler> logger)
        : ICommandHandler<RegisterPaymentCommand, PaymentDto>
    {
        /// <param name="registerPaymentCommand"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        public async Task<PaymentDto> Handle(RegisterPaymentCommand registerPaymentCommand,
            CancellationToken cancellationToken)
        {
            logger.LogInformation("RegisterPaymentCommandHandler.Handle: Starting payment registration. PolicyId: {PolicyId}, PayorId: {PayorId}, InvoiceNumber: {InvoiceNumber}, PaymentProvider: {PaymentProvider}, TransactionType: {TransactionType}, Amount: {Amount} {CurrencyCode}, Status: {Status}",
                registerPaymentCommand.PolicyId,
                registerPaymentCommand.PayorId,
                registerPaymentCommand.InvoiceNumber ?? "null",
                registerPaymentCommand.PaymentProvider,
                registerPaymentCommand.TransactionType,
                registerPaymentCommand.Amount,
                registerPaymentCommand.CurrencyCode,
                registerPaymentCommand.Status);

            try
            {
                PaymentAggregate payment =
                    await paymentService.RegisterPaymentAsync(registerPaymentCommand, cancellationToken);

                var result = mapper.Map<PaymentDto>(payment);

                logger.LogInformation("RegisterPaymentCommandHandler.Handle: Payment registration completed. PaymentId: {PaymentId}, PolicyId: {PolicyId}, PayorId: {PayorId}, FinalStatus: {Status}",
                    payment.Id,
                    payment.PolicyId,
                    payment.PayorId,
                    payment.Status);

                return result;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "RegisterPaymentCommandHandler.Handle: Unexpected error during payment registration. PolicyId: {PolicyId}, PayorId: {PayorId}, TransactionType: {TransactionType}",
                    registerPaymentCommand.PolicyId,
                    registerPaymentCommand.PayorId,
                    registerPaymentCommand.TransactionType);
                throw;
            }
        }
    }
}