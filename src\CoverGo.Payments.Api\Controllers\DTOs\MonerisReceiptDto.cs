using CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

namespace CoverGo.Payments.Api.Controllers.DTOs;

/// <summary>
/// DTO for Moneris receipt data returned by the API
/// </summary>
public class MonerisReceiptDto
{
    /// <summary>
    /// Indicates if the request was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// The ticket ID from Moneris
    /// </summary>
    public string? Ticket { get; set; }

    /// <summary>
    /// The token from Moneris (if available)
    /// </summary>
    public string? Token { get; set; }

    /// <summary>
    /// Error information (if any)
    /// </summary>
    public Dictionary<string, object>? Error { get; set; }

    /// <summary>
    /// Receipt information
    /// </summary>
    public ReceiptDto? Receipt { get; set; }

    /// <summary>
    /// Request information
    /// </summary>
    public RequestDto? Request { get; set; }

    /// <summary>
    /// Creates a DTO from ResponseData
    /// </summary>
    /// <param name="responseData">The ResponseData from Moneris</param>
    /// <returns>MonerisReceiptDto</returns>
    public static MonerisReceiptDto FromResponseData(ResponseData responseData)
    {
        return new MonerisReceiptDto
        {
            IsSuccess = responseData.IsSuccess,
            Ticket = responseData.Ticket,
            Token = responseData.Token,
            Error = responseData.Error,
            Receipt = responseData.Receipt != null ? ReceiptDto.FromReceipt(responseData.Receipt) : null,
            Request = responseData.Request != null ? RequestDto.FromRequest(responseData.Request) : null
        };
    }
}

/// <summary>
/// DTO for receipt information
/// </summary>
public class ReceiptDto
{
    /// <summary>
    /// Result of the transaction
    /// </summary>
    public string? Result { get; set; }

    /// <summary>
    /// Credit card receipt details
    /// </summary>
    public CreditCardReceiptDto? CreditCard { get; set; }

    public static ReceiptDto FromReceipt(Receipt receipt)
    {
        return new ReceiptDto
        {
            Result = receipt.Result,
            CreditCard = receipt.Cc != null ? CreditCardReceiptDto.FromCreditCardReceipt(receipt.Cc) : null
        };
    }
}

/// <summary>
/// DTO for credit card receipt details
/// </summary>
public class CreditCardReceiptDto
{
    public string? OrderNo { get; set; }
    public string? TransactionNo { get; set; }
    public string? ReferenceNo { get; set; }
    public string? TransactionCode { get; set; }
    public string? TransactionType { get; set; }
    public string? TransactionDateTime { get; set; }
    public string? Amount { get; set; }
    public string? ResponseCode { get; set; }
    public string? ApprovalCode { get; set; }
    public string? CardType { get; set; }
    public string? InvoiceNumber { get; set; }
    public string? First6Last4 { get; set; }
    public string? ExpiryDate { get; set; }
    public string? Result { get; set; }

    public static CreditCardReceiptDto FromCreditCardReceipt(CreditCardReceipt receipt)
    {
        return new CreditCardReceiptDto
        {
            OrderNo = receipt.OrderNo,
            TransactionNo = receipt.TransactionNo,
            ReferenceNo = receipt.ReferenceNo,
            TransactionCode = receipt.TransactionCode,
            TransactionType = receipt.TransactionType,
            TransactionDateTime = receipt.TransactionDateTime,
            Amount = receipt.Amount,
            ResponseCode = receipt.ResponseCode,
            ApprovalCode = receipt.ApprovalCode,
            CardType = receipt.CardType,
            InvoiceNumber = receipt.InvoiceNumber,
            First6Last4 = receipt.First6Last4,
            ExpiryDate = receipt.ExpiryDate,
            Result = receipt.Result
        };
    }
}

/// <summary>
/// DTO for request information
/// </summary>
public class RequestDto
{
    public string? TxnTotal { get; set; }
    public string? OrderNo { get; set; }
    public string? CustId { get; set; }
    public string? Ticket { get; set; }
    public string? PayByToken { get; set; }
    public string? DynamicDescriptor { get; set; }
    public CustomerInfoDto? CustomerInfo { get; set; }
    public AddressDto? Billing { get; set; }
    public AddressDto? Shipping { get; set; }

    public static RequestDto FromRequest(Request request)
    {
        return new RequestDto
        {
            TxnTotal = request.TxnTotal,
            OrderNo = request.OrderNo,
            CustId = request.CustId,
            Ticket = request.Ticket,
            PayByToken = request.PayByToken,
            DynamicDescriptor = request.DynamicDescriptor,
            CustomerInfo = request.CustomerInfo != null ? CustomerInfoDto.FromCustomerInfo(request.CustomerInfo) : null,
            Billing = request.Billing != null ? AddressDto.FromAddress(request.Billing) : null,
            Shipping = request.Shipping != null ? AddressDto.FromAddress(request.Shipping) : null
        };
    }
}

/// <summary>
/// DTO for customer information
/// </summary>
public class CustomerInfoDto
{
    public string? Email { get; set; }
    public string? Instructions { get; set; }

    public static CustomerInfoDto FromCustomerInfo(CustomerInfo customerInfo)
    {
        return new CustomerInfoDto
        {
            Email = customerInfo.Email,
            Instructions = customerInfo.Instructions
        };
    }
}

/// <summary>
/// DTO for address information
/// </summary>
public class AddressDto
{
    public string? AddressLine1 { get; set; }
    public string? AddressLine2 { get; set; }
    public string? City { get; set; }
    public string? Province { get; set; }
    public string? PostalCode { get; set; }
    public string? Country { get; set; }

    public static AddressDto FromAddress(Address address)
    {
        return new AddressDto
        {
            AddressLine1 = address.AddressLine1,
            AddressLine2 = address.AddressLine2,
            City = address.City,
            Province = address.Province,
            PostalCode = address.PostalCode,
            Country = address.Country
        };
    }
}
