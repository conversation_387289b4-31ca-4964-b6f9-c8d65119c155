using MongoDB.Driver;

namespace CoverGo.Payments.Application.Common;

public static class FilterExtensions
{
    public static FilterDefinition<TAggregate> GetOrFilter<TAggregate, TWhere>(
        this IReadOnlyCollection<TWhere> wheres,
        Func<TWhere?, FilterDefinition<TAggregate>> getFilter
    )
    {
        if (!wheres.Any())
            return Builders<TAggregate>.Filter.Empty;

        FilterDefinition<TAggregate> filter = getFilter(wheres.FirstOrDefault());

        foreach (TWhere where in wheres.Skip(1))
        {
            filter |= getFilter(where);
        }

        return filter;
    }

    public static FilterDefinition<TAggregate> GetAndFilter<TAggregate, TWhere>(
        this IReadOnlyCollection<TWhere> wheres,
        Func<TWhere?, FilterDefinition<TAggregate>> getFilter
    )
    {
        if (!wheres.Any())
            return Builders<TAggregate>.Filter.Empty;

        FilterDefinition<TAggregate> filter = getFilter(wheres.FirstOrDefault());

        foreach (TWhere where in wheres.Skip(1))
        {
            filter &= getFilter(where);
        }

        return filter;
    }
}
