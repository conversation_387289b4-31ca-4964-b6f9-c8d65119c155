﻿using FluentValidation;
using Microsoft.Extensions.Logging;
using CoverGo.Payments.Domain.Payment;

namespace CoverGo.Payments.Application.Payments.Commands.CancelPayment
{
    public class CancelPaymentCommandValidator : AbstractValidator<CancelPaymentCommand>
    {
        public CancelPaymentCommandValidator(
            ILogger<CancelPaymentCommandValidator> logger)
        {
            RuleFor(pc => pc.PaymentId).NotEmpty().WithMessage("No paymentId found.");
            
            RuleFor(c => c.CancellationStatus)
                .Must(BeValidCancellationStatus)
                .When(c => c.CancellationStatus.HasValue)
                .WithMessage("Invalid cancellation status. Valid statuses are: Canceled, Expired, Failed.");
            
            logger.LogTrace("----- INSTANCE CREATED - {ClassName}", GetType().Name);
        }

        private static bool BeValidCancellationStatus(PaymentStatus? status)
        {
            if (!status.HasValue) return true;
            
            var validStatuses = new[] { PaymentStatus.Canceled, PaymentStatus.Expired, PaymentStatus.Failed };
            return validStatuses.Contains(status.Value);
        }
    }
}
