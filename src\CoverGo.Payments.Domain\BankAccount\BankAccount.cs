using CoverGo.BuildingBlocks.Domain.Core.Entities;

namespace CoverGo.Payments.Domain.BankAccount;

public class BankAccount : AggregateRootBase<string>
{
    public BankAccount() : base(Guid.NewGuid().ToString())
    {
    }

    public required string Country { get; set; }
    public required string Currency { get; set; }
    public required string AccountHolderName { get; set; }
    public string? AccountNumber { get; set; }
    public required string BankName { get; set; }
    public string? Iban { get; set; }
    public required string Bic { get; set; }
    
    #region Entity and Payor Information
    public required string EntityId { get; init; }
    public required string PayorId { get; init; }
    public required PayorType PayorType { get; init; }
    #endregion
    
    private IEnumerable<BankAccountUsage> _usedFor = [];
    public required IEnumerable<BankAccountUsage> UsedFor 
    { 
        get => _usedFor; 
        set
        {
            ArgumentNullException.ThrowIfNull(value);
            if (!value.Any())
                throw new BankAccountUsedForInvalidException();
            _usedFor = value;
        }
    }

    public void UpdateBankAccount(
        string country,
        string currency,
        string accountHolderName,
        string bankName,
        string bic,
        IEnumerable<BankAccountUsage> usedFor,
        string? accountNumber = null,
        string? iban = null)
    {
        Country = country;
        Currency = currency;
        AccountHolderName = accountHolderName;
        BankName = bankName;
        Bic = bic;
        UsedFor = usedFor;
        AccountNumber = accountNumber;
        Iban = iban;
    }

    public string GetUniqueIdentifier()
    {
        // Used for duplicate detection - combining key fields
        var identifier = $"{AccountHolderName}|{AccountNumber}|{BankName}|{Iban}";
        return identifier.ToLowerInvariant();
    }
} 