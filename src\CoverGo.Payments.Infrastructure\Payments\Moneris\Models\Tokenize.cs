﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class Tokenize
{
    [JsonProperty("success")]
    public string? Success { get; set; }

    [JsonProperty("first4last4")]
    public string? First4Last4 { get; set; }

    [JsonProperty("datakey")]
    public string? DataKey { get; set; }

    [JsonProperty("status")]
    public string? Status { get; set; }

    [JsonProperty("message")]
    public string? Message { get; set; }
}