﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class RefundResponse
{
    [JsonProperty(PayUContainer.PropsName.PayuPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public long PayuPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
    public int Code { get; set; }

    [JsonProperty(PayUContainer.PropsName.Message, NullValueHandling = NullValueHandling.Ignore)]
    public string Message { get; set; }

    [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
    public string Status { get; set; }

    [JsonProperty(PayUContainer.PropsName.RefundRequestId, NullValueHandling = NullValueHandling.Ignore)]
    public string RefundRequestId { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.MerchantRefundReference, NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantRefundReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount, NullValueHandling = NullValueHandling.Ignore)]
    public string Amount { get; set; }
}