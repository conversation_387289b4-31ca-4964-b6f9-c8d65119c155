﻿mutation CancelPaymentInitializations($input: CancelPaymentInitializationsInput!) {
    cancelPaymentInitializations(input: $input) {
        paymentInitializationsCancellationResult {
            isSuccessful
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                    code
                }
            }
        }
    }
}