﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class CreditCardReceipt
{
    [JsonProperty("order_no")]
    public string? OrderNo { get; set; }

    [JsonProperty("cust_id")]
    public string? CustId { get; set; }

    [JsonProperty("transaction_no")]
    public string? TransactionNo { get; set; }

    [JsonProperty("reference_no")]
    public string? ReferenceNo { get; set; }

    [JsonProperty("transaction_code")]
    public string? TransactionCode { get; set; }

    [JsonProperty("transaction_type")]
    public string? TransactionType { get; set; }

    [JsonProperty("transaction_date_time")]
    public string? TransactionDateTime { get; set; }

    [JsonProperty("corporate_card")]
    public string? CorporateCard { get; set; }

    [JsonProperty("amount")]
    public string? Amount { get; set; }

    [JsonProperty("response_code")]
    public string? ResponseCode { get; set; }

    [JsonProperty("iso_response_code")]
    public string? IsoResponseCode { get; set; }

    [JsonProperty("approval_code")]
    public string? ApprovalCode { get; set; }

    [JsonProperty("card_type")]
    public string? CardType { get; set; }

    [JsonProperty("dynamic_descriptor")]
    public string? DynamicDescriptor { get; set; }

    [JsonProperty("invoice_number")]
    public string? InvoiceNumber { get; set; }

    [JsonProperty("customer_code")]
    public string? CustomerCode { get; set; }

    [JsonProperty("eci")]
    public string? Eci { get; set; }

    [JsonProperty("cvd_result_code")]
    public string? CvdResultCode { get; set; }

    [JsonProperty("avs_result_code")]
    public string? AvsResultCode { get; set; }

    [JsonProperty("cavv_result_code")]
    public string? CavvResultCode { get; set; }

    [JsonProperty("first6last4")]
    public string? First6Last4 { get; set; }

    [JsonProperty("expiry_date")]
    public string? ExpiryDate { get; set; }

    [JsonProperty("recur_success")]
    public string? RecurSuccess { get; set; }

    [JsonProperty("issuer_id")]
    public string? IssuerId { get; set; }

    [JsonProperty("is_debit")]
    public string? IsDebit { get; set; }

    [JsonProperty("ecr_no")]
    public string? EcrNo { get; set; }

    [JsonProperty("batch_no")]
    public string? BatchNo { get; set; }

    [JsonProperty("sequence_no")]
    public string? SequenceNo { get; set; }

    [JsonProperty("result")]
    public string? Result { get; set; }

    [JsonProperty("tokenize")]
    public Tokenize? Tokenize { get; set; }

    [JsonProperty("fraud")]
    public Fraud? Fraud { get; set; }

    [JsonProperty("mcp")]
    public Mcp? Mcp { get; set; }
}