using CoverGo.BuildingBlocks.Api.GraphQl.Errors.Types.InputDataValidation;
using CoverGo.Payments.Api.Common.Errors;
using CoverGo.Payments.Application.Refunds.Commands.RefundPayment;
using CoverGo.Payments.Application.Refunds.Contracts;
using MediatR;

namespace CoverGo.Payments.Api.Refunds.RefundPayment;

[MutationType]
public class RefundPaymentMutation
{
    [Error(typeof(InputDataValidationError))]
    [Error(typeof(DomainError))]
    [UseMutationConvention(PayloadFieldName = "payment")]
    //[Authorize]
    public async Task<PaymentRefundDto> RefundPayment(
        RefundPaymentCommand input,
        [Service] IMediator commandProcessor,
        CancellationToken cancellationToken) =>
        await commandProcessor.Send(input, cancellationToken);
}