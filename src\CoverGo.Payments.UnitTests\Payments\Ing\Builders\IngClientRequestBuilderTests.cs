﻿using CoverGo.Payments.Infrastructure.Payments.Ing.Builders;
using FluentAssertions;
using Newtonsoft.Json;

namespace CoverGo.Payments.UnitTests.Payments.Ing.Builders;

public class IngClientRequestBuilderTests
{
    [Fact]
    public async Task GIVEN_valid_parameters_WHEN_BuildRequestMessage_called_for_POST_THEN_should_return_correct_HttpRequestMessage()
    {
        var url = new Uri("https://api.ing.com/resource");
        HttpMethod method = HttpMethod.Post;
        const string apiKey = "testApiKey";
        var content = new { Name = "John", Age = 30 };
        
        HttpRequestMessage message = IngClientRequestBuilder.BuildRequestMessage(url, method, apiKey, content);

        message.Should().NotBeNull();
        message.Method.Should().Be(HttpMethod.Post);
        message.RequestUri.Should().Be(url);
        message.Headers.Should().Contain(header => header.Key == "Authorization" && header.Value.Contains($"Bearer {apiKey}"));
        message.Headers.Should().Contain(header => header.Key == "Host" && header.Value.Contains(url.Host));
        
        message.Content.Should().NotBeNull();
        string jsonContent = JsonConvert.SerializeObject(content);
        string contentAsString =  await message.Content!.ReadAsStringAsync();
        contentAsString.Should().Be(jsonContent);
        message.Content.Headers.ContentType!.MediaType.Should().Be("application/json");
    }

    [Fact]
    public void GIVEN_valid_parameters_WHEN_BuildRequestMessage_called_for_GET_THEN_should_not_set_content()
    {
        var url = new Uri("https://api.ing.com/resource");
        HttpMethod method = HttpMethod.Get;
        const string apiKey = "testApiKey";
        
        HttpRequestMessage message = IngClientRequestBuilder.BuildRequestMessage(url, method, apiKey, null);

        message.Should().NotBeNull();
        message.Method.Should().Be(HttpMethod.Get);
        message.RequestUri.Should().Be(url);
        message.Headers.Should().Contain(header => header.Key == "Authorization" && header.Value.Contains($"Bearer {apiKey}"));
        message.Headers.Should().Contain(header => header.Key == "Host" && header.Value.Contains(url.Host));
        
        message.Content.Should().BeNull();
    }

    [Fact]
    public void GIVEN_valid_parameters_with_additional_headers_WHEN_BuildRequestMessage_called_THEN_should_set_additional_headers()
    {
        var url = new Uri("https://api.ing.com/resource");
        HttpMethod method = HttpMethod.Post;
        const string apiKey = "testApiKey";
        var content = new { Name = "John", Age = 30 };
        IDictionary<string, string> additionalHeaders = new Dictionary<string, string>
        {
            { "Custom-Header", "CustomValue" }
        };
        
        HttpRequestMessage message = IngClientRequestBuilder.BuildRequestMessage(url, method, apiKey, content,additionalHeaders);

        message.Should().NotBeNull();
        message.Headers.Should().Contain(header => header.Key == "Custom-Header" && header.Value.Contains("CustomValue"));
    }

    [Theory]
    [InlineData(null, "POST", "testApiKey", "url")]
    [InlineData("https://api.ing.com/resource", null, "testApiKey", "method")]
    [InlineData("https://api.ing.com/resource", "POST", null, "apiKey")]
    public void GIVEN_null_parameter_WHEN_BuildRequestMessage_called_THEN_should_throw_ArgumentNullException(string url, string method, string apiKey, string expectedParameterName)
    {
        Uri uri = url == null ? null : new Uri(url);
        HttpMethod httpMethod = method == null ? null : new HttpMethod(method);
        
        Action act = () => IngClientRequestBuilder.BuildRequestMessage(uri, httpMethod, apiKey, null);

        act.Should().Throw<ArgumentNullException>().WithMessage($"*{expectedParameterName}*");
    }

    [Fact]
    public void GIVEN_POST_method_but_null_content_WHEN_BuildRequestMessage_called_THEN_should_not_set_content()
    {
        var url = new Uri("https://api.ing.com/resource");
        HttpMethod method = HttpMethod.Post;
        const string apiKey = "testApiKey";
        
        HttpRequestMessage message = IngClientRequestBuilder.BuildRequestMessage(url, method, apiKey, null);

        message.Should().NotBeNull();
        message.Method.Should().Be(HttpMethod.Post);
        message.Content.Should().BeNull();  // Content should be null because content parameter was null
    }

    [Fact]
    public void GIVEN_DELETE_method_WHEN_BuildRequestMessage_called_THEN_should_not_set_content()
    {
        var url = new Uri("https://api.ing.com/resource");
        HttpMethod method = HttpMethod.Delete;
        string apiKey = "testApiKey";
        
        HttpRequestMessage message = IngClientRequestBuilder.BuildRequestMessage(url, method, apiKey, new { Name = "John" });

        message.Should().NotBeNull();
        message.Method.Should().Be(HttpMethod.Delete);
        
        message.Content.Should().BeNull();
    }
}