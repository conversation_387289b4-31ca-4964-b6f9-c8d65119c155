using CoverGo.Payments.Domain.BankAccount;

using HotChocolate.Authorization;
using HotChocolate.Data;

using MongoDB.Driver;

namespace CoverGo.Payments.Api.BankAccounts;

[QueryType]
public class BankAccountQueries
{
    [Authorize]
    [UseSingleOrDefault]
    public IExecutable<BankAccount> BankAccount(
        [Service] IMongoCollection<BankAccount> bankAccounts,
        BankAccountId id) =>
        bankAccounts.Find(x => x.Id == id.Value).AsExecutable();

    [Authorize]
    [UseOffsetPaging]
    [UseSorting]
    [UseFiltering]
    public IExecutable<BankAccount> BankAccounts(
        [Service] IMongoCollection<BankAccount> bankAccounts)
        => bankAccounts.AsQueryable().AsExecutable();
} 