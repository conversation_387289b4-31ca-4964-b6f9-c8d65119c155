mode: ContinuousDeployment
branches:
  master:
    regex: ^master$|^main$
    mode: ContinuousDeployment
    tag: rc
    increment: Minor
    source-branches: []
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: true
  release:
    regex: ^release[/-]
    mode: ContinuousDeployment
    tag: rc
    increment: Patch
    prevent-increment-of-merged-branch-version: false
    track-merge-target: false
    source-branches: [ 'master', 'main' ]
    tracks-release-branches: false
    is-release-branch: true
    is-mainline: true   
  unknown:
    mode: ContinuousDeployment
    tag: '{BranchName}'
    regex: (?<BranchName>.+)
    source-branches:
    - main
    - release
ignore:
  sha: []
