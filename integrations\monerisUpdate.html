<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Moneris Hosted Tokenization</title>
    <script>
        const API_BASE_URL = 'https://api-v2.dev.covergo.cloud/graphql/';
        const TENANT_HEADER = 'gms_dev';

        let paymentId = null;
        let apiUrl = null;
        let isButtonPressed = false;

        async function initializePayment() {
            const query = `
                mutation InitializeUpdatePayment($input: InitializeUpdatePaymentInput!) {
                    initializeUpdatePayment(input: $input) {
                        initializeUpdatePaymentResult {
                            payment { id }
                            redirectUrl
                            data { key, value }
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors { propertyPath, message, code }
                            }
                            ... on DomainError { message, code }
                        }
                    }
                }
            `;

            const variables = {
                input: {
                    currencyDesc: "CAD",
                    policyId: "policyIdTest",
                    payorId: "payorIdTest",
                    paymentProvider: "MONERIS"
                }
            };

            try {
                const response = await fetchGraphQL(query, variables, TENANT_HEADER);
                processInitializePaymentResponse(response);
            } catch (error) {
                console.error("Error initializing payment:", error);
            }
        }

        function processInitializePaymentResponse(response) {
            const result = response?.data?.initializeUpdatePayment?.initializeUpdatePaymentResult;
            if (result) {
                paymentId = result.payment.id;
                apiUrl = result.data.find(item => item.key === 'apiUrl')?.value;
                loadMonerisIframe(result.redirectUrl);
            } else {
                console.error("Failed to retrieve payment initialization data:", response.errors);
            }
        }

        function loadMonerisIframe(redirectUrl) {
            if (!redirectUrl) {
                console.error("Redirect URL is missing.");
                return;
            }

            const iframeUrl = `${redirectUrl}&pmmsg=true&css_body=background:green;&css_textbox=border-width:2px;&display_labels=1&css_textbox_pan=width:140px;&enable_exp=1&css_textbox_exp=width:40px;&enable_cvd=1&css_textbox_cvd=width:40px&enable_exp_formatting=1&enable_cc_formatting=1`;
            document.getElementById("monerisFrame").src = iframeUrl;
        }

        function submitPaymentTokenization() {
            if (!apiUrl) {
                console.error("API URL not set. Cannot submit tokenization request.");
                return;
            }

            document.getElementById('monerisFrame').contentWindow.postMessage('tokenize', `${apiUrl}/HPPtoken/index.php`);
            isButtonPressed = true;
        }

        function handleMonerisIframeResponse(event) {
            if (!isButtonPressed || event.origin !== apiUrl) return;

            try {
                const { responseCode, dataKey, errorMessage } = JSON.parse(event.data);
                displayMonerisResponse(event.origin, responseCode, dataKey, errorMessage);
                if (responseCode[0] === "001" && dataKey) {
                    processTokenFinalization(dataKey);
                } else {
                    console.error("Tokenization failed:", errorMessage);
                }
            } catch (error) {
                console.error("Error processing Moneris response:", error);
            }
        }

        function displayMonerisResponse(origin, responseCode, dataKey, errorMessage) {
            document.getElementById("monerisResponse").innerHTML = `${origin} SENT - Response Code: ${responseCode} - Token: ${dataKey} - Error: ${errorMessage}`;
            document.getElementById("monerisFrame").style.display = 'none';
        }

        async function processTokenFinalization(token) {
            try {
                const dynamicFields = { token: token };
                await finalizePayment(dynamicFields);
            } catch (error) {
                console.error("Error finalizing payment:", error);
            }
        }

        async function finalizePayment(dynamicFields) {
            const mutation = `
                mutation FinalizePayment($input: FinalizePaymentInput!) {
                    finalizePayment(input: $input) {
                        payment { id, paymentStatus }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors { propertyPath, message, code }
                            }
                        }
                    }
                }
            `;

            const variables = {
                input: {
                    paymentId: paymentId,
                    dynamicFields: dynamicFields,
                }
            };

            try {
                const response = await fetchGraphQL(mutation, variables, TENANT_HEADER);
                console.log("Payment finalized:", response);
                alert("Payment finalized:", response.data.finalizePayment.payment.paymentStatus);
            } catch (error) {
                console.error("Error sending finalization mutation:", error);
            }
        }

        async function fetchGraphQL(query, variables, tenant) {
            const response = await fetch(API_BASE_URL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Tenant': tenant
                },
                body: JSON.stringify({ query, variables })
            });
            return response.json();
        }

        // Event Listeners
        window.addEventListener("message", handleMonerisIframeResponse, false);
        window.onload = initializePayment;
    </script>
</head>
<body>
<h1>Moneris Hosted Tokenization</h1>
<div id="monerisResponse"></div>

<!-- Moneris Hosted Tokenization IFrame -->
<iframe id="monerisFrame" frameborder="0" width="400px" height="250px"></iframe>

<!-- Submit Button -->
<input type="button" onClick="submitPaymentTokenization()" value="Submit Payment">
</body>
</html>
