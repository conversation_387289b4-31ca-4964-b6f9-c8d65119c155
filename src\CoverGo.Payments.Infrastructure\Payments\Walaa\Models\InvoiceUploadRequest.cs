﻿using System.Text.Json.Serialization;

namespace CoverGo.Payments.Infrastructure.Payments.Walaa.Models;

public class InvoiceUploadRequest
{
    [JsonPropertyName("ProductDescription")]
    public string? ProductDescription { get; set; }

    [JsonPropertyName("ProductModule")]
    public string? ProductModule { get; set; }

    [JsonPropertyName("TotalAmount")]
    public string? TotalAmount { get; set; }

    [JsonPropertyName("ProviderCompanyName")]
    public string? ProviderCompanyName { get; set; }

    [JsonPropertyName("InvoiceDetails")]
    public InvoiceUploadRequestInvoiceDetail[]? InvoiceDetails { get; set; }
}

public class InvoiceUploadRequestInvoiceDetail
{
    [JsonPropertyName("InvoiceholderID")]
    public string? InvoiceholderID { get; set; }

    [JsonPropertyName("CustomerNameEn")]
    public string? CustomerNameEn { get; set; }

    [JsonPropertyName("CustomerNameAr")]
    public string? CustomerNameAr { get; set; }

    [JsonPropertyName("RequestExpiryDate")]
    public string? RequestExpiryDate { get; set; }

    [JsonPropertyName("PaymentDescriptionEn")]
    public string? PaymentDescriptionEn { get; set; }

    [JsonPropertyName("PaymentDescriptionAr")]
    public string? PaymentDescriptionAr { get; set; }

    [JsonPropertyName("InvoiceAmount")]
    public string? InvoiceAmount { get; set; }

    [JsonPropertyName("InvoiceAmountWithoutVat")]
    public string? InvoiceAmountWithoutVat { get; set; }

    [JsonPropertyName("InvoiceVatAmount")]
    public string? InvoiceVatAmount { get; set; }

    [JsonPropertyName("InvoiceVatPercentage")]
    public string? InvoiceVatPercentage { get; set; }

    [JsonPropertyName("InvoiceNumber")]
    public string? InvoiceNumber { get; set; }

    [JsonPropertyName("CustomerMobileNumber")]
    public string? CustomerMobileNumber { get; set; }

    [JsonPropertyName("CustomerEmail")]
    public string? CustomerEmail { get; set; }

    [JsonPropertyName("InvoiceBreakdown")]
    public InvoiceBreakdownItem[]? InvoiceBreakdown { get; set; }

    [JsonPropertyName("InvoiceDiscounts")]
    public InvoiceDiscount[]? InvoiceDiscounts { get; set; }
}

public class InvoiceBreakdownItem
{
    [JsonPropertyName("BreakDownDescription")]
    public string? BreakDownDescription { get; set; }

    [JsonPropertyName("BreakDownDescriptionArabic")]
    public string? BreakDownDescriptionArabic { get; set; }

    [JsonPropertyName("BreakdownTypeID")]
    public string? BreakdownTypeID { get; set; }

    [JsonPropertyName("BreakdownAmount")]
    public string? BreakdownAmount { get; set; }

    [JsonPropertyName("BreakdownPercentage")]
    public string? BreakdownPercentage { get; set; }
}

public class InvoiceDiscount
{
    [JsonPropertyName("DiscountDescription")]
    public string? DiscountDescription { get; set; }

    [JsonPropertyName("DiscountDescriptionArabic")]
    public string? DiscountDescriptionArabic { get; set; }

    [JsonPropertyName("DiscountTypeID")]
    public string? DiscountTypeID { get; set; }

    [JsonPropertyName("DiscountAmount")]
    public string? DiscountAmount { get; set; }

    [JsonPropertyName("DiscountPercentage")]
    public string? DiscountPercentage { get; set; }
}
