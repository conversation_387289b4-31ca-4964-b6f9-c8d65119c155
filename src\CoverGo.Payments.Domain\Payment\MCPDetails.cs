﻿using CoverGo.BuildingBlocks.Domain.Core.ValueObjects;

namespace CoverGo.Payments.Domain.Payment;

public class MCPDetails(
    string? mcpMerchantSettlementAmount,
    string? mcpCurrencyCode,
    string? mcpRate,
    int? mcpDecimalPrecision,
    string? mcpAmount,
    string? mcpCurrencyDesc)
    : ValueObject
{
    public string? McpMerchantSettlementAmount { get; private set; } = mcpMerchantSettlementAmount;

    public string? McpCurrencyCode { get; private set; } = mcpCurrencyCode;

    public string? McpRate { get; private set; } = mcpRate;

    public int? McpDecimalPrecision { get; private set; } = mcpDecimalPrecision;

    public string? McpAmount { get; private set; } = mcpAmount;

    public string? McpCurrencyDesc { get; private set; } = mcpCurrencyDesc;

    protected override IEnumerable<object> GetEqualityComponents()
    {
        yield return McpMerchantSettlementAmount!;
        yield return McpCurrencyCode!;
        yield return McpRate!;
        yield return McpDecimalPrecision!;
        yield return McpAmount!;
        yield return McpCurrencyDesc!;
    }
}