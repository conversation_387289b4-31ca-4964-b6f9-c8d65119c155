<!DOCTYPE html>
<html>
<head>
    <title>CoverGo Payment</title>
    <meta http-equiv="X-UA-Compatible" content="IE=EmulateIE9">
    <script src="https://gatewayt.moneris.com/chktv2/js/chkt_v2.00.js"></script>
    <script type="text/javascript">
        var myCheckout = new monerisCheckout();
        myCheckout.setCheckoutDiv("monerisCheckout");

        myCheckout.setCallback("page_loaded", myPageLoad);
        myCheckout.setCallback("cancel_transaction", myCancelTransaction);
        myCheckout.setCallback("error_event", myErrorEvent);
        myCheckout.setCallback("payment_receipt", myPaymentReceipt);
        myCheckout.setCallback("payment_complete", myPaymentComplete);
        myCheckout.setCallback("page_closed", myPageClosed);
        myCheckout.setCallback("payment_submitted", myPaymentSubmitted);

        let paymentId = null;

        async function fetchTicket() {
            const amount = document.getElementById('amount').value;

            const query = `
                mutation InitializePayment($input: InitializePaymentInput!) {
                    initializePayment(input: $input) {
                        initialPaymentResult {
                            payment {
                                id
                                money {
                                    paymentCurrencyCode
                                    paymentCurrencyDesc
                                    paymentDecimalPrecision
                                    paymentAmount
                                }
                                paymentProvider
                                externalReference
                                paymentStatus
                                auditInfo {
                                    createdAt
                                    createdBy
                                    lastModifiedAt
                                    lastModifiedBy
                                    deletedAt
                                    deletedBy
                                }
                            }
                            redirectUrl
                            data {
                                key
                                value
                            }
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors {
                                    propertyPath
                                    message
                                    code
                                }
                            }
                        }
                    }
                }
            `;

            const variables = {
                input: {
                    amount: parseFloat(amount),
                    currencyCode: "124",
                    currencyDesc: "CAD",
                    paymentProvider: "MONERIS",
                    decimalPrecision: 2,
                    payorId: "payorIdTest",
                    policyId: "policyIdTest",
                    invoiceNumber: "invoiceNumberTest"
                }
            };

            const response = await fetch('https://api-v2.dev.covergo.cloud/graphql/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Tenant': `gms_dev`
                },
                body: JSON.stringify({
                    query: query,
                    variables: variables
                })
            });

            const result = await response.json();
            if (result.data && result.data.initializePayment && result.data.initializePayment.initialPaymentResult) {
                paymentId = result.data.initializePayment.initialPaymentResult.payment.id;
                const ticket = result.data.initializePayment.initialPaymentResult.data.find(item => item.key === 'ticket').value;
                const environment = result.data?.initializePayment?.initialPaymentResult?.data?.find(item => item.key === 'environment').value;
                startCheckout(ticket, environment);
            } else {
                console.error('Failed to fetch ticket:', result.errors);
            }
        }

        function startCheckout(ticket, environment) {
            if(environment){
                myCheckout.setMode(environment);
            } else {
                myCheckout.setMode("qa");
            }
            
            myCheckout.startCheckout(ticket);
        }

        async function callBackend(mutation, variables) {
            const response = await fetch('https://api-v2.dev.covergo.cloud/graphql/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Tenant': `gms_dev`
                },
                body: JSON.stringify({
                    query: mutation,
                    variables: variables
                })
            });
            return await response.json();
        }

        function myPageLoad(response) {
            console.log("Page loaded:", response);
        }

        async function myCancelTransaction(response) {
            console.log("Transaction canceled:", response);
            myCheckout.closeCheckout();
            
            const mutation = `
                mutation CancelPayment($input: CancelPaymentInput!) {
                    cancelPayment(input: $input) {
                        payment {
                            id
                            paymentStatus
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors {
                                    propertyPath
                                    message
                                    code
                                }
                            }
                        }
                    }
                }
            `;
            const variables = { input: { paymentId: paymentId } };
            await callBackend(mutation, variables);
        }

        async function myErrorEvent(response) {
            console.error("Error event:", response);
            myCheckout.closeCheckout();
            
            const mutation = `
                mutation FailPayment($input: FailPaymentInput!) {
                    failPayment(input: $input) {
                        payment {
                            id
                            paymentStatus
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors {
                                    propertyPath
                                    message
                                    code
                                }
                            }
                        }
                    }
                }
            `;
            const variables = { input: { paymentId: paymentId } };
            await callBackend(mutation, variables);
        }

        async function myPaymentReceipt(response) {
            console.log("Payment receipt:", response);
            debugger
            const mutation = `
                mutation FinalizePayment($input: FinalizePaymentInput!) {
                    finalizePayment(input: $input) {
                        payment {
                            id
                            paymentStatus
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors {
                                    propertyPath
                                    message
                                    code
                                }
                            }
                        }
                    }
                }
            `;
            const variables = {
                input: {
                    paymentId: paymentId,
                    dynamicFields: response,
                }
            };
            await callBackend(mutation, variables);
        }

        async function myPaymentComplete(response) {
            console.log("Payment complete:", response);
            const mutation = `
                mutation FinalizePayment($input: FinalizePaymentInput!) {
                    finalizePayment(input: $input) {
                        payment {
                            id
                            paymentStatus
                        }
                        errors {
                            ... on InputDataValidationError {
                                message
                                code
                                errors {
                                    propertyPath
                                    message
                                    code
                                }
                            }
                        }
                    }
                }
            `;
            const variables = {
                input: {
                    paymentId: paymentId,
                    dynamicFields: response,
                }
            };
            await callBackend(mutation, variables);
            myCheckout.closeCheckout();
        }

        function myPageClosed(response) {
            console.log("Page closed:", response);
        }

        function myPaymentSubmitted(response) {
            console.log("Payment submitted:", response);
        }
    </script>
</head>
<body>
    <h1>CoverGo Payment</h1>
    <form id="form1">
        <label for="amount">Amount:</label>
        <input type="number" id="amount" name="amount" step="0.01" required> CAD
        <br>
        <br>
        <input type="button" class="button" onclick="fetchTicket()" value="Pay via Moneris">
        <div id="monerisCheckout"></div>
    </form>
</body>
</html>