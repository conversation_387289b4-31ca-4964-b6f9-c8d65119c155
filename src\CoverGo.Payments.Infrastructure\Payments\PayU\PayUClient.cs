﻿using CoverGo.Payments.Application.Payments;
using CoverGo.Payments.Infrastructure.Payments.PayU.Builders;
using CoverGo.Payments.Infrastructure.Payments.PayU.Configurations;
using CoverGo.Payments.Infrastructure.Payments.PayU.Models;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Infrastructure.Payments.PayU
{
    public class PayUClient : BaseClient, IPayUClient
    {
        public PayUClient(PayUClientSettings settings, IHttpClientFactory clientFactory,
            IDateTimeProvider dateTimeProvider, ILogger logger)
            : base(settings, clientFactory, dateTimeProvider, logger)
        {
        }

        public async Task<PaymentResponse?> PostPaymentAsync(PaymentRequest payment, CancellationToken ct) =>
            await ProcessAsync<PaymentResponse>(
                PayUClientUrlBuilder.BuildPostPaymentUrl(Settings.Url, Settings.ApiVersion),
                HttpMethod.Post,
                ct,
                payment);

        public async Task<PaymentResponse?> GetPaymentAsync(string merchantPaymentReference, CancellationToken ct) =>
            await ProcessAsync<PaymentResponse>(
                PayUClientUrlBuilder.BuildGetPaymentUrl(Settings.Url, Settings.ApiVersion, merchantPaymentReference),
                HttpMethod.Get,
                ct);

        public async Task<CaptureResponse?> CapturePaymentAsync(CaptureRequest captureRequest, CancellationToken ct) =>
            await ProcessAsync<CaptureResponse>(
                PayUClientUrlBuilder.BuildCapturePaymentUrl(Settings.Url, Settings.ApiVersion),
                HttpMethod.Post,
                ct,
                captureRequest);

        public async Task<RefundResponse?> PostRefundAsync(RefundRequest refundRequest, CancellationToken ct) =>
            await ProcessAsync<RefundResponse>(
                PayUClientUrlBuilder.BuildRefundPaymentUrl(Settings.Url, Settings.ApiVersion),
                HttpMethod.Post,
                ct,
                refundRequest);
        
        public async Task<TokenResponse?> PostTokenAsync(TokenRequest tokenRequest, CancellationToken ct) =>
            await ProcessAsync<TokenResponse>(
                PayUClientUrlBuilder.BuildTokenPaymentUrl(Settings.Url, Settings.ApiVersion),
                HttpMethod.Post,
                ct,
                tokenRequest);

        public async Task<SessionResponse?> PostSessionAsync(SessionRequest sessionRequest, CancellationToken ct) =>
            await ProcessAsync<SessionResponse>(
                PayUClientUrlBuilder.BuildPostSessionUrl(Settings.Url, Settings.ApiVersion),
                HttpMethod.Post,
                ct,
                sessionRequest);
        
    }
}