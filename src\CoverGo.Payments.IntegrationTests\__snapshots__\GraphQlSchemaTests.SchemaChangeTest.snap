﻿schema {
  query: Query
  mutation: Mutation
}

interface FieldValidationError {
  propertyPath: String!
  message: String!
  code: String!
}

interface UserError {
  message: String!
  code: String!
}

type Address {
  addressLine1: String
  addressLine2: String
  street: String
  houseNumber: String
  postalCode: String
  city: String
  state: String
  country: String
}

type AuditInfo {
  createdAt: DateTime!
  createdBy: String!
  lastModifiedAt: DateTime
  lastModifiedBy: String
  deletedAt: DateTime
  deletedBy: String
}

type CancelPaymentInitializationsPayload {
  paymentInitializationsCancellationResult: PaymentInitializationsCancellationResultDto
  errors: [CancelPaymentInitializationsError!]
}

type CancelPaymentPayload {
  payment: PaymentDto
  errors: [CancelPaymentError!]
}

type CapturePaymentPayload {
  payment: PaymentDto
  errors: [CapturePaymentError!]
}

type CreatePspSettingsPayload {
  result: ResultDto
  errors: [CreatePspSettingsError!]
}

type DomainError implements UserError {
  message: String!
  code: String!
}

type EncryptPspSettingsPayload {
  result: String
  errors: [EncryptPspSettingsError!]
}

type FailPaymentPayload {
  payment: PaymentDto
  errors: [FailPaymentError!]
}

type FieldError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

type FieldRequiredError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

type FieldUniqueError implements FieldValidationError {
  code: String!
  message: String!
  propertyPath: String!
}

type FinalizePaymentPayload {
  payment: PaymentDto
  errors: [FinalizePaymentError!]
}

type InitializePaymentPayload {
  initialPaymentResult: ProcessInitialPaymentResultDto
  errors: [InitializePaymentError!]
}

type InitializeUpdatePaymentPayload {
  initializeUpdatePaymentResult: ProcessInitialPaymentResultDto
  errors: [InitializeUpdatePaymentError!]
}

type InputDataValidationError implements UserError {
  errors: [FieldValidationError!]!
  message: String!
  code: String!
}

type KeyValuePairOfStringAndString {
  key: String!
  value: String!
}

type MCPDetails {
  mcpMerchantSettlementAmount: String
  mcpCurrencyCode: String
  mcpRate: String
  mcpDecimalPrecision: Int
  mcpAmount: String
  mcpCurrencyDesc: String
}

type MoneyDto {
  paymentCurrencyCode: String!
  paymentCurrencyDesc: String!
  paymentDecimalPrecision: Int!
  paymentAmount: Decimal!
}

type Mutation {
  cancelPaymentInitializations(input: CancelPaymentInitializationsInput!): CancelPaymentInitializationsPayload!
  cancelPayment(input: CancelPaymentInput!): CancelPaymentPayload!
  capturePayment(input: CapturePaymentInput!): CapturePaymentPayload!
  failPayment(input: FailPaymentInput!): FailPaymentPayload!
  finalizePayment(input: FinalizePaymentInput!): FinalizePaymentPayload!
  initializePayment(input: InitializePaymentInput!): InitializePaymentPayload!
  initializeUpdatePayment(input: InitializeUpdatePaymentInput!): InitializeUpdatePaymentPayload!
  recurringPayment(input: RecurringPaymentInput!): RecurringPaymentPayload!
  registerPayment(input: RegisterPaymentInput!): RegisterPaymentPayload!
  tokenizePaymentInitialization(input: TokenizePaymentInitializationInput!): TokenizePaymentInitializationPayload!
  createPspSettings(input: CreatePspSettingsInput!): CreatePspSettingsPayload!
  encryptPspSettings(input: EncryptPspSettingsInput!): EncryptPspSettingsPayload!
  refundPayment(input: RefundPaymentInput!): RefundPaymentPayload!
}

type PSPBearer {
  token: String
}

"Information about pagination in a connection."
type PageInfo {
  "Indicates whether more edges exist following the set defined by the clients arguments."
  hasNextPage: Boolean!
  "Indicates whether more edges exist prior the set defined by the clients arguments."
  hasPreviousPage: Boolean!
  "When paginating backwards, the cursor to continue."
  startCursor: String
  "When paginating forwards, the cursor to continue."
  endCursor: String
}

type PayerData {
  companyName: String
  externalCustomerId: String
  firstName: String
  lastName: String
  address: Address
  emailAddress: String
  language: String
  phoneNumber: String
  customerName: String!
}

type PaymentAggregate {
  description: String!
  calculateRefundedAmount: Decimal!
  policyId: String!
  invoiceNumber: String
  paymentMethod: String
  payorId: String!
  payerData: PayerData
  providerPaymentId: String
  initialBearer: PSPBearer
  paymentProvider: PaymentProvider!
  paymentStatusHistoryItems: [PaymentStatusHistoryItem!]!
  paymentStatusHistoryItem: PaymentStatusHistoryItem
  status: PaymentStatus
  money: PaymentMoney!
  internalReference: String!
  externalReference: String
  effectiveDate: DateTime
  attempt: Int!
  isSuccessful: Boolean!
  isFailure: Boolean!
  id: String!
}

type PaymentDto {
  id: String!
  money: MoneyDto!
  paymentProvider: PaymentProvider!
  internalReference: String!
  externalReference: String
  paymentStatus: PaymentStatus
  auditInfo: AuditInfo!
  refundedAmount: Decimal!
}

type PaymentInitializationsCancellationResultDto {
  isSuccessful: Boolean!
}

type PaymentMoney {
  paymentCurrencyCode: String!
  paymentCurrencyDesc: String!
  paymentAmount: Decimal!
  paymentDecimalPrecision: Int!
  paymentMcDetails: MCPDetails
}

type PaymentRefundDto {
  id: String!
  paymentId: String!
  status: RefundStatus!
  money: MoneyDto!
  createdAtDateUtc: DateTime!
  providerPaymentId: String
}

type PaymentStatusHistoryItem {
  createdAtDateUtc: DateTime!
  money: PaymentMoney!
  webhookBody: String
  error: String
  refundId: Int
  status: PaymentStatus!
}

"A connection to a list of items."
type PaymentsConnection {
  "Information to aid in pagination."
  pageInfo: PageInfo!
  "A list of edges."
  edges: [PaymentsEdge!]
  "A flattened list of the nodes."
  nodes: [PaymentAggregate!]
}

"An edge in a connection."
type PaymentsEdge {
  "A cursor for use in pagination."
  cursor: String!
  "The item at the end of the edge."
  node: PaymentAggregate!
}

type ProcessInitialPaymentResultDto {
  payment: PaymentDto!
  redirectUrl: URL
  data: [KeyValuePairOfStringAndString!]
}

type PspSettingsAlreadyExistsError implements UserError {
  message: String!
  code: String!
}

type Query {
  payments("Returns the first _n_ elements from the list." first: Int "Returns the elements in the list that come after the specified cursor." after: String "Returns the last _n_ elements from the list." last: Int "Returns the elements in the list that come before the specified cursor." before: String where: PaymentAggregateFilterInput order: [PaymentAggregateSortInput!]): PaymentsConnection @authorize
  tokenizedPaymentInitializationPublicFields(initializationToken: String!): [KeyValuePairOfStringAndString!]!
}

type RecurringPaymentPayload {
  payment: PaymentDto
  errors: [RecurringPaymentError!]
}

type RefundPaymentPayload {
  payment: PaymentRefundDto
  errors: [RefundPaymentError!]
}

type RegisterPaymentPayload {
  payment: PaymentDto
  errors: [RegisterPaymentError!]
}

type ResultDto {
  success: Boolean!
  id: String
}

type TokenizePaymentInitializationPayload {
  tokenizedPaymentInitialization: TokenizedPaymentInitializationDto
  errors: [TokenizePaymentInitializationError!]
}

type TokenizedPaymentInitializationDto {
  initializationToken: String!
}

union CancelPaymentError = InputDataValidationError | DomainError

union CancelPaymentInitializationsError = InputDataValidationError | DomainError

union CapturePaymentError = InputDataValidationError | DomainError

union CreatePspSettingsError = InputDataValidationError | PspSettingsAlreadyExistsError | DomainError

union EncryptPspSettingsError = DomainError

union FailPaymentError = InputDataValidationError | DomainError

union FinalizePaymentError = InputDataValidationError | DomainError

union InitializePaymentError = InputDataValidationError | DomainError

union InitializeUpdatePaymentError = InputDataValidationError | DomainError

union RecurringPaymentError = InputDataValidationError | DomainError

union RefundPaymentError = InputDataValidationError | DomainError

union RegisterPaymentError = InputDataValidationError | DomainError

union TokenizePaymentInitializationError = InputDataValidationError | DomainError

input AddressFilterInput {
  and: [AddressFilterInput!]
  or: [AddressFilterInput!]
  addressLine1: StringOperationFilterInput
  addressLine2: StringOperationFilterInput
  street: StringOperationFilterInput
  houseNumber: StringOperationFilterInput
  postalCode: StringOperationFilterInput
  city: StringOperationFilterInput
  state: StringOperationFilterInput
  country: StringOperationFilterInput
}

input AddressSortInput {
  addressLine1: SortEnumType
  addressLine2: SortEnumType
  street: SortEnumType
  houseNumber: SortEnumType
  postalCode: SortEnumType
  city: SortEnumType
  state: SortEnumType
  country: SortEnumType
}

input BooleanOperationFilterInput {
  eq: Boolean
  neq: Boolean
}

input CancelPaymentInitializationsInput {
  initializationTokens: [String!]!
}

input CancelPaymentInput {
  paymentId: String!
}

input CapturePaymentInput {
  paymentId: String!
}

input CreatePspSettingsInput {
  paymentProvider: PaymentProvider!
  pspSettings: JSON!
}

input DateTimeOperationFilterInput {
  eq: DateTime
  neq: DateTime
  in: [DateTime]
  nin: [DateTime]
  gt: DateTime
  ngt: DateTime
  gte: DateTime
  ngte: DateTime
  lt: DateTime
  nlt: DateTime
  lte: DateTime
  nlte: DateTime
}

input DecimalOperationFilterInput {
  eq: Decimal
  neq: Decimal
  in: [Decimal]
  nin: [Decimal]
  gt: Decimal
  ngt: Decimal
  gte: Decimal
  ngte: Decimal
  lt: Decimal
  nlt: Decimal
  lte: Decimal
  nlte: Decimal
}

input EncryptPspSettingsInput {
  paymentProvider: PaymentProvider
}

input EntityAuditInfoFilterInput {
  and: [EntityAuditInfoFilterInput!]
  or: [EntityAuditInfoFilterInput!]
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  lastModifiedAt: DateTimeOperationFilterInput
  lastModifiedBy: StringOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input EntityAuditInfoSortInput {
  createdAt: SortEnumType
  createdBy: SortEnumType
  lastModifiedAt: SortEnumType
  lastModifiedBy: SortEnumType
  deletedAt: SortEnumType
  deletedBy: SortEnumType
}

input FailPaymentInput {
  paymentId: String!
}

input FinalizePaymentInput {
  paymentId: String!
  dynamicFields: JSON
}

input IDomainEventFilterInput {
  and: [IDomainEventFilterInput!]
  or: [IDomainEventFilterInput!]
}

input InitializePaymentInput {
  amount: Decimal
  currencyCode: String
  currencyDesc: String
  decimalPrecision: Int
  policyId: String
  invoiceNumber: String
  payorId: String
  paymentProvider: PaymentProvider
  dynamicFields: JSON
  initializationToken: String
}

input InitializeUpdatePaymentInput {
  currencyDesc: String!
  policyId: String!
  payorId: String!
  paymentProvider: PaymentProvider!
  dynamicFields: JSON
}

input IntOperationFilterInput {
  eq: Int
  neq: Int
  in: [Int]
  nin: [Int]
  gt: Int
  ngt: Int
  gte: Int
  ngte: Int
  lt: Int
  nlt: Int
  lte: Int
  nlte: Int
}

input ListFilterInputTypeOfIDomainEventFilterInput {
  all: IDomainEventFilterInput
  none: IDomainEventFilterInput
  some: IDomainEventFilterInput
  any: Boolean
}

input ListFilterInputTypeOfPaymentStatusHistoryItemFilterInput {
  all: PaymentStatusHistoryItemFilterInput
  none: PaymentStatusHistoryItemFilterInput
  some: PaymentStatusHistoryItemFilterInput
  any: Boolean
}

input MCPDetailsFilterInput {
  and: [MCPDetailsFilterInput!]
  or: [MCPDetailsFilterInput!]
  mcpMerchantSettlementAmount: StringOperationFilterInput
  mcpCurrencyCode: StringOperationFilterInput
  mcpRate: StringOperationFilterInput
  mcpDecimalPrecision: IntOperationFilterInput
  mcpAmount: StringOperationFilterInput
  mcpCurrencyDesc: StringOperationFilterInput
}

input MCPDetailsSortInput {
  mcpMerchantSettlementAmount: SortEnumType
  mcpCurrencyCode: SortEnumType
  mcpRate: SortEnumType
  mcpDecimalPrecision: SortEnumType
  mcpAmount: SortEnumType
  mcpCurrencyDesc: SortEnumType
}

input NullableOfPaymentStatusOperationFilterInput {
  eq: PaymentStatus
  neq: PaymentStatus
  in: [PaymentStatus]
  nin: [PaymentStatus]
}

input PSPBearerFilterInput {
  and: [PSPBearerFilterInput!]
  or: [PSPBearerFilterInput!]
  token: StringOperationFilterInput
}

input PSPBearerSortInput {
  token: SortEnumType
}

input PayerDataFilterInput {
  and: [PayerDataFilterInput!]
  or: [PayerDataFilterInput!]
  companyName: StringOperationFilterInput
  externalCustomerId: StringOperationFilterInput
  firstName: StringOperationFilterInput
  lastName: StringOperationFilterInput
  address: AddressFilterInput
  emailAddress: StringOperationFilterInput
  language: StringOperationFilterInput
  phoneNumber: StringOperationFilterInput
  customerName: StringOperationFilterInput
}

input PayerDataSortInput {
  companyName: SortEnumType
  externalCustomerId: SortEnumType
  firstName: SortEnumType
  lastName: SortEnumType
  address: AddressSortInput
  emailAddress: SortEnumType
  language: SortEnumType
  phoneNumber: SortEnumType
  customerName: SortEnumType
}

input PaymentAggregateFilterInput {
  and: [PaymentAggregateFilterInput!]
  or: [PaymentAggregateFilterInput!]
  policyId: StringOperationFilterInput
  invoiceNumber: StringOperationFilterInput
  paymentMethod: StringOperationFilterInput
  payorId: StringOperationFilterInput
  payerData: PayerDataFilterInput
  providerPaymentId: StringOperationFilterInput
  initialBearer: PSPBearerFilterInput
  paymentProvider: PaymentProviderOperationFilterInput
  paymentStatusHistoryItems: ListFilterInputTypeOfPaymentStatusHistoryItemFilterInput
  paymentStatusHistoryItem: PaymentStatusHistoryItemFilterInput
  status: NullableOfPaymentStatusOperationFilterInput
  money: PaymentMoneyFilterInput
  internalReference: StringOperationFilterInput
  externalReference: StringOperationFilterInput
  pspSettings: StringOperationFilterInput
  dynamicFields: StringOperationFilterInput
  effectiveDate: DateTimeOperationFilterInput
  attempt: IntOperationFilterInput
  isSuccessful: BooleanOperationFilterInput
  isFailure: BooleanOperationFilterInput
  domainEvents: ListFilterInputTypeOfIDomainEventFilterInput
  entityAuditInfo: EntityAuditInfoFilterInput
  id: StringOperationFilterInput
}

input PaymentAggregateSortInput {
  policyId: SortEnumType
  invoiceNumber: SortEnumType
  paymentMethod: SortEnumType
  payorId: SortEnumType
  payerData: PayerDataSortInput
  providerPaymentId: SortEnumType
  initialBearer: PSPBearerSortInput
  paymentProvider: SortEnumType
  paymentStatusHistoryItem: PaymentStatusHistoryItemSortInput
  status: SortEnumType
  money: PaymentMoneySortInput
  internalReference: SortEnumType
  externalReference: SortEnumType
  pspSettings: SortEnumType
  dynamicFields: SortEnumType
  effectiveDate: SortEnumType
  attempt: SortEnumType
  isSuccessful: SortEnumType
  isFailure: SortEnumType
  entityAuditInfo: EntityAuditInfoSortInput
  id: SortEnumType
}

input PaymentMoneyFilterInput {
  and: [PaymentMoneyFilterInput!]
  or: [PaymentMoneyFilterInput!]
  paymentCurrencyCode: StringOperationFilterInput
  paymentCurrencyDesc: StringOperationFilterInput
  paymentAmount: DecimalOperationFilterInput
  paymentDecimalPrecision: IntOperationFilterInput
  paymentMcDetails: MCPDetailsFilterInput
}

input PaymentMoneySortInput {
  paymentCurrencyCode: SortEnumType
  paymentCurrencyDesc: SortEnumType
  paymentAmount: SortEnumType
  paymentDecimalPrecision: SortEnumType
  paymentMcDetails: MCPDetailsSortInput
}

input PaymentProviderOperationFilterInput {
  eq: PaymentProvider
  neq: PaymentProvider
  in: [PaymentProvider!]
  nin: [PaymentProvider!]
}

input PaymentStatusHistoryItemFilterInput {
  and: [PaymentStatusHistoryItemFilterInput!]
  or: [PaymentStatusHistoryItemFilterInput!]
  createdAtDateUtc: DateTimeOperationFilterInput
  money: PaymentMoneyFilterInput
  webhookBody: StringOperationFilterInput
  error: StringOperationFilterInput
  refundId: IntOperationFilterInput
  status: PaymentStatusOperationFilterInput
}

input PaymentStatusHistoryItemSortInput {
  createdAtDateUtc: SortEnumType
  money: PaymentMoneySortInput
  webhookBody: SortEnumType
  error: SortEnumType
  refundId: SortEnumType
  status: SortEnumType
}

input PaymentStatusOperationFilterInput {
  eq: PaymentStatus
  neq: PaymentStatus
  in: [PaymentStatus!]
  nin: [PaymentStatus!]
}

input RecurringPaymentInput {
  amount: Decimal!
  currencyDesc: String!
  decimalPrecision: Int!
  policyId: String!
  invoiceNumber: String!
  payorId: String!
}

input RefundPaymentInput {
  paymentId: String!
  amount: Decimal!
  decimalPrecision: Int!
}

input RegisterPaymentInput {
  paymentProvider: PaymentProvider!
  amount: Decimal!
  currencyCode: String!
  effectiveDate: DateTime!
  policyId: String!
  invoiceNumber: String
  payorId: String!
  status: String
  paymentMethod: String
  error: String
  dynamicFields: JSON
  transactionType: String!
}

input StringOperationFilterInput {
  and: [StringOperationFilterInput!]
  or: [StringOperationFilterInput!]
  eq: String
  neq: String
  contains: String
  ncontains: String
  in: [String]
  nin: [String]
  startsWith: String
  nstartsWith: String
  endsWith: String
  nendsWith: String
}

input TokenizePaymentInitializationInput {
  amount: Decimal!
  currencyCode: String!
  currencyDesc: String!
  decimalPrecision: Int!
  policyId: String!
  invoiceNumber: String!
  payorId: String!
  paymentProvider: PaymentProvider!
  dynamicFields: JSON
  publicFields: JSON
}

enum ApplyPolicy {
  BEFORE_RESOLVER
  AFTER_RESOLVER
  VALIDATION
}

enum PaymentProvider {
  NONE
  FAKE
  MONERIS
  STRIPE
  AMAZON
  PAY_U
  WALAA
  ING
  EXTERNAL_FILE
}

enum PaymentStatus {
  CREATED
  IN_PROGRESS
  PREPARED
  PRELIMINARY_SUCCEEDED
  SUCCEEDED
  UNDEFINED
  CANCELED
  CHARGEBACK
  PENDING
  FAILED
  UNMAPPED
  REFUNDED
  THREE_D_SECURE_PENDING
  PARTIALLY_REFUNDED
  PARTIAL_CHARGEBACK
  IN_DISPUTE
  FINALIZING
  SCHEDULED
  PARTIALLY_PAID
  SUCCESSFULLY_IMPORTED
  OVER_PAID
  EXPIRED
}

enum RefundStatus {
  CREATED
  SUCCEEDED
  FAILED
  CANCELED
}

enum SortEnumType {
  ASC
  DESC
}

directive @authorize("The name of the authorization policy that determines access to the annotated resource." policy: String "Roles that are allowed to access the annotated resource." roles: [String!] "Defines when when the authorize directive shall be applied.By default the authorize directives are applied during the validation phase." apply: ApplyPolicy! = BEFORE_RESOLVER) repeatable on OBJECT | FIELD_DEFINITION

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime @specifiedBy(url: "https:\/\/www.graphql-scalars.com\/date-time")

"The built-in `Decimal` scalar type."
scalar Decimal

scalar JSON

scalar URL @specifiedBy(url: "https:\/\/tools.ietf.org\/html\/rfc3986")
