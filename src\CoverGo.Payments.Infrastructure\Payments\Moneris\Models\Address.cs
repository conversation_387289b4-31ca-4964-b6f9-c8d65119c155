﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.Moneris.Models;

public class Address
{
    [JsonProperty("address_1")]
    public string? AddressLine1 { get; set; }

    [JsonProperty("address_2")]
    public string? AddressLine2 { get; set; }

    [JsonProperty("city")]
    public string? City { get; set; }

    [JsonProperty("country")]
    public string? Country { get; set; }

    [JsonProperty("province")]
    public string? Province { get; set; }

    [JsonProperty("postal_code")]
    public string? PostalCode { get; set; }
}