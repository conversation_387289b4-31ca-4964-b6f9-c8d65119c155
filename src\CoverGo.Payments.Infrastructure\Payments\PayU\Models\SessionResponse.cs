﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models
{
    public class SessionResponse
    {
        [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
        public int Code { get; set; }

        [JsonProperty(PayUContainer.PropsName.Status, NullValueHandling = NullValueHandling.Ignore)]
        public string Status { get; set; }

        [JsonProperty(PayUContainer.PropsName.Message, NullValueHandling = NullValueHandling.Ignore)]
        public string Message { get; set; }

        [JsonProperty(PayUContainer.PropsName.LifetimeMinutes, NullValueHandling = NullValueHandling.Ignore)]
        public int LifetimeMinutes { get; set; }

        [JsonProperty(PayUContainer.PropsName.SessionId, NullValueHandling = NullValueHandling.Ignore)]
        public string SessionId { get; set; }

        [JsonProperty(PayUContainer.PropsName.CreatedAt, NullValueHandling = NullValueHandling.Ignore)]
        public string CreatedAt { get; set; }
    }
}
