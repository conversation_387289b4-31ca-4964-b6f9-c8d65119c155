﻿using CoverGo.Payments.Infrastructure.Payments.Ing.Models;
using CoverGo.Payments.Infrastructure.Payments.ING.Models;

namespace CoverGo.Payments.Infrastructure.Payments.Ing;

public interface IIngClient
{
    Task<PaymentProfileResponse?> GetPaymentProfileAsync(string customerId, CancellationToken cancellationToken);
    Task<DebitPaymentProfileResponse?> DebitPaymentProfileAsync(DebitPaymentProfileRequest request, CancellationToken cancellationToken);
    Task<RefundResponse?> PostRefundAsync(RefundRequest request, string transactionId, CancellationToken cancellationToken);
}