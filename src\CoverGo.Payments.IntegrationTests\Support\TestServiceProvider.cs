using CoverGo.Multitenancy;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace CoverGo.Payments.IntegrationTests.Support;

public static class TestServiceProvider
{
    public static IServiceProvider NewServiceProvider(string tenantId = "covergo")
    {
        var serviceCollection = new ServiceCollection();
        //serviceCollection.AddApplication();

        var manager = new ConfigurationManager();

        string? env = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT") ?? "Development.Tests";

        manager
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{env}.json");

        //serviceCollection.AddInfrastructure(manager);
        serviceCollection.AddTransient(sp => new TenantId(tenantId));

        return serviceCollection.BuildServiceProvider();
    }

    public static IServiceScope NewScope(string tenantId = "covergo") => NewServiceProvider(tenantId).CreateScope();
}