﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.PayU.Models;

public class PaymentRequest
{
    [JsonProperty(PayUContainer.PropsName.MerchantPaymentReference, NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantPaymentReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantPaymentAttemptReference,
        NullValueHandling = NullValueHandling.Ignore)]
    public string MerchantPaymentAttemptReference { get; set; }

    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.ReturnUrl, NullValueHandling = NullValueHandling.Ignore)]
    public string ReturnUrl { get; set; }

    [JsonProperty(PayUContainer.PropsName.Authorization, NullValueHandling = NullValueHandling.Ignore)]
    public Authorization Authorization { get; set; }

    [JsonProperty(PayUContainer.PropsName.Client, NullValueHandling = NullValueHandling.Ignore)]
    public Client Client { get; set; }

    [JsonProperty(PayUContainer.PropsName.Products, NullValueHandling = NullValueHandling.Ignore)]
    public List<Product> Products { get; set; }

    [JsonProperty(PayUContainer.PropsName.AirlineInfo, NullValueHandling = NullValueHandling.Ignore)]
    public AirlineInfo AirlineInfo { get; set; }

    [JsonProperty(PayUContainer.PropsName.ThreeDSecure, NullValueHandling = NullValueHandling.Ignore)]
    public ThreeDSecure ThreeDSecure { get; set; }

    [JsonProperty(PayUContainer.PropsName.StoredCredentials, NullValueHandling = NullValueHandling.Ignore)]
    public StoredCredentials StoredCredentials { get; set; }

    [JsonProperty(PayUContainer.PropsName.AdditionalDetails, NullValueHandling = NullValueHandling.Ignore)]
    public Dictionary<string, string> AdditionalDetails { get; set; }
}

public class Authorization
{
    [JsonProperty(PayUContainer.PropsName.PaymentMethod, NullValueHandling = NullValueHandling.Ignore)]
    public string PaymentMethod { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardDetails, NullValueHandling = NullValueHandling.Ignore)]
    public CardDetails CardDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.Credit, NullValueHandling = NullValueHandling.Ignore)]
    public CreditDetails Credit { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantToken, NullValueHandling = NullValueHandling.Ignore)]
    public MerchantToken MerchantToken { get; set; }

    [JsonProperty(PayUContainer.PropsName.ApplePayToken, NullValueHandling = NullValueHandling.Ignore)]
    public ApplePayToken ApplePayToken { get; set; }

    [JsonProperty(PayUContainer.PropsName.GooglePayToken, NullValueHandling = NullValueHandling.Ignore)]
    public string GooglePayToken { get; set; }

    [JsonProperty(PayUContainer.PropsName.OneTimeUseToken, NullValueHandling = NullValueHandling.Ignore)]
    public OneTimeUseToken OneTimeUseToken { get; set; }

    [JsonProperty(PayUContainer.PropsName.UsePaymentPage, NullValueHandling = NullValueHandling.Ignore)]
    public string UsePaymentPage { get; set; }

    [JsonProperty(PayUContainer.PropsName.InstallmentsNumber, NullValueHandling = NullValueHandling.Ignore,
        DefaultValueHandling = DefaultValueHandling.Ignore)]
    public int InstallmentsNumber { get; set; }

    [JsonProperty(PayUContainer.PropsName.UseLoyaltyPoints, NullValueHandling = NullValueHandling.Ignore)]
    public string UseLoyaltyPoints { get; set; }

    [JsonProperty(PayUContainer.PropsName.LoyaltyPointsAmount, NullValueHandling = NullValueHandling.Ignore,
        DefaultValueHandling = DefaultValueHandling.Ignore)]
    public int LoyaltyPointsAmount { get; set; }

    [JsonProperty(PayUContainer.PropsName.CampaignType, NullValueHandling = NullValueHandling.Ignore)]
    public string CampaignType { get; set; }

    [JsonProperty(PayUContainer.PropsName.Fx, NullValueHandling = NullValueHandling.Ignore)]
    public Fx Fx { get; set; }

    [JsonProperty(PayUContainer.PropsName.PaymentPageOptions, NullValueHandling = NullValueHandling.Ignore)]
    public PaymentPageOptions? PaymentPageOptions { get; set; }
}

public partial class CardDetails
{
    [JsonProperty(PayUContainer.PropsName.Number, NullValueHandling = NullValueHandling.Ignore)]
    public long Number { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExpiryMonth, NullValueHandling = NullValueHandling.Ignore)]
    public int ExpiryMonth { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExpiryYear, NullValueHandling = NullValueHandling.Ignore)]
    public int ExpiryYear { get; set; }

    [JsonProperty(PayUContainer.PropsName.Cvv, NullValueHandling = NullValueHandling.Ignore)]
    public int Cvv { get; set; }

    [JsonProperty(PayUContainer.PropsName.Owner, NullValueHandling = NullValueHandling.Ignore)]
    public string? Owner { get; set; }

    [JsonProperty(PayUContainer.PropsName.TimeSpentTypingNumber, NullValueHandling = NullValueHandling.Ignore)]
    public int TimeSpentTypingNumber { get; set; }

    [JsonProperty(PayUContainer.PropsName.TimeSpentTypingOwner, NullValueHandling = NullValueHandling.Ignore)]
    public int TimeSpentTypingOwner { get; set; }
}

public class CreditDetails
{
    [JsonProperty(PayUContainer.PropsName.Limits, NullValueHandling = NullValueHandling.Ignore)]
    public List<CreditLimit> Limits { get; set; }

    [JsonProperty(PayUContainer.PropsName.CustomerUUID, NullValueHandling = NullValueHandling.Ignore)]
    public string CustomerUUID { get; set; }

    [JsonProperty(PayUContainer.PropsName.CustomerMasterUUID, NullValueHandling = NullValueHandling.Ignore)]
    public string CustomerMasterUUID { get; set; }

    [JsonProperty(PayUContainer.PropsName.Scoring, NullValueHandling = NullValueHandling.Ignore)]
    public double Scoring { get; set; }

    [JsonProperty(PayUContainer.PropsName.Cnp, NullValueHandling = NullValueHandling.Ignore)]
    public string Cnp { get; set; }

    [JsonProperty(PayUContainer.PropsName.SourceOfIncome, NullValueHandling = NullValueHandling.Ignore)]
    public string SourceOfIncome { get; set; }

    [JsonProperty(PayUContainer.PropsName.UncensoredScoring, NullValueHandling = NullValueHandling.Ignore)]
    public double UncensoredScoring { get; set; }

    [JsonProperty(PayUContainer.PropsName.NrInstalments, NullValueHandling = NullValueHandling.Ignore)]
    public int NrInstalments { get; set; }

    [JsonProperty(PayUContainer.PropsName.NrDaysDueDate, NullValueHandling = NullValueHandling.Ignore)]
    public int NrDaysDueDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.FinancialPartner, NullValueHandling = NullValueHandling.Ignore)]
    public string FinancialPartner { get; set; }

    [JsonProperty(PayUContainer.PropsName.CampaignCode, NullValueHandling = NullValueHandling.Ignore)]
    public string CampaignCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.PaymentMethodLimitAmount, NullValueHandling = NullValueHandling.Ignore)]
    public int PaymentMethodLimitAmount { get; set; }
}

public class CreditLimit
{
    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.Amount, NullValueHandling = NullValueHandling.Ignore)]
    public double Amount { get; set; }
}

public class MerchantToken
{
    [JsonProperty(PayUContainer.PropsName.TokenHash, NullValueHandling = NullValueHandling.Ignore)]
    public string? TokenHash { get; set; }

    [JsonProperty(PayUContainer.PropsName.Cvv, NullValueHandling = NullValueHandling.Ignore,
        DefaultValueHandling = DefaultValueHandling.Ignore)]
    public int Cvv { get; set; }

    [JsonProperty(PayUContainer.PropsName.Owner, NullValueHandling = NullValueHandling.Ignore)]
    public string Owner { get; set; }
}

public class ApplePayToken
{
    [JsonProperty(PayUContainer.PropsName.Data, NullValueHandling = NullValueHandling.Ignore)]
    public string Data { get; set; }

    [JsonProperty(PayUContainer.PropsName.Header, NullValueHandling = NullValueHandling.Ignore)]
    public ApplePayTokenHeader Header { get; set; }

    [JsonProperty(PayUContainer.PropsName.Signature, NullValueHandling = NullValueHandling.Ignore)]
    public string Signature { get; set; }

    [JsonProperty(PayUContainer.PropsName.Version, NullValueHandling = NullValueHandling.Ignore)]
    public string Version { get; set; }
}

public class ApplePayTokenHeader
{
    [JsonProperty(PayUContainer.PropsName.ApplicationData, NullValueHandling = NullValueHandling.Ignore)]
    public string ApplicationData { get; set; }

    [JsonProperty(PayUContainer.PropsName.EphemeralPublicKey, NullValueHandling = NullValueHandling.Ignore)]
    public string EphemeralPublicKey { get; set; }

    [JsonProperty(PayUContainer.PropsName.WrappedKey, NullValueHandling = NullValueHandling.Ignore)]
    public string WrappedKey { get; set; }

    [JsonProperty(PayUContainer.PropsName.PublicKeyHash, NullValueHandling = NullValueHandling.Ignore)]
    public string PublicKeyHash { get; set; }

    [JsonProperty(PayUContainer.PropsName.TransactionId, NullValueHandling = NullValueHandling.Ignore)]
    public string TransactionId { get; set; }
}

public class OneTimeUseToken
{
    [JsonProperty(PayUContainer.PropsName.Token, NullValueHandling = NullValueHandling.Ignore)]
    public string Token { get; set; }

    [JsonProperty(PayUContainer.PropsName.SessionId, NullValueHandling = NullValueHandling.Ignore)]
    public string SessionId { get; set; }
}

public class Fx
{
    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExchangeRate, NullValueHandling = NullValueHandling.Ignore)]
    public string ExchangeRate { get; set; }
}

public class PaymentPageOptions
{
    [JsonProperty(PayUContainer.PropsName.OrderTimeout, NullValueHandling = NullValueHandling.Ignore)]
    public int OrderTimeout { get; set; }
}

public class Client
{
    [JsonProperty(PayUContainer.PropsName.Billing, NullValueHandling = NullValueHandling.Ignore)]
    public Billing Billing { get; set; }

    [JsonProperty(PayUContainer.PropsName.Delivery, NullValueHandling = NullValueHandling.Ignore)]
    public Delivery Delivery { get; set; }

    [JsonProperty(PayUContainer.PropsName.Ip, NullValueHandling = NullValueHandling.Ignore)]
    public string Ip { get; set; }

    [JsonProperty(PayUContainer.PropsName.Time, NullValueHandling = NullValueHandling.Ignore)]
    public string Time { get; set; }

    [JsonProperty(PayUContainer.PropsName.CommunicationLanguage, NullValueHandling = NullValueHandling.Ignore)]
    public string CommunicationLanguage { get; set; }
    
    [JsonProperty(PayUContainer.PropsName.Country, NullValueHandling = NullValueHandling.Ignore)]
    public string IpCountry { get; set; }
}

public class Billing
{
    [JsonProperty(PayUContainer.PropsName.FirstName, NullValueHandling = NullValueHandling.Ignore)]
    public string FirstName { get; set; }

    [JsonProperty(PayUContainer.PropsName.LastName, NullValueHandling = NullValueHandling.Ignore)]
    public string LastName { get; set; }

    [JsonProperty(PayUContainer.PropsName.Email, NullValueHandling = NullValueHandling.Ignore)]
    public string Email { get; set; }

    [JsonProperty(PayUContainer.PropsName.CountryCode, NullValueHandling = NullValueHandling.Ignore)]
    public string CountryCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.Phone, NullValueHandling = NullValueHandling.Ignore)]
    public string Phone { get; set; }

    [JsonProperty(PayUContainer.PropsName.City, NullValueHandling = NullValueHandling.Ignore)]
    public string City { get; set; }

    [JsonProperty(PayUContainer.PropsName.State, NullValueHandling = NullValueHandling.Ignore)]
    public string State { get; set; }

    [JsonProperty(PayUContainer.PropsName.CompanyName, NullValueHandling = NullValueHandling.Ignore)]
    public string CompanyName { get; set; }

    [JsonProperty(PayUContainer.PropsName.TaxId, NullValueHandling = NullValueHandling.Ignore)]
    public string TaxId { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressLine1, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressLine1 { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressLine2, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressLine2 { get; set; }

    [JsonProperty(PayUContainer.PropsName.ZipCode, NullValueHandling = NullValueHandling.Ignore)]
    public string ZipCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.IdentityDocument, NullValueHandling = NullValueHandling.Ignore)]
    public IdentityDocument IdentityDocument { get; set; }
}

public class Delivery
{
    [JsonProperty(PayUContainer.PropsName.FirstName, NullValueHandling = NullValueHandling.Ignore)]
    public string FirstName { get; set; }

    [JsonProperty(PayUContainer.PropsName.LastName, NullValueHandling = NullValueHandling.Ignore)]
    public string LastName { get; set; }

    [JsonProperty(PayUContainer.PropsName.Phone, NullValueHandling = NullValueHandling.Ignore)]
    public string Phone { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressLine1, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressLine1 { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressLine2, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressLine2 { get; set; }

    [JsonProperty(PayUContainer.PropsName.ZipCode, NullValueHandling = NullValueHandling.Ignore)]
    public string ZipCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.City, NullValueHandling = NullValueHandling.Ignore)]
    public string City { get; set; }

    [JsonProperty(PayUContainer.PropsName.State, NullValueHandling = NullValueHandling.Ignore)]
    public string State { get; set; }

    [JsonProperty(PayUContainer.PropsName.CountryCode, NullValueHandling = NullValueHandling.Ignore)]
    public string CountryCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.Email, NullValueHandling = NullValueHandling.Ignore)]
    public string Email { get; set; }
}

public class IdentityDocument
{
    [JsonProperty(PayUContainer.PropsName.Number, NullValueHandling = NullValueHandling.Ignore)]
    public string Number { get; set; }

    [JsonProperty(PayUContainer.PropsName.Type, NullValueHandling = NullValueHandling.Ignore)]
    public string Type { get; set; }
}

public class Product
{
    [JsonProperty(PayUContainer.PropsName.Name, NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }

    [JsonProperty(PayUContainer.PropsName.Sku, NullValueHandling = NullValueHandling.Ignore)]
    public string Sku { get; set; }

    [JsonProperty(PayUContainer.PropsName.AdditionalDetails, NullValueHandling = NullValueHandling.Ignore)]
    public string AdditionalDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.UnitPrice, NullValueHandling = NullValueHandling.Ignore)]
    public decimal UnitPrice { get; set; }

    [JsonProperty(PayUContainer.PropsName.Quantity, NullValueHandling = NullValueHandling.Ignore)]
    public int Quantity { get; set; }

    [JsonProperty(PayUContainer.PropsName.Vat, NullValueHandling = NullValueHandling.Ignore)]
    public string Vat { get; set; }

    [JsonProperty(PayUContainer.PropsName.Marketplace, NullValueHandling = NullValueHandling.Ignore)]
    public Marketplace Marketplace { get; set; }
}

public class Marketplace
{
    [JsonProperty(PayUContainer.PropsName.Version, NullValueHandling = NullValueHandling.Ignore)]
    public int Version { get; set; }

    [JsonProperty(PayUContainer.PropsName.Id, NullValueHandling = NullValueHandling.Ignore)]
    public string Id { get; set; }

    [JsonProperty(PayUContainer.PropsName.SellerId, NullValueHandling = NullValueHandling.Ignore)]
    public string SellerId { get; set; }

    [JsonProperty(PayUContainer.PropsName.CommissionAmount, NullValueHandling = NullValueHandling.Ignore)]
    public double CommissionAmount { get; set; }

    [JsonProperty(PayUContainer.PropsName.CommissionCurrency, NullValueHandling = NullValueHandling.Ignore)]
    public string CommissionCurrency { get; set; }
}

public class AirlineInfo
{
    [JsonProperty(PayUContainer.PropsName.PassengerName, NullValueHandling = NullValueHandling.Ignore)]
    public string PassengerName { get; set; }

    [JsonProperty(PayUContainer.PropsName.TicketNumber, NullValueHandling = NullValueHandling.Ignore)]
    public int TicketNumber { get; set; }

    [JsonProperty(PayUContainer.PropsName.RefundPolicy, NullValueHandling = NullValueHandling.Ignore)]
    public int RefundPolicy { get; set; }

    [JsonProperty(PayUContainer.PropsName.ReservationSystem, NullValueHandling = NullValueHandling.Ignore)]
    public string ReservationSystem { get; set; }

    [JsonProperty(PayUContainer.PropsName.TravelAgency, NullValueHandling = NullValueHandling.Ignore)]
    public TravelAgency TravelAgency { get; set; }

    [JsonProperty(PayUContainer.PropsName.FlightSegments, NullValueHandling = NullValueHandling.Ignore)]
    public List<FlightSegment> FlightSegments { get; set; }
}

public class TravelAgency
{
    [JsonProperty(PayUContainer.PropsName.Code, NullValueHandling = NullValueHandling.Ignore)]
    public string Code { get; set; }

    [JsonProperty(PayUContainer.PropsName.Name, NullValueHandling = NullValueHandling.Ignore)]
    public string Name { get; set; }
}

public class FlightSegment
{
    [JsonProperty(PayUContainer.PropsName.DepartureDate, NullValueHandling = NullValueHandling.Ignore)]
    public string DepartureDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.DepartureAirport, NullValueHandling = NullValueHandling.Ignore)]
    public string DepartureAirport { get; set; }

    [JsonProperty(PayUContainer.PropsName.DestinationAirport, NullValueHandling = NullValueHandling.Ignore)]
    public string DestinationAirport { get; set; }

    [JsonProperty(PayUContainer.PropsName.AirlineCode, NullValueHandling = NullValueHandling.Ignore)]
    public string AirlineCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.AirlineName, NullValueHandling = NullValueHandling.Ignore)]
    public string AirlineName { get; set; }

    [JsonProperty(PayUContainer.PropsName.ServiceClass, NullValueHandling = NullValueHandling.Ignore)]
    public string ServiceClass { get; set; }

    [JsonProperty(PayUContainer.PropsName.Stopover, NullValueHandling = NullValueHandling.Ignore)]
    public int Stopover { get; set; }

    [JsonProperty(PayUContainer.PropsName.FareCode, NullValueHandling = NullValueHandling.Ignore)]
    public string FareCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.FlightNumber, NullValueHandling = NullValueHandling.Ignore)]
    public string FlightNumber { get; set; }
}

public class ThreeDSecure
{
    [JsonProperty(PayUContainer.PropsName.MpiData, NullValueHandling = NullValueHandling.Ignore)]
    public MpiData MpiData { get; set; }

    [JsonProperty(PayUContainer.PropsName.StrongCustomerAuthentication, NullValueHandling = NullValueHandling.Ignore)]
    public StrongCustomerAuthentication StrongCustomerAuthentication { get; set; }
}

public class MpiData
{
    [JsonProperty(PayUContainer.PropsName.Eci, NullValueHandling = NullValueHandling.Ignore)]
    public int Eci { get; set; }

    [JsonProperty(PayUContainer.PropsName.Xid, NullValueHandling = NullValueHandling.Ignore)]
    public string Xid { get; set; }

    [JsonProperty(PayUContainer.PropsName.Cavv, NullValueHandling = NullValueHandling.Ignore)]
    public string Cavv { get; set; }

    [JsonProperty(PayUContainer.PropsName.DsTransactionId, NullValueHandling = NullValueHandling.Ignore)]
    public string DsTransactionId { get; set; }

    [JsonProperty(PayUContainer.PropsName.Version, NullValueHandling = NullValueHandling.Ignore)]
    public int Version { get; set; }
}

public class StrongCustomerAuthentication
{
    [JsonProperty(PayUContainer.PropsName.Cardholder, NullValueHandling = NullValueHandling.Ignore)]
    public Cardholder Cardholder { get; set; }

    [JsonProperty(PayUContainer.PropsName.ClientEnvironment, NullValueHandling = NullValueHandling.Ignore)]
    public ClientEnvironment ClientEnvironment { get; set; }

    [JsonProperty(PayUContainer.PropsName.Purchase, NullValueHandling = NullValueHandling.Ignore)]
    public Purchase Purchase { get; set; }

    [JsonProperty(PayUContainer.PropsName.ThreeDSRequestorPreferences, NullValueHandling = NullValueHandling.Ignore)]
    public ThreeDSRequestorPreferences ThreeDSRequestorPreferences { get; set; }
}

public class Cardholder
{
    [JsonProperty(PayUContainer.PropsName.Contact, NullValueHandling = NullValueHandling.Ignore)]
    public Contact Contact { get; set; }

    [JsonProperty(PayUContainer.PropsName.AccountInformation, NullValueHandling = NullValueHandling.Ignore)]
    public AccountInformation AccountInformation { get; set; }
}

public class Contact
{
    [JsonProperty(PayUContainer.PropsName.Phone, NullValueHandling = NullValueHandling.Ignore)]
    public Phone Phone { get; set; }
}

public class Phone
{
    [JsonProperty(PayUContainer.PropsName.Home, NullValueHandling = NullValueHandling.Ignore)]
    public Home Home { get; set; }

    [JsonProperty(PayUContainer.PropsName.Mobile, NullValueHandling = NullValueHandling.Ignore)]
    public Mobile Mobile { get; set; }

    [JsonProperty(PayUContainer.PropsName.Work, NullValueHandling = NullValueHandling.Ignore)]
    public Work Work { get; set; }
}

public class Home
{
    [JsonProperty(PayUContainer.PropsName.CountryPrefix, NullValueHandling = NullValueHandling.Ignore)]
    public string CountryPrefix { get; set; }

    [JsonProperty(PayUContainer.PropsName.Subscriber, NullValueHandling = NullValueHandling.Ignore)]
    public string Subscriber { get; set; }
}

public class Mobile
{
    [JsonProperty(PayUContainer.PropsName.CountryPrefix, NullValueHandling = NullValueHandling.Ignore)]
    public string CountryPrefix { get; set; }

    [JsonProperty(PayUContainer.PropsName.Subscriber, NullValueHandling = NullValueHandling.Ignore)]
    public string Subscriber { get; set; }
}

public class Work
{
    [JsonProperty(PayUContainer.PropsName.CountryPrefix, NullValueHandling = NullValueHandling.Ignore)]
    public string CountryPrefix { get; set; }

    [JsonProperty(PayUContainer.PropsName.Subscriber, NullValueHandling = NullValueHandling.Ignore)]
    public string Subscriber { get; set; }
}

public class AccountInformation
{
    [JsonProperty(PayUContainer.PropsName.Address, NullValueHandling = NullValueHandling.Ignore)]
    public Address Address { get; set; }

    [JsonProperty(PayUContainer.PropsName.FraudActivity, NullValueHandling = NullValueHandling.Ignore)]
    public string FraudActivity { get; set; }

    [JsonProperty(PayUContainer.PropsName.CreateDate, NullValueHandling = NullValueHandling.Ignore)]
    public string CreateDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.PastOrdersYear, NullValueHandling = NullValueHandling.Ignore)]
    public int PastOrdersYear { get; set; }

    [JsonProperty(PayUContainer.PropsName.PastOrdersDay, NullValueHandling = NullValueHandling.Ignore)]
    public int PastOrdersDay { get; set; }

    [JsonProperty(PayUContainer.PropsName.PurchasesLastSixMonths, NullValueHandling = NullValueHandling.Ignore)]
    public int PurchasesLastSixMonths { get; set; }

    [JsonProperty(PayUContainer.PropsName.ChangeDate, NullValueHandling = NullValueHandling.Ignore)]
    public string ChangeDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.ChangeIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string ChangeIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.AgeIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string AgeIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.PasswordChangedDate, NullValueHandling = NullValueHandling.Ignore)]
    public string PasswordChangedDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.PasswordChangeIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string PasswordChangeIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.NameToRecipientMatch, NullValueHandling = NullValueHandling.Ignore)]
    public string NameToRecipientMatch { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddCardAttemptsDay, NullValueHandling = NullValueHandling.Ignore)]
    public int AddCardAttemptsDay { get; set; }

    [JsonProperty(PayUContainer.PropsName.AuthMethod, NullValueHandling = NullValueHandling.Ignore)]
    public string AuthMethod { get; set; }

    [JsonProperty(PayUContainer.PropsName.AuthDateTime, NullValueHandling = NullValueHandling.Ignore)]
    public string AuthDateTime { get; set; }

    [JsonProperty(PayUContainer.PropsName.RequestorAuthenticationData, NullValueHandling = NullValueHandling.Ignore)]
    public string RequestorAuthenticationData { get; set; }

    [JsonProperty(PayUContainer.PropsName.AdditionalDetails, NullValueHandling = NullValueHandling.Ignore)]
    public string AdditionalDetails { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardAddedIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string CardAddedIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.CardAddedDate, NullValueHandling = NullValueHandling.Ignore)]
    public string CardAddedDate { get; set; }
}

public class Address
{
    [JsonProperty(PayUContainer.PropsName.Match, NullValueHandling = NullValueHandling.Ignore)]
    public string Match { get; set; }

    [JsonProperty(PayUContainer.PropsName.Billing, NullValueHandling = NullValueHandling.Ignore)]
    public BillingAddress Billing { get; set; }

    [JsonProperty(PayUContainer.PropsName.Delivery, NullValueHandling = NullValueHandling.Ignore)]
    public DeliveryAddress Delivery { get; set; }
}

public class BillingAddress
{
    [JsonProperty(PayUContainer.PropsName.Address3, NullValueHandling = NullValueHandling.Ignore)]
    public string Address3 { get; set; }

    [JsonProperty(PayUContainer.PropsName.StateCode, NullValueHandling = NullValueHandling.Ignore)]
    public string StateCode { get; set; }
}

public class DeliveryAddress
{
    [JsonProperty(PayUContainer.PropsName.Address3, NullValueHandling = NullValueHandling.Ignore)]
    public string Address3 { get; set; }

    [JsonProperty(PayUContainer.PropsName.StateCode, NullValueHandling = NullValueHandling.Ignore)]
    public string StateCode { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressFirstUsedDate, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressFirstUsedDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.AddressUsageIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string AddressUsageIndicator { get; set; }
}

public class ClientEnvironment
{
    [JsonProperty(PayUContainer.PropsName.DeviceChannel, NullValueHandling = NullValueHandling.Ignore)]
    public string DeviceChannel { get; set; }

    [JsonProperty(PayUContainer.PropsName.Browser, NullValueHandling = NullValueHandling.Ignore)]
    public Browser Browser { get; set; }
}

public class Browser
{
    [JsonProperty(PayUContainer.PropsName.AcceptHeader, NullValueHandling = NullValueHandling.Ignore)]
    public string AcceptHeader { get; set; }

    [JsonProperty(PayUContainer.PropsName.RequestIp, NullValueHandling = NullValueHandling.Ignore)]
    public string RequestIp { get; set; }

    [JsonProperty(PayUContainer.PropsName.JavaEnabled, NullValueHandling = NullValueHandling.Ignore)]
    public string JavaEnabled { get; set; }

    [JsonProperty(PayUContainer.PropsName.Language, NullValueHandling = NullValueHandling.Ignore)]
    public string Language { get; set; }

    [JsonProperty(PayUContainer.PropsName.ColorDepth, NullValueHandling = NullValueHandling.Ignore)]
    public int ColorDepth { get; set; }

    [JsonProperty(PayUContainer.PropsName.ScreenHeight, NullValueHandling = NullValueHandling.Ignore)]
    public int ScreenHeight { get; set; }

    [JsonProperty(PayUContainer.PropsName.ScreenWidth, NullValueHandling = NullValueHandling.Ignore)]
    public int ScreenWidth { get; set; }

    [JsonProperty(PayUContainer.PropsName.Timezone, NullValueHandling = NullValueHandling.Ignore)]
    public int Timezone { get; set; }

    [JsonProperty(PayUContainer.PropsName.UserAgent, NullValueHandling = NullValueHandling.Ignore)]
    public string UserAgent { get; set; }
}

public class Purchase
{
    [JsonProperty(PayUContainer.PropsName.Recurring, NullValueHandling = NullValueHandling.Ignore)]
    public Recurring Recurring { get; set; }

    [JsonProperty(PayUContainer.PropsName.TransactionType, NullValueHandling = NullValueHandling.Ignore)]
    public string TransactionType { get; set; }

    [JsonProperty(PayUContainer.PropsName.ShipIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string ShipIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.PreOrderIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string PreOrderIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.PreOrderDate, NullValueHandling = NullValueHandling.Ignore)]
    public string PreOrderDate { get; set; }

    [JsonProperty(PayUContainer.PropsName.DeliveryTimeFrame, NullValueHandling = NullValueHandling.Ignore)]
    public string DeliveryTimeFrame { get; set; }

    [JsonProperty(PayUContainer.PropsName.ReorderedIndicator, NullValueHandling = NullValueHandling.Ignore)]
    public string ReorderedIndicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.MerchantFunds, NullValueHandling = NullValueHandling.Ignore)]
    public MerchantFunds MerchantFunds { get; set; }
}

public class Recurring
{
    [JsonProperty(PayUContainer.PropsName.FrequencyDays, NullValueHandling = NullValueHandling.Ignore)]
    public int FrequencyDays { get; set; }

    [JsonProperty(PayUContainer.PropsName.ExpiryDate, NullValueHandling = NullValueHandling.Ignore)]
    public string ExpiryDate { get; set; }
}

public class MerchantFunds
{
    [JsonProperty(PayUContainer.PropsName.Amount, NullValueHandling = NullValueHandling.Ignore)]
    public int Amount { get; set; }

    [JsonProperty(PayUContainer.PropsName.Currency, NullValueHandling = NullValueHandling.Ignore)]
    public string Currency { get; set; }
}

public class ThreeDSRequestorPreferences
{
    [JsonProperty(PayUContainer.PropsName.Challenge, NullValueHandling = NullValueHandling.Ignore)]
    public Challenge Challenge { get; set; }
}

public class Challenge
{
    [JsonProperty(PayUContainer.PropsName.Indicator, NullValueHandling = NullValueHandling.Ignore)]
    public string Indicator { get; set; }

    [JsonProperty(PayUContainer.PropsName.Ui, NullValueHandling = NullValueHandling.Ignore)]
    public Ui Ui { get; set; }
}

public class Ui
{
    [JsonProperty(PayUContainer.PropsName.WindowSize, NullValueHandling = NullValueHandling.Ignore)]
    public string WindowSize { get; set; }
}

public class StoredCredentials
{
    [JsonProperty(PayUContainer.PropsName.ConsentType, NullValueHandling = NullValueHandling.Ignore)]
    public string ConsentType { get; set; }

    [JsonProperty(PayUContainer.PropsName.UseType, NullValueHandling = NullValueHandling.Ignore)]
    public string UseType { get; set; }

    [JsonProperty(PayUContainer.PropsName.UseId, NullValueHandling = NullValueHandling.Ignore)]
    public string UseId { get; set; }
}