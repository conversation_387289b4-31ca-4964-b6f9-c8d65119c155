using CoverGo.Payments.Application.Payments.Queries.GetTokenizedPaymentInitializationPublicFieldsQuery;
using MediatR;

namespace CoverGo.Payments.Api.Payments.GetPayments;

[QueryType]
public class TokenizedPaymentInitializationPublicFieldsQuery
{
    //[Error(typeof(DomainError))]
    //[Authorize]
    public async Task<IReadOnlyDictionary<string, string>> GetTokenizedPaymentInitializationPublicFields(
        string initializationToken,
        [Service] IMediator mediator,
        CancellationToken cancellationToken
    ) => await mediator.Send(new GetTokenizedPaymentInitializationPublicFieldsQuery(initializationToken),
        cancellationToken);
}