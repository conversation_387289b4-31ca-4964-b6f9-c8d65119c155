using CoverGo.Payments.Domain.BankAccount;
using CoverGo.Payments.Infrastructure.Indexing;
using MongoDB.Driver;

namespace CoverGo.Payments.Infrastructure.BankAccounts;

public class BankAccountIndexing(IMongoCollection<BankAccount> collection) : IIndexing
{
    public async Task CreateIndexesAsync()
    {
        // Create a compound index for common query patterns
        await CreateCompoundIndexAsync();
    }

    private async Task CreateCompoundIndexAsync()
    {
        // Use PascalCase field names to match MongoDB .NET driver default convention
        const string accountHolderNameField = nameof(BankAccount.AccountHolderName);
        const string bankNameField = nameof(BankAccount.BankName);
        
        var indexOptions = new CreateIndexOptions<BankAccount>
        {
            Name = "BankAccount_AccountHolderName_BankName_Compound",
            Background = true
        };

        // Create compound index - order matters for query performance
        // AccountHolderName first, then BankName (matching the query pattern)
        var indexDefinition = Builders<BankAccount>.IndexKeys
            .Ascending(accountHolderNameField)
            .Ascending(bankNameField);

        var indexModel = new CreateIndexModel<BankAccount>(
            indexDefinition,
            indexOptions);

        await collection.Indexes.CreateOneAsync(indexModel);
    }
}