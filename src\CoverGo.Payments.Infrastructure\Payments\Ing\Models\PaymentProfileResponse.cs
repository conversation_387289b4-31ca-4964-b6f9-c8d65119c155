﻿using Newtonsoft.Json;

namespace CoverGo.Payments.Infrastructure.Payments.ING.Models;

public class PaymentProfileResponse
{
    [JsonProperty(IngContainer.PropsName.PaymentProfiles)]
    public PaymentProfile[] PaymentProfiles { get; set; }
}

public class PaymentProfile
{
    [JsonProperty(IngContainer.PropsName.Id)]
    public string Id { get; set; }
    
    [JsonProperty(IngContainer.PropsName.FirstName)]
    public string FirstName { get; set; }
    
    [JsonProperty(IngContainer.PropsName.LastName)]
    public string LastName { get; set; }
    
    [JsonProperty(IngContainer.PropsName.MaskedNumber)]
    public string MaskedNumber { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Month)]
    public string Month { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Year)]
    public string Year { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Organization)]
    public string Organization { get; set; }
    
    [JsonProperty(IngContainer.PropsName.IsActive)]
    public int IsActive { get; set; }
    
    [JsonProperty(IngContainer.PropsName.Profile)]
    public string Profile { get; set; }
    
    [JsonProperty(IngContainer.PropsName.MerchantMid)]
    public string MerchantMid { get; set; }

    [JsonProperty(IngContainer.PropsName.MerchantCustomerId)]
    public string MerchantCustomerId { get; set; }
}