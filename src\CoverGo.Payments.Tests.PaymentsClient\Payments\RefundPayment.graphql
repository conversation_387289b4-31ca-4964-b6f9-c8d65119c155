mutation RefundPayment($input: RefundPaymentInput!) {
    refundPayment(input: $input) {
        payment {
            id
            paymentId
            status
            money {
                paymentCurrencyCode
                paymentCurrencyDesc
                paymentDecimalPrecision
                paymentAmount
            }
            createdAtDateUtc
            providerPaymentId
        }
        errors {
            ... on InputDataValidationError {
                message
                code
                errors {
                    propertyPath
                    message
                    code
                }
            }
            ... on DomainError {
                message
                code
            }
        }
    }
}