﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Queries;
using CoverGo.Payments.Domain.Payment;
using MongoDB.Driver;
using MongoDB.Driver.Linq;

namespace CoverGo.Payments.Application.Payments.Queries.PaymentQuery;

public class PaymentQueryHandler(IMongoCollection<PaymentAggregate> mongoCollection)
    : IQueryHandler<PaymentQuery, PaymentAggregate>
{
    public Task<PaymentAggregate> Handle(PaymentQuery request, CancellationToken cancellationToken) =>
        mongoCollection
            .AsQueryable()
            .Where(it => it.Id == request.Id.Value && it.EntityAuditInfo.DeletedAt == null)
            .SingleAsync(cancellationToken);
}