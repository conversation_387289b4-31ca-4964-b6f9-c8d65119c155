﻿namespace CoverGo.Payments.Domain.Helpers;

public static class <PERSON><PERSON><PERSON>cy<PERSON><PERSON><PERSON>
{
    private static readonly Dictionary<string, string> CurrencyToIsoCodeMap = new(StringComparer.OrdinalIgnoreCase)
    {
        { "AED", "784" }, // UAE Dirham
        { "AFN", "971" }, // Afghan Afghani
        { "ALL", "008" }, // Albanian Lek
        { "AMD", "051" }, // Armenian Dram
        { "ANG", "532" }, // Netherlands Antillean Guilder
        { "AOA", "973" }, // Angolan Kwanza
        { "ARS", "032" }, // Argentine Peso
        { "AUD", "036" }, // Australian Dollar
        { "AWG", "533" }, // Aruban Florin
        { "AZN", "944" }, // Azerbaijani Manat
        { "BAM", "977" }, // Bosnia and Herzegovina Convertible Mark
        { "BBD", "052" }, // Barbadian Dollar
        { "BDT", "050" }, // Bangladeshi Taka
        { "BGN", "975" }, // Bulgarian Lev
        { "BHD", "048" }, // <PERSON><PERSON>
        { "BIF", "108" }, // Burundian Franc
        { "BMD", "060" }, // Bermudian Dollar
        { "BND", "096" }, // Brunei Dollar
        { "BOB", "068" }, // Bolivian Boliviano
        { "BRL", "986" }, // Brazilian Real
        { "BSD", "044" }, // Bahamian Dollar
        { "BTN", "064" }, // Bhutanese Ngultrum
        { "BWP", "072" }, // Botswana Pula
        { "BYN", "933" }, // Belarusian Ruble
        { "BZD", "084" }, // Belize Dollar
        { "CAD", "124" }, // Canadian Dollar
        { "CDF", "976" }, // Congolese Franc
        { "CHF", "756" }, // Swiss Franc
        { "CLP", "152" }, // Chilean Peso
        { "CNY", "156" }, // Yuan Renminbi
        { "COP", "170" }, // Colombian Peso
        { "CRC", "188" }, // Costa Rican Colón
        { "CUP", "192" }, // Cuban Peso
        { "CVE", "132" }, // Cape Verdean Escudo
        { "CZK", "203" }, // Czech Koruna
        { "DJF", "262" }, // Djiboutian Franc
        { "DKK", "208" }, // Danish Krone
        { "DOP", "214" }, // Dominican Peso
        { "DZD", "012" }, // Algerian Dinar
        { "EGP", "818" }, // Egyptian Pound
        { "ERN", "232" }, // Eritrean Nakfa
        { "ETB", "230" }, // Ethiopian Birr
        { "EUR", "978" }, // Euro
        { "FJD", "242" }, // Fijian Dollar
        { "FKP", "238" }, // Falkland Islands Pound
        { "FOK", "234" }, // Faroese Króna
        { "GBP", "826" }, // Pound Sterling
        { "GEL", "981" }, // Georgian Lari
        { "GGP", "831" }, // Guernsey Pound
        { "GHS", "936" }, // Ghanaian Cedi
        { "GIP", "292" }, // Gibraltar Pound
        { "GMD", "270" }, // Gambian Dalasi
        { "GNF", "324" }, // Guinean Franc
        { "GTQ", "320" }, // Guatemalan Quetzal
        { "GYD", "328" }, // Guyanese Dollar
        { "HKD", "344" }, // Hong Kong Dollar
        { "HNL", "340" }, // Honduran Lempira
        { "HRK", "191" }, // Croatian Kuna
        { "HTG", "332" }, // Haitian Gourde
        { "HUF", "348" }, // Hungarian Forint
        { "IDR", "360" }, // Indonesian Rupiah
        { "ILS", "376" }, // Israeli New Shekel
        { "IMP", "833" }, // Isle of Man Pound
        { "INR", "356" }, // Indian Rupee
        { "IQD", "368" }, // Iraqi Dinar
        { "IRR", "364" }, // Iranian Rial
        { "ISK", "352" }, // Icelandic Króna
        { "JEP", "832" }, // Jersey Pound
        { "JMD", "388" }, // Jamaican Dollar
        { "JOD", "400" }, // Jordanian Dinar
        { "JPY", "392" }, // Yen
        { "KES", "404" }, // Kenyan Shilling
        { "KGS", "417" }, // Kyrgyzstani Som
        { "KHR", "116" }, // Cambodian Riel
        { "KMF", "174" }, // Comorian Franc
        { "KRW", "410" }, // South Korean Won
        { "KWD", "414" }, // Kuwaiti Dinar
        { "KYD", "136" }, // Cayman Islands Dollar
        { "KZT", "398" }, // Kazakhstani Tenge
        { "LAK", "418" }, // Lao Kip
        { "LBP", "422" }, // Lebanese Pound
        { "LKR", "144" }, // Sri Lankan Rupee
        { "LRD", "430" }, // Liberian Dollar
        { "LSL", "426" }, // Lesotho Loti
        { "LYD", "434" }, // Libyan Dinar
        { "MAD", "504" }, // Moroccan Dirham
        { "MDL", "498" }, // Moldovan Leu
        { "MGA", "969" }, // Malagasy Ariary
        { "MKD", "807" }, // Macedonian Denar
        { "MMK", "104" }, // Myanmar Kyat
        { "MNT", "496" }, // Mongolian Tögrög
        { "MOP", "446" }, // Macanese Pataca
        { "MRU", "929" }, // Mauritanian Ouguiya
        { "MUR", "480" }, // Mauritian Rupee
        { "MVR", "462" }, // Maldivian Rufiyaa
        { "MWK", "454" }, // Malawian Kwacha
        { "MXN", "484" }, // Mexican Peso
        { "MYR", "458" }, // Malaysian Ringgit
        { "MZN", "943" }, // Mozambican Metical
        { "NAD", "516" }, // Namibian Dollar
        { "NGN", "566" }, // Nigerian Naira
        { "NIO", "558" }, // Nicaraguan Córdoba
        { "NOK", "578" }, // Norwegian Krone
        { "NPR", "524" }, // Nepalese Rupee
        { "NZD", "554" }, // New Zealand Dollar
        { "OMR", "512" }, // Omani Rial
        { "PAB", "590" }, // Panamanian Balboa
        { "PEN", "604" }, // Peruvian Sol
        { "PGK", "598" }, // Papua New Guinean Kina
        { "PHP", "608" }, // Philippine Peso
        { "PKR", "586" }, // Pakistani Rupee
        { "PLN", "985" }, // Polish Złoty
        { "PYG", "600" }, // Paraguayan Guaraní
        { "QAR", "634" }, // Qatari Riyal
        { "RON", "946" }, // Romanian Leu
        { "RSD", "941" }, // Serbian Dinar
        { "RUB", "643" }, // Russian Ruble
        { "RWF", "646" }, // Rwandan Franc
        { "SAR", "682" }, // Saudi Riyal
        { "SBD", "090" }, // Solomon Islands Dollar
        { "SCR", "690" }, // Seychellois Rupee
        { "SDG", "938" }, // Sudanese Pound
        { "SEK", "752" }, // Swedish Krona
        { "SGD", "702" }, // Singapore Dollar
        { "SHP", "654" }, // Saint Helena Pound
        { "SLL", "694" }, // Sierra Leonean Leone
        { "SOS", "706" }, // Somali Shilling
        { "SRD", "968" }, // Surinamese Dollar
        { "SSP", "728" }, // South Sudanese Pound
        { "STN", "930" }, // São Tomé and Príncipe Dobra
        { "SYP", "760" }, // Syrian Pound
        { "SZL", "748" }, // Eswatini Lilangeni
        { "THB", "764" }, // Thai Baht
        { "TJS", "972" }, // Tajikistani Somoni
        { "TMT", "934" }, // Turkmenistan Manat
        { "TND", "788" }, // Tunisian Dinar
        { "TOP", "776" }, // Tongan Paʻanga
        { "TRY", "949" }, // Turkish Lira
        { "TTD", "780" }, // Trinidad and Tobago Dollar
        { "TWD", "901" }, // New Taiwan Dollar
        { "TZS", "834" }, // Tanzanian Shilling
        { "UAH", "980" }, // Ukrainian Hryvnia
        { "UGX", "800" }, // Ugandan Shilling
        { "USD", "840" }, // US Dollar
        { "UYU", "858" }, // Uruguayan Peso
        { "UZS", "860" }, // Uzbekistani Soʻm
        { "VES", "928" }, // Venezuelan Bolívar Soberano
        { "VND", "704" }, // Vietnamese Đồng
        { "VUV", "548" }, // Vanuatu Vatu
        { "WST", "882" }, // Samoan Tālā
        { "XAF", "950" }, // Central African CFA Franc
        { "XCD", "951" }, // East Caribbean Dollar
        { "XOF", "952" }, // West African CFA Franc
        { "XPF", "953" }, // CFP Franc
        { "YER", "886" }, // Yemeni Rial
        { "ZAR", "710" }, // South African Rand
        { "ZMW", "967" }, // Zambian Kwacha
        { "ZWL", "932" }, // Zimbabwean Dollar
    };

    private static readonly Dictionary<string, string> IsoCodeToCurrencyMap = CurrencyToIsoCodeMap
        .ToDictionary(kvp => kvp.Value, kvp => kvp.Key, StringComparer.OrdinalIgnoreCase);

    public static decimal? ConvertSubunitsToUnits(long? subunits) => subunits / 100m;

    public static decimal? ConvertSubunitsToUnits(decimal? subunits) => subunits / 100m;

    public static long ConvertUnitsToSubunits(decimal units) => (long)(units * 100);

    public static string GetIsoCode(string currency)
    {
        if (CurrencyToIsoCodeMap.TryGetValue(currency, out string? isoCode))
        {
            return isoCode;
        }

        throw new ArgumentException($"Unsupported currency: {currency}");
    }

    public static string GetCurrency(string isoCode)
    {
        if (IsoCodeToCurrencyMap.TryGetValue(isoCode, out string? currency))
        {
            return currency;
        }

        throw new ArgumentException($"Unsupported ISO code: {isoCode}");
    }
}