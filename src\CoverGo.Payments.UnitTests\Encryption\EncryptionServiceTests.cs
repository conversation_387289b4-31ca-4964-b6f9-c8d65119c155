﻿using CoverGo.Payments.Infrastructure.Encryption.Symmetric;
using FluentAssertions;
using System.Security.Cryptography;

namespace CoverGo.Payments.UnitTests.Encryption
{
    public class EncryptionServiceTests
    {
        private readonly string _key = Convert.ToBase64String(Aes.Create().Key);
        private readonly string _iv = Convert.ToBase64String(Aes.Create().IV);
        private readonly int _keySize = 256;
        private readonly CipherMode _cipherMode = CipherMode.CBC;
        private readonly PaddingMode _paddingMode = PaddingMode.PKCS7;

        [Fact]
        public void Given_ValidPlainText_When_Encrypt_Then_ReturnsEncryptedText()
        {
            // Arrange
            var service = new AesEncryptionService(_key, _iv, _keySize, _cipherMode, _paddingMode);
            string plainText = "Hello, World!";

            // Act
            string encryptedText = service.Encrypt(plainText);

            // Assert
            encryptedText.Should().NotBe(plainText);
            encryptedText.Should().NotBeNullOrWhiteSpace();
        }

        [Fact]
        public void Given_ValidCipherText_When_Decrypt_Then_ReturnsPlainText()
        {
            // Arrange
            var service = new AesEncryptionService(_key, _iv, _keySize, _cipherMode, _paddingMode);
            string plainText = "Hello, World!";
            string encryptedText = service.Encrypt(plainText);

            // Act
            string decryptedText = service.Decrypt(encryptedText);

            // Assert
            decryptedText.Should().Be(plainText);
        }

        [Fact]
        public void Given_EmptyPlainText_When_Encrypt_Then_ThrowsArgumentException()
        {
            // Arrange
            var service = new AesEncryptionService(_key, _iv, _keySize, _cipherMode, _paddingMode);

            // Act & Assert
            Action act = () => service.Encrypt(string.Empty);
            act.Should().Throw<ArgumentException>();
        }

        [Fact]
        public void Given_EmptyCipherText_When_Decrypt_Then_ThrowsArgumentException()
        {
            // Arrange
            var service = new AesEncryptionService(_key, _iv, _keySize, _cipherMode, _paddingMode);

            // Act & Assert
            Action act = () => service.Decrypt(string.Empty);
            act.Should().Throw<ArgumentException>();
        }
    }
}
