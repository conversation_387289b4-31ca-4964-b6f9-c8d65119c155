﻿using CoverGo.BuildingBlocks.Domain.Core.DomainEvents;
using CoverGo.Payments.Integration.Events;

namespace CoverGo.Payments.Domain.Payment.DomainEvents;

public abstract record PaymentDomainEventBase
    : IDomainEvent
{
    public required string PolicyId { get; init; }

    public string? InvoiceNumber { get; init; }
    
    public string? PaymentMethod { get; init; }

    public required string PayorId { get; init; }

    public required string Status { get; init; }
    
    public required string Type { get; init; }
    
    public required string ReferenceId { get; init; }
    
    public required DateTime EffectiveDate { get; init; }

    public required Money Money { get; init; }
    
    public CreditCard? CreditCard { get; init; }

    public PaymentProviderInfo? PaymentProviderInfo { get; init; }

    public PaymentBankInfo? PaymentBankInfo { get; init; }
}