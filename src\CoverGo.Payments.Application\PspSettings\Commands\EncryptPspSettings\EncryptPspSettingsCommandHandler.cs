﻿using CoverGo.BuildingBlocks.Application.Core.CQS.Commands;
using CoverGo.BuildingBlocks.Application.Core.Ports.DataAccess;
using CoverGo.Payments.Application.PspSettings.Commands.CreatePspSettings;
using CoverGo.Payments.Domain.Exceptions;
using CoverGo.Payments.Domain.PspSettings;
using Microsoft.Extensions.Logging;

namespace CoverGo.Payments.Application.PspSettings.Commands.EncryptPspSettings
{
    public class EncryptPspSettingsCommandHandler(
            IRepository<PspSettingsAggregate, string> pspSettingsRepository,
            ILogger<CreatePspSettingsCommandHandler> logger)
            : ICommandHandler<EncryptPspSettingsCommand, string>
    {
        public async Task<string> Handle(EncryptPspSettingsCommand request, CancellationToken cancellationToken)
        {
            logger.LogInformation("Handling EncryptPspSettingsCommand");

            try
            {
                IEnumerable<PspSettingsAggregate> pspSettings = await pspSettingsRepository.FindAllByAsync(psp => true, cancellationToken);
                if (request.PaymentProvider != null)
                {
                    pspSettings = pspSettings.Where(psp => psp.PaymentProvider == request.PaymentProvider);
                }
                foreach (var pspSetting in pspSettings)
                {
                    await pspSettingsRepository.UpdateAsync(pspSetting, cancellationToken);
                }
            }
            catch (Exception ex) 
            {
                logger.LogError(ex, "Error while encrypting PSP settings.");
                throw new DomainException("Error while encrypting PSP settings.", ex);
            }

            return "SUCCESS";
        }
    }
}
